class ApiConfig {
  static const signup = '/auth/v1/signup';
  static const login = '/auth/v1/token?grant_type=password';
  static const forgotPassword = '/auth/v1/forgot-password';
  static const updatePartyInfoEndpoint = '/rest/v1/rpc/fn_update_party_info';
  static const getUserInfo = 'fn_get_user_info';
  static const listAllAssignedGames = 'fn_list_all_assigned_games';
  //static const listAllAssignedGames = 'fn_list_all_approved_games';
  static const listGameCategory = 'v_list_game_category';
  static const gameSummaryStats = 'fn_get_game_summary_stats';
  static const getGameConfig = '/rest/v1/rpc/fn_get_game_config';
  static const startGame = 'fn_start_game_session';
  static const endGame = "fn_end_game_session";
  static const getActiveSession = "fn_get_active_session";
  static const requestGameAccess = "fn_request_game_access";
  static const getProfileDetails = 'get_profile_details';
  static const updateProfileDetails = 'fn_update_profile_info';
  static const updateUserMetadata = 'fn_update_user_metadata';
  static const getRecentActivities = 'fn_get_recent_activities';
}

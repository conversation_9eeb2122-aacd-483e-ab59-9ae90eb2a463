import 'package:go_router/go_router.dart';
import 'package:recallloop/features/exercises/presentation/pages/sequence_memory_page.dart';
import 'package:recallloop/features/exercises/presentation/pages/visual_memory_page.dart';
import 'package:recallloop/features/profile/domain/entities/user_profile.dart';
import 'package:recallloop/features/profile/presentation/pages/edit_profile_page.dart';
import 'package:recallloop/features/recentactivities/presentation/pages/recentactivities_page.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/logout_page.dart';
import '../../features/auth/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/signup_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/exercises/presentation/pages/exercises_page.dart';
import '../../features/exercises/presentation/pages/number_sequence_page.dart';
import '../../features/exercises/presentation/pages/object_memory_page.dart';
import '../../features/exercises/presentation/pages/pattern_recognition_page.dart';
import '../../features/exercises/presentation/pages/shape_matching_page.dart';
import '../../features/exercises/presentation/pages/word_recall_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/mood/presentation/pages/mood_tracking_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/progress/presentation/pages/progress_page.dart';
import '../../shared/presentation/pages/main_navigation_page.dart';

class AppRouter {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String home = '/home';
  static const String exercises = '/exercises';
  static const String recentactivities = '/recentactivities';
  static const String wordRecall = '/exercises/word-recall';
  static const String numberSequence = '/exercises/number-sequence';
  static const String shapeMatching = '/exercises/shape-matching';
  static const String patternRecognition = '/exercises/pattern-recognition';
  static const String objectMemory = '/exercises/object-memory';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String moodTracking = '/mood';
  static const String progress = '/progress';
  static const String visualMemory = '/exercises/visual-memory';
  static const String sequenceMemory = '/exercises/sequence-memory';
  static const String logout = '/logout';
  static final GoRouter router = GoRouter(
    initialLocation: splash,
    routes: [
      // Splash Screen Route
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: signup,
        name: 'signup',
        builder: (context, state) => const SignupPage(),
      ),

      // Main App Routes with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainNavigationPage(child: child),
        routes: [
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) => const HomePageWrapper(),
          ),
          GoRoute(
            path: exercises,
            name: 'exercises',
            builder: (context, state) => const ExercisesPage(),
          ),
          // GoRoute(
          //   path: profile,
          //   name: 'profile',
          //   builder: (context, state) => const ProfilePage(),
          // ),
          GoRoute(
            path: profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
            routes: [
              // Add nested route for edit profile
              GoRoute(
                path: 'edit',
                name: 'editProfile',
                builder: (context, state) {
                  final profile = state.extra as UserProfile?;
                  if (profile == null) {
                    // If no profile passed, navigate back to profile
                    return const ProfilePage();
                  }
                  return EditProfilePage(profile: profile);
                },
              ),
            ],
          ),
          GoRoute(
            path: moodTracking,
            name: 'mood',
            builder: (context, state) => const MoodTrackingPage(),
          ),
          GoRoute(
            path: progress,
            name: 'progress',
            builder: (context, state) => const ProgressPage(),
          ),
          GoRoute(
            path: recentactivities,
            name: 'recentactivities',
            builder: (context, state) => const RecentActivitiesPage(),
          ),
          GoRoute(
            path: logout,
            name: 'logout',
            builder: (context, state) => const LogoutPage(),
          ),
        ],
      ),

      // Exercise Detail Routes (outside shell for full-screen experience)
      GoRoute(
        path: wordRecall,
        name: 'word-recall',
        builder: (context, state) =>
            WordRecallPage(key: state.pageKey, extra: state.extra),
      ),
      GoRoute(
        path: numberSequence,
        name: 'number-sequence',
        builder: (context, state) =>
            NumberSequencePage(key: state.pageKey, extra: state.extra),
      ),
      GoRoute(
        path: shapeMatching,
        name: 'shape-matching',
        builder: (context, state) =>
            ShapeMatchingPage(key: state.pageKey, extra: state.extra),
      ),
      GoRoute(
        path: patternRecognition,
        name: 'pattern-recognition',
        builder: (context, state) {
          final extra = state.extra;
          return PatternRecognitionPage(key: state.pageKey, extra: extra);
        },
      ),
      GoRoute(
        path: objectMemory,
        name: 'object-memory',
        builder: (context, state) =>
            ObjectMemoryPage(key: state.pageKey, extra: state.extra),
      ),
      GoRoute(
        path: visualMemory,
        name: 'visual-memory',
        builder: (context, state) =>
            VisualMemoryPage(key: state.pageKey, extra: state.extra),
      ),
      GoRoute(
        path: sequenceMemory,
        name: 'sequence-memory',
        builder: (context, state) =>
            SequenceMemoryPage(key: state.pageKey, extra: state.extra),
      ),
    ],
  );
}

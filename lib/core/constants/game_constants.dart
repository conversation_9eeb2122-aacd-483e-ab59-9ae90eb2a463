class GameConstants {
  // Number Memory Game Data
  static const Map<String, List<Map<String, dynamic>>> numberMemoryData = {
    "easy": [
      {"numbers": "385"},
      {"numbers": "6214"},
      {"numbers": "90372"},
      {"numbers": "147"},
      {"numbers": "5829"},
      {"numbers": "36741"},
    ],
    "medium": [
      {"numbers": "58192"},
      {"numbers": "720463"},
      {"numbers": "4917382"},
      {"numbers": "83756"},
      {"numbers": "294817"},
      {"numbers": "6385947"},
    ],
    "hard": [
      {"numbers": "8204617"},
      {"numbers": "61938274"},
      {"numbers": "302819475"},
      {"numbers": "75849362"},
      {"numbers": "194736582"},
      {"numbers": "8472639158"},
    ],
    "expert": [
      {"numbers": "39485726174"},
      {"numbers": "847293651847"},
      {"numbers": "192847365928"},
      {"numbers": "7394856271849"},
      {"numbers": "58392847561938"},
      {"numbers": "847293651847362"},
    ],
  };

  // Enhanced Number Memory Pattern Data
  static const Map<String, List<Map<String, dynamic>>> numberMemoryPatterns = {
    "easy": [
      {
        "pattern": [1, 2, 3, 4, "?"],
        "options": [5, 6, 7, 8],
        "answer": 5,
        "explanation": "Sequential counting: 1, 2, 3, 4, 5",
      },
      {
        "pattern": [2, 4, 6, 8, "?"],
        "options": [10, 12, 9, 11],
        "answer": 10,
        "explanation": "Even numbers: 2, 4, 6, 8, 10",
      },
      {
        "pattern": [5, 10, 15, 20, "?"],
        "options": [25, 30, 35, 40],
        "answer": 25,
        "explanation": "Multiples of 5: 5, 10, 15, 20, 25",
      },
    ],
    "medium": [
      {
        "pattern": [1, 4, 9, 16, "?"],
        "options": [25, 20, 24, 30],
        "answer": 25,
        "explanation": "Perfect squares: 1², 2², 3², 4², 5²",
      },
      {
        "pattern": [2, 6, 18, 54, "?"],
        "options": [162, 108, 216, 180],
        "answer": 162,
        "explanation":
            "Multiply by 3 each time: 2×3=6, 6×3=18, 18×3=54, 54×3=162",
      },
      {
        "pattern": [1, 1, 2, 3, 5, "?"],
        "options": [8, 7, 9, 6],
        "answer": 8,
        "explanation": "Fibonacci sequence: 1+1=2, 1+2=3, 2+3=5, 3+5=8",
      },
    ],
    "hard": [
      {
        "pattern": [3, 7, 15, 31, "?"],
        "options": [63, 62, 64, 65],
        "answer": 63,
        "explanation": "Pattern: 2ⁿ⁺¹ - 1 where n starts from 2",
      },
      {
        "pattern": [1, 8, 27, 64, "?"],
        "options": [125, 100, 120, 130],
        "answer": 125,
        "explanation": "Perfect cubes: 1³, 2³, 3³, 4³, 5³",
      },
      {
        "pattern": [2, 3, 5, 8, 13, "?"],
        "options": [21, 20, 18, 19],
        "answer": 21,
        "explanation":
            "Fibonacci starting with 2, 3: 2+3=5, 3+5=8, 5+8=13, 8+13=21",
      },
    ],
    "expert": [
      {
        "pattern": [1, 4, 13, 40, 121, "?"],
        "options": [364, 360, 365, 362],
        "answer": 364,
        "explanation": "Pattern: 3ⁿ + 1 where n starts from 0",
      },
      {
        "pattern": [7, 26, 63, 124, 215, "?"],
        "options": [342, 340, 344, 346],
        "answer": 342,
        "explanation": "Pattern: n³ + 6n where n starts from 1",
      },
    ],
  };

  // Visual Memory Game Data
  static const Map<String, Map<String, dynamic>> visualMemoryData = {
    "easy": {"gridSize": 2, "circles": 1, "peekTime": 5, "maxScore": 10},
    "medium": {"gridSize": 3, "circles": 2, "peekTime": 4, "maxScore": 15},
    "hard": {"gridSize": 3, "circles": 3, "peekTime": 3, "maxScore": 20},
    "expert": {"gridSize": 4, "circles": 4, "peekTime": 2, "maxScore": 25},
  };

  // Crossword game data
  static const Map<String, dynamic> crossword = {
    "levels": {
      "easy": [
        {
          "id": 1,
          "gridSize": 5,
          "words": [
            {
              "word": "CAT",
              "clue": "Pet that meows",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "CUP",
              "clue": "Drink from this",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
          ],
        },
        {
          "id": 2,
          "gridSize": 5,
          "words": [
            {
              "word": "DOG",
              "clue": "Pet that barks",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "DAY",
              "clue": "Opposite of night",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
          ],
        },
      ],
      "medium": [
        {
          "id": 3,
          "gridSize": 7,
          "words": [
            {
              "word": "BOOK",
              "clue": "Something you read",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "BIRD",
              "clue": "Animal that flies",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
            {
              "word": "DOOR",
              "clue": "You open this to enter",
              "direction": "across",
              "row": 4,
              "col": 2,
            },
          ],
        },
        {
          "id": 4,
          "gridSize": 7,
          "words": [
            {
              "word": "FISH",
              "clue": "Swims in water",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "FIRE",
              "clue": "Hot and burns",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
            {
              "word": "RICE",
              "clue": "White grain food",
              "direction": "across",
              "row": 4,
              "col": 2,
            },
          ],
        },
      ],
      "hard": [
        {
          "id": 5,
          "gridSize": 9,
          "words": [
            {
              "word": "COMPUTER",
              "clue": "Electronic device",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "CAMERA",
              "clue": "Takes pictures",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
            {
              "word": "MOUNTAIN",
              "clue": "High natural elevation",
              "direction": "across",
              "row": 4,
              "col": 2,
            },
          ],
        },
        {
          "id": 6,
          "gridSize": 9,
          "words": [
            {
              "word": "ELEPHANT",
              "clue": "Large gray animal",
              "direction": "across",
              "row": 1,
              "col": 1,
            },
            {
              "word": "ENVELOPE",
              "clue": "Contains a letter",
              "direction": "down",
              "row": 1,
              "col": 1,
            },
            {
              "word": "TELEPHONE",
              "clue": "Device for calling",
              "direction": "across",
              "row": 5,
              "col": 2,
            },
          ],
        },
      ],
    },
  };

  // Odd-One-Out by difficulty (for dynamic difficulty selection)
  static const Map<String, List<Map<String, dynamic>>> oddOneOutByDifficulty = {
    "easy": [
      {
        "question": "Which is the odd one out?",
        "options": ["Facebook", "Instagram", "Twitter", "Google"],
        "answer": "Google",
        "reason":
            "Google is primarily a search engine, while the others are social media platforms.",
      },
      {
        "question": "Pick the odd one out.",
        "options": ["Mercury", "Venus", "Earth", "Pluto"],
        "answer": "Pluto",
        "reason":
            "Pluto is classified as a dwarf planet, while the others are planets in the solar system.",
      },
      {
        "question": "Select the odd one.",
        "options": ["Apple", "Banana", "Carrot", "Grapes"],
        "answer": "Carrot",
        "reason": "Carrot is a vegetable; the others are fruits.",
      },
      {
        "question": "Which doesn't belong?",
        "options": ["Lion", "Tiger", "Leopard", "Eagle"],
        "answer": "Eagle",
        "reason": "Eagle is a bird; the others are big cats.",
      },
      {
        "question": "Find the odd item.",
        "options": ["Red", "Blue", "Green", "Circle"],
        "answer": "Circle",
        "reason": "Circle is a shape; the others are colors.",
      },
      {
        "question": "Choose the different one.",
        "options": ["Dog", "Cat", "Cow", "Shark"],
        "answer": "Shark",
        "reason": "Shark is a fish; the others are mammals.",
      },
      {
        "question": "Odd one out?",
        "options": ["Table", "Chair", "Sofa", "Pencil"],
        "answer": "Pencil",
        "reason": "Pencil is a stationery item; the others are furniture.",
      },
      {
        "question": "Spot the outlier.",
        "options": ["Monday", "Friday", "Sunday", "Winter"],
        "answer": "Winter",
        "reason": "Winter is a season; the others are days of the week.",
      },
    ],
    "medium": [
      {
        "question": "Odd one out?",
        "options": ["Gold", "Silver", "Iron", "Diamond"],
        "answer": "Diamond",
        "reason": "Diamond is not a metal; the others are metals.",
      },
      {
        "question": "Select the odd one.",
        "options": ["Oxygen", "Hydrogen", "Carbon", "Water"],
        "answer": "Water",
        "reason": "Water is a compound; the others are elements.",
      },
      {
        "question": "Which is different?",
        "options": ["Triangle", "Square", "Rectangle", "Sphere"],
        "answer": "Sphere",
        "reason": "Sphere is 3D; the others are 2D shapes.",
      },
      {
        "question": "Pick the odd one out.",
        "options": ["Copper", "Aluminum", "Plastic", "Zinc"],
        "answer": "Plastic",
        "reason": "Plastic is not a metal; the others are metals.",
      },
      {
        "question": "Find the outlier.",
        "options": ["Asia", "Africa", "Australia", "Sahara"],
        "answer": "Sahara",
        "reason": "Sahara is a desert; the others are continents.",
      },
      {
        "question": "Choose the unrelated item.",
        "options": ["Jupiter", "Saturn", "Neptune", "Sirius"],
        "answer": "Sirius",
        "reason": "Sirius is a star; the others are planets.",
      },
      {
        "question": "Spot the odd one.",
        "options": ["Kilogram", "Meter", "Liter", "Hour"],
        "answer": "Hour",
        "explanation":
            "Hour measures time; the others measure mass, length, or volume.",
      },
    ],
    "hard": [
      {
        "question": "Find the unrelated item.",
        "options": ["Shakespeare", "Hemingway", "Tolstoy", "Einstein"],
        "answer": "Einstein",
        "reason": "Einstein was a scientist; the others were famous writers.",
      },
      {
        "question": "Choose the outlier.",
        "options": ["Asia", "Europe", "Africa", "Arctic"],
        "answer": "Arctic",
        "reason": "Arctic is not a continent; the others are continents.",
      },
      {
        "question": "Which doesn't belong?",
        "options": ["Photosynthesis", "Mitosis", "Evaporation", "Osmosis"],
        "answer": "Evaporation",
        "reason":
            "Evaporation is a physical process; others are biological processes.",
      },
      {
        "question": "Pick the different concept.",
        "options": [
          "Relativity",
          "Quantum Mechanics",
          "Evolution",
          "Thermodynamics",
        ],
        "answer": "Evolution",
        "reason":
            "Evolution is a biological theory; others are physics theories.",
      },
      {
        "question": "Identify the outlier.",
        "options": ["Python", "Java", "Ruby", "HTML"],
        "answer": "HTML",
        "reason":
            "HTML is a markup language; others are programming languages.",
      },
      {
        "question": "Find the odd scientific field.",
        "options": ["Astrophysics", "Neuroscience", "Geology", "Cubism"],
        "answer": "Cubism",
        "reason": "Cubism is an art movement; others are sciences.",
      },
      {
        "question": "Spot the different one.",
        "options": ["Heisenberg", "Bohr", "Curie", "Picasso"],
        "answer": "Picasso",
        "reason": "Picasso was an artist; the others were scientists.",
      },
    ],
  };

  // Visual Memory Games List
  static const List<Map<String, dynamic>> visualMemoryGames = [
    {
      "id": "visual_memory_basic",
      "name": "Basic Visual Memory",
      "description": "Remember the positions of colored circles",
      "difficulty": "medium",
      "type": "visual_memory",
    },
    {
      "id": "visual_memory_shapes",
      "name": "Shape Memory",
      "description": "Remember the positions of different shapes",
      "difficulty": "hard",
      "type": "shape_memory",
    },
    {
      "id": "visual_memory_colors",
      "name": "Color Sequence Memory",
      "description": "Remember the sequence of colors",
      "difficulty": "medium",
      "type": "color_sequence",
    },
    {
      "id": "visual_memory_patterns",
      "name": "Pattern Memory",
      "description": "Remember complex visual patterns",
      "difficulty": "expert",
      "type": "pattern_memory",
    },
    {
      "id": "visual_memory_objects",
      "name": "Object Memory",
      "description": "Remember the positions of different objects",
      "difficulty": "hard",
      "type": "object_memory",
    },
    {
      "id": "visual_memory_emojis",
      "name": "Emoji Memory",
      "description": "Remember and tap the positions of emojis",
      "difficulty": "easy",
      "type": "emoji_memory",
    },
    {
      "id": "visual_memory_animals",
      "name": "Animal Footprints",
      "description": "Track animal footprints patterns",
      "difficulty": "medium",
      "type": "footprint_memory",
    },
    {
      "id": "visual_memory_faces",
      "name": "Funny Faces",
      "description": "Remember faces and expressions",
      "difficulty": "hard",
      "type": "face_memory",
    },
    {
      "id": "visual_memory_flags",
      "name": "Flag Finder",
      "description": "Remember positions of country flags",
      "difficulty": "medium",
      "type": "flag_memory",
    },
    {
      "id": "visual_memory_pixel_art",
      "name": "Pixel Art Memory",
      "description": "Recreate tiny pixel art patterns",
      "difficulty": "hard",
      "type": "pixel_memory",
    },
    {
      "id": "visual_memory_traffic",
      "name": "Traffic Lights",
      "description": "Follow light sequences",
      "difficulty": "easy",
      "type": "traffic_sequence",
    },
    {
      "id": "visual_memory_fruits",
      "name": "Fruit Basket",
      "description": "Remember shuffled fruits",
      "difficulty": "easy",
      "type": "fruit_memory",
    },
    {
      "id": "visual_memory_aliens",
      "name": "Alien Symbols",
      "description": "Decode and remember alien symbols",
      "difficulty": "expert",
      "type": "alien_symbol_memory",
    },
    {
      "id": "visual_memory_balloons",
      "name": "Pop the Balloons",
      "description": "Remember positions of colored balloons",
      "difficulty": "easy",
      "type": "balloon_memory",
    },
    {
      "id": "visual_memory_shadow",
      "name": "Shadow Match",
      "description": "Match objects to their shadows",
      "difficulty": "medium",
      "type": "shadow_memory",
    },
    {
      "id": "visual_memory_flip_match",
      "name": "Flip & Match",
      "description": "Match pairs of items by flipping cards",
      "difficulty": "medium",
      "type": "flip_match",
    },
    {
      "id": "number_memory_basic",
      "name": "Number Memory",
      "description": "Remember sequences of numbers",
      "difficulty": "easy",
      "type": "number_memory",
    },
    {
      "id": "number_memory_patterns",
      "name": "Number Patterns",
      "description": "Complete number sequence patterns",
      "difficulty": "medium",
      "type": "number_pattern",
    },
    {
      "id": "number_memory_advanced",
      "name": "Advanced Number Memory",
      "description": "Remember complex number sequences",
      "difficulty": "hard",
      "type": "number_memory_advanced",
    },
    {
      "id": "color_pattern_memory",
      "name": "Color Pattern Memory",
      "description": "Complete color sequence patterns",
      "difficulty": "medium",
      "type": "color_pattern",
    },
  ];

  // Shape Memory Data
  static const List<String> shapeTypes = [
    '⚪', // circle
    '⬛', // square
    '🔺', // triangle
    '💎', // diamond
    '⭐', // star
    '❤️', // heart
    '⬢', // hexagon
    '🔷',
  ];

  // Object Memory Data
  static const List<String> objectTypes = [
    'apple',
    'book',
    'car',
    'house',
    'tree',
    'star',
    'moon',
    'sun',
    'flower',
    'bird',
  ];

  // Emoji Memory Data
  static const List<String> emojiTypes = [
    '😀',
    '😍',
    '🎉',
    '🌟',
    '🎨',
    '🎭',
    '🎪',
    '🎯',
    '🎲',
    '🎮',
    '🏆',
    '💎',
    '🔥',
    '💫',
    '⭐',
    '🌈',
    '🍕',
    '🍦',
    '🍭',
    '🍩',
  ];

  // Funny Face Data
  static const List<String> faceTypes = [
    '😀',
    '😃',
    '😄',
    '😁',
    '😆',
    '😅',
    '😂',
    '🤣',
    '😊',
    '😇',
    '🙂',
    '🙃',
    '😉',
    '😌',
    '😍',
    '🥰',
    '😘',
    '😗',
    '😙',
    '😚',
  ];

  // Flag Data
  static const List<String> flagTypes = [
    '🇺🇸',
    '🇬🇧',
    '🇫🇷',
    '🇩🇪',
    '🇮🇹',
    '🇪🇸',
    '🇯🇵',
    '🇨🇦',
    '🇦🇺',
    '🇧🇷',
    '🇮🇳',
    '🇨🇳',
    '🇰🇷',
    '🇷🇺',
    '🇿🇦',
    '🇪🇬',
    '🇲🇽',
    '🇦🇷',
    '🇨🇱',
    '🇵🇪',
  ];

  // Fruit Data
  static const List<String> fruitTypes = [
    '🍎',
    '🍌',
    '🍊',
    '🍇',
    '🍓',
    '🍑',
    '🍍',
    '🥭',
    '🥝',
    '🍒',
    '🍐',
    '🥑',
    '🍅',
    '🥕',
    '🥬',
    '🥒',
    '🌽',
    '🥔',
    '🍠',
    '🥜',
  ];

  // Traffic Light Data
  static const List<String> trafficTypes = [
    '🔴',
    '🟡',
    '🟢',
    '🔴',
    '🟡',
    '🟢',
    '🔴',
    '🟡',
    '🟢',
    '🔴',
  ];

  // Alien Symbol Data
  static const List<String> alienSymbolTypes = [
    '👽',
    '🛸',
    '🌟',
    '💫',
    '⭐',
    '🔮',
    '⚡',
    '💎',
    '🔥',
    '🌈',
  ];

  // Shadow Match Data
  static const List<String> shadowTypes = [
    '🐕',
    '🐈',
    '🐦',
    '🐟',
    '🐢',
    '🐸',
    '🐙',
    '🦋',
    '🐞',
    '🦄',
  ];

  // Pixel Art Patterns (simplified as emoji patterns)
  static const List<List<String>> pixelArtPatterns = [
    ['⬛', '⬜', '⬛', '⬜', '⬛', '⬜', '⬛', '⬜', '⬛'],
    ['⬜', '⬛', '⬜', '⬛', '⬜', '⬛', '⬜', '⬛', '⬜'],
    ['⬛', '⬛', '⬛', '⬜', '⬜', '⬜', '⬛', '⬛', '⬛'],
    ['⬜', '⬜', '⬜', '⬛', '⬛', '⬛', '⬜', '⬜', '⬜'],
  ];

  // Circle colors for visual memory game
  static const List<String> circleColors = [
    '#FF6B6B', // Red
    '#4ECDC4', // Teal
    '#45B7D1', // Blue
    '#96CEB4', // Green
    '#FFEAA7', // Yellow
    '#DDA0DD', // Plum
    '#FFB347', // Orange
    '#98D8C8', // Mint
    '#F7DC6F', // Gold
  ];

  // Word Recall Data
  static const Map<String, List<String>> wordRecallData = {
    "easy": [
      "cat",
      "dog",
      "sun",
      "car",
      "book",
      "tree",
      "ball",
      "fish",
      "bird",
      "cake",
      "door",
      "hand",
      "moon",
      "star",
      "fire",
      "love",
      "home",
      "blue",
      "red",
      "big",
      "run",
      "jump",
      "walk",
      "talk",
      "play",
      "work",
      "food",
      "milk",
      "egg",
      "cup",
      "pen",
      "box",
      "hat",
      "bag",
      "key",
      "bed",
      "eye",
      "ear",
      "arm",
      "leg",
      "hot",
      "cold",
      "new",
      "old",
      "good",
      "bad",
      "yes",
      "no",
      "up",
      "down",
    ],
    "medium": [
      "apple",
      "banana",
      "orange",
      "grape",
      "lemon",
      "peach",
      "pear",
      "plum",
      "cherry",
      "strawberry",
      "blueberry",
      "raspberry",
      "blackberry",
      "watermelon",
      "cantaloupe",
      "honeydew",
      "pineapple",
      "mango",
      "papaya",
      "kiwi",
      "coconut",
      "avocado",
      "tomato",
      "potato",
      "carrot",
      "onion",
      "garlic",
      "pepper",
      "cucumber",
      "lettuce",
      "spinach",
      "broccoli",
      "cauliflower",
      "cabbage",
      "celery",
      "asparagus",
      "mushroom",
      "corn",
      "peas",
      "beans",
      "lentils",
      "chickpeas",
      "quinoa",
      "rice",
      "pasta",
      "bread",
      "cheese",
      "yogurt",
      "butter",
      "honey",
      "jam",
    ],
    "hard": [
      "computer",
      "keyboard",
      "mouse",
      "monitor",
      "speaker",
      // "headphone",
      // "microphone",
      // "camera",
      // "phone",
      // "tablet",
      // "laptop",
      // "printer",
      // "scanner",
      // "router",
      // "modem",
      // "server",
      // "database",
      // "network",
      // "internet",
      // "website",
      // "application",
      // "software",
      // "hardware",
      // "algorithm",
      // "programming",
      // "coding",
      // "development",
      // "testing",
      // "debugging",
      // "deployment",
      // "maintenance",
      // "upgrade",
      // "backup",
      // "security",
      // "encryption",
      // "authentication",
      // "authorization",
      // "firewall",
      // "antivirus",
      // "malware",
      // "phishing",
      // "spam",
      // "virus",
      // "trojan",
      // "worm",
      // "spyware",
      // "adware",
      // "ransomware",
      // "botnet",
      // "ddos",
    ],
  };
  static const Map<String, List<Map<String, dynamic>>> shapePatternData = {
    "easy": [
      {
        "sequence": ["circle", "square", "circle", "square", "?"],
        "options": ["circle", "square", "triangle", "star"],
        "answer": "circle",
        "explanation": "Alternates between circle and square.",
      },
      {
        "sequence": ["circle", "diamond", "circle", "diamond", "?"],
        "options": ["circle", "diamond", "star", "triangle"],
        "answer": "circle",
        "explanation": "Alternates between circle and diamond.",
      },
      {
        "sequence": ["triangle", "triangle", "square", "square", "?"],
        "options": ["triangle", "square", "circle", "diamond"],
        "answer": "triangle",
        "explanation": "Each shape repeats twice, then switches.",
      },
    ],
    "medium": [
      {
        "sequence": ["star", "star", "diamond", "diamond", "?"],
        "options": ["star", "diamond", "circle", "square"],
        "answer": "star",
        "explanation": "Each shape repeats twice, then switches.",
      },
      {
        "sequence": ["circle", "square", "triangle", "diamond", "?"],
        "options": ["star", "circle", "square", "triangle"],
        "answer": "star",
        "explanation": "Each shape is unique, next is star.",
      },
      {
        "sequence": ["circle", "square", "triangle", "circle", "square", "?"],
        "options": ["triangle", "circle", "square", "diamond"],
        "answer": "triangle",
        "explanation": "Rotates through circle, square, triangle.",
      },
    ],
    "hard": [
      {
        "sequence": [
          "circle",
          "square",
          "triangle",
          "circle",
          "square",
          "triangle",
          "?",
        ],
        "options": ["circle", "square", "diamond", "star"],
        "answer": "circle",
        "explanation": "Repeating sequence of circle, square, triangle.",
      },
      {
        "sequence": [
          "star",
          "diamond",
          "heart",
          "star",
          "diamond",
          "heart",
          "?",
        ],
        "options": ["star", "diamond", "heart", "circle"],
        "answer": "star",
        "explanation": "Repeating sequence of star, diamond, heart.",
      },
      {
        "sequence": [
          "circle",
          "square",
          "circle",
          "triangle",
          "circle",
          "diamond",
          "?",
        ],
        "options": ["circle", "square", "star", "heart"],
        "answer": "circle",
        "explanation": "Circle appears in every other position.",
      },
    ],
    "expert": [
      {
        "sequence": [
          "circle",
          "circle",
          "square",
          "square",
          "triangle",
          "triangle",
          "?",
        ],
        "options": ["circle", "square", "triangle", "diamond"],
        "answer": "triangle",
        "explanation":
            "AABBCC pattern, so next is another triangle, but the answer is triangle based on options.",
      },
      {
        "sequence": [
          "star",
          "diamond",
          "star",
          "heart",
          "star",
          "hexagon",
          "?",
        ],
        "options": ["star", "diamond", "heart", "circle"],
        "answer": "star",
        "explanation": "Star alternates with other shapes.",
      },
    ],
  };

  // Number Pattern Data
  static const Map<String, List<Map<String, dynamic>>> numberPatternData = {
    "easy": [
      {
        "sequence": [2, 4, 6, 8, "?"],
        "options": [10, 12, 9, 16],
        "answer": 10,
        "explanation": "The sequence increases by 2 each time.",
      },
    ],
    "medium": [
      {
        "sequence": [1, 2, 4, 8, "?"],
        "options": [16, 12, 20, 24],
        "answer": 16,
        "explanation": "Each number is multiplied by 2 to get the next number.",
      },
    ],
    "hard": [
      {
        "sequence": [4, 5, 7, 10, 14, 19, "?"],
        "options": [25, 24, 26, 23],
        "answer": 25,
        "explanation":
            "The difference between consecutive numbers increases by 1 each time (e.g., +1, +2, +3).",
      },
    ],
  };

  static const Map<String, List<Map<String, dynamic>>> letterPatternData = {
    "easy": [
      {
        "sequence": ["A", "B", "C", "D", "?"],
        "options": ["E", "F", "G", "H"],
        "answer": "E",
        "explanation": "Next letter in alphabet: A → B → C → D → E",
      },
      {
        "sequence": ["A", "C", "E", "G", "?"],
        "options": ["I", "H", "F", "D"],
        "answer": "I",
        "explanation": "Skip one letter each time: A → C → E → G → I",
      },
      {
        "sequence": ["A", "E", "I", "O", "?"],
        "options": ["U", "Y", "A", "E"],
        "answer": "U",
        "explanation": "Vowels in order: A → E → I → O → U",
      },
      {
        "sequence": ["A", "A", "B", "B", "?"],
        "options": ["C", "D", "A", "E"],
        "answer": "C",
        "explanation": "Repeat each letter twice: A → A → B → B → C",
      },
      {
        "sequence": ["Z", "Y", "X", "W", "?"],
        "options": ["V", "U", "T", "S"],
        "answer": "V",
        "explanation": "Alphabet in reverse: Z → Y → X → W → V",
      },
    ],

    "medium": [
      {
        "sequence": ["B", "D", "G", "K", "?"],
        "options": ["P", "O", "N", "M"],
        "answer": "P",
        "explanation":
            "Add 2, then 3, then 4, then 5 positions: B → D (+2) → G (+3) → K (+4) → P (+5)",
      },
      {
        "sequence": ["C", "F", "J", "O", "?"],
        "options": ["U", "T", "S", "R"],
        "answer": "U",
        "explanation": "Add 3, 4, 5, 6 positions: C → F → J → O → U",
      },
      {
        "sequence": ["A", "D", "G", "J", "?"],
        "options": ["M", "N", "O", "P"],
        "answer": "M",
        "explanation": "Add 3 each time: A → D → G → J → M",
      },
      {
        "sequence": ["M", "N", "P", "Q", "?"],
        "options": ["S", "R", "T", "U"],
        "answer": "S",
        "explanation": "Alternate +1 and +2: M(+1)N → N(+2)P → P(+1)Q → Q(+2)S",
      },
      {
        "sequence": ["E", "H", "L", "Q", "?"],
        "options": ["W", "V", "U", "T"],
        "answer": "W",
        "explanation": "Add 3, 4, 5, 6: E → H (+3) → L (+4) → Q (+5) → W (+6)",
      },
    ],

    "hard": [
      {
        "sequence": ["A", "B", "D", "G", "K", "P", "?"],
        "options": ["V", "U", "T", "S"],
        "answer": "V",
        "explanation": "Increasing step: +1, +2, +3, +4, +5, +6.",
      },
      {
        "sequence": ["Z", "Y", "W", "T", "P", "K", "?"],
        "options": ["E", "F", "G", "D"],
        "answer": "E",
        "explanation": "Decreasing step: -1, -2, -3, -4, -5, -6.",
      },
      {
        "sequence": ["A", "Z", "B", "Y", "C", "X", "?"],
        "options": ["D", "W", "E", "V"],
        "answer": "D",
        "explanation": "Two interleaved sequences: A, B, C... and Z, Y, X...",
      },
    ],
    "expert": [
      {
        "sequence": ["A", "C", "E", "G", "I", "K", "?"],
        "options": ["M", "L", "N", "O"],
        "answer": "M",
        "explanation": "Letters with one letter skipped in between.",
      },
      {
        "sequence": ["C", "F", "J", "O", "?"],
        "options": ["U", "T", "S", "R"],
        "answer": "U",
        "explanation": "Increasing step: +3, +4, +5, +6.",
      },
    ],
  };

  static const Map<String, List<Map<String, dynamic>>> colorPatternData = {
    "easy": [
      {
        "sequence": ["#FF6B6B", "#4ECDC4", "#FF6B6B", "#4ECDC4", "?"],
        "options": ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"],
        "answer": "#FF6B6B",
        "explanation": "Alternates between Red and Teal.",
      },
      {
        "sequence": ["#FFEAA7", "#FFEAA7", "#DDA0DD", "#DDA0DD", "?"],
        "options": ["#FFEAA7", "#DDA0DD", "#45B7D1", "#FF6B6B"],
        "answer": "#FFEAA7",
        "explanation": "Each color repeats twice, then switches.",
      },
      {
        "sequence": ["#45B7D1", "#96CEB4", "#45B7D1", "#96CEB4", "?"],
        "options": ["#45B7D1", "#96CEB4", "#FF6B6B", "#FFEAA7"],
        "answer": "#45B7D1",
        "explanation": "Blue and Green alternate repeatedly.",
      },
    ],
    "medium": [
      {
        "sequence": ["#FF6B6B", "#4ECDC4", "#45B7D1", "#FF6B6B", "?"],
        "options": ["#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7"],
        "answer": "#4ECDC4",
        "explanation": "Repeats Red, Teal, Blue.",
      },
      {
        "sequence": ["#FF6B6B", "#45B7D1", "#96CEB4", "#DDA0DD", "?"],
        "options": ["#FF6B6B", "#45B7D1", "#96CEB4", "#FFEAA7"],
        "answer": "#FF6B6B",
        "explanation": "Cyclic pattern of 4 colors.",
      },
      {
        "sequence": ["#FFEAA7", "#FF6B6B", "#FFEAA7", "#45B7D1", "?"],
        "options": ["#FF6B6B", "#FFEAA7", "#45B7D1", "#96CEB4"],
        "answer": "#FF6B6B",
        "explanation":
            "Yellow repeats every second step, alternating Red/Blue.",
      },
    ],
    "hard": [
      {
        "sequence": [
          "#FF6B6B",
          "#4ECDC4",
          "#45B7D1",
          "#FF6B6B",
          "#4ECDC4",
          "#45B7D1",
          "?",
        ],
        "options": ["#FF6B6B", "#4ECDC4", "#96CEB4", "#FFEAA7"],
        "answer": "#FF6B6B",
        "explanation": "Repeating sequence of Red, Teal, Blue.",
      },
      {
        "sequence": [
          "#FF6B6B",
          "#FF6B6B",
          "#4ECDC4",
          "#4ECDC4",
          "#45B7D1",
          "#45B7D1",
          "?",
        ],
        "options": ["#FF6B6B", "#4ECDC4", "#96CEB4", "#FFEAA7"],
        "answer": "#FF6B6B",
        "explanation": "AABBCC pattern, so next is another Red.",
      },
    ],
    "expert": [
      {
        "sequence": [
          "#FF6B6B",
          "#4ECDC4",
          "#FF6B6B",
          "#45B7D1",
          "#FF6B6B",
          "#96CEB4",
          "?",
        ],
        "options": ["#FF6B6B", "#4ECDC4", "#FFEAA7", "#DDA0DD"],
        "answer": "#FF6B6B",
        "explanation": "Red alternates with other colors.",
      },
      {
        "sequence": [
          "#FF6B6B",
          "#4ECDC4",
          "#45B7D1",
          "#96CEB4",
          "#FFEAA7",
          "#DDA0DD",
          "?",
        ],
        "options": ["#FF6B6B", "#4ECDC4", "#45B7D1", "#98D8C8"],
        "answer": "#FF6B6B",
        "explanation": "A repeating cycle of 6 unique colors.",
      },
    ],
  };

  // Sequence Memory Game Data
  static const Map<String, Map<String, dynamic>> sequenceMemoryData = {
    "easy": {"sequenceLength": 3, "displayTime": 1000, "maxScore": 10},
    "medium": {"sequenceLength": 5, "displayTime": 800, "maxScore": 15},
    "hard": {"sequenceLength": 7, "displayTime": 600, "maxScore": 20},
    "expert": {"sequenceLength": 9, "displayTime": 500, "maxScore": 25},
  };

  // Sequence Memory Games List
  static const List<Map<String, dynamic>> sequenceMemoryGames = [
    {
      "id": "sequence_memory_basic",
      "name": "Sequence Memory Excercise",
      "description": "Remember and repeat the sequence of lights",
      "difficulty": "medium",
      "type": "sequence_memory",
    },
    {
      "id": "sequence_memory_numbers",
      "name": "Number Sequence",
      "description": "Remember the sequence of numbers",
      "difficulty": "hard",
      "type": "number_sequence",
    },
    {
      "id": "sequence_memory_colors",
      "name": "Color Sequence",
      "description": "Remember the sequence of colors",
      "difficulty": "medium",
      "type": "color_sequence",
    },
    {
      "id": "sequence_memory_sounds",
      "name": "Sound Sequence",
      "description": "Remember the sequence of sounds",
      "difficulty": "expert",
      "type": "sound_sequence",
    },
    {
      "id": "sequence_memory_letters",
      "name": "Letter Sequence",
      "description": "Remember the sequence of letters",
      "difficulty": "medium",
      "type": "letter_sequence",
    },
    {
      "id": "sequence_memory_symbols",
      "name": "Symbol Sequence",
      "description": "Remember the sequence of symbols",
      "difficulty": "hard",
      "type": "symbol_sequence",
    },
  ];

  // Sequence Memory Content Pools
  static const List<String> sequenceNumbers = [
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
  ];

  static const List<String> sequenceColors = [
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#96CEB4",
    "#FFEAA7",
    "#DDA0DD",
    "#98D8C8",
    "#F7DC6F",
    "#BB8FCE",
  ];

  static const List<String> sequenceSymbols = [
    "★",
    "♦",
    "♠",
    "♣",
    "♥",
    "●",
    "◆",
    "■",
    "▲",
  ];

  static const List<String> sequenceLetters = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
  ];

  // Card Sequence Data
  static const List<Map<String, dynamic>> sequenceCards = [
    {"suit": "♠", "value": "A", "color": "black", "display": "A♠"},
    {"suit": "♠", "value": "2", "color": "black", "display": "2♠"},
    {"suit": "♠", "value": "3", "color": "black", "display": "3♠"},
    {"suit": "♥", "value": "A", "color": "red", "display": "A♥"},
    {"suit": "♥", "value": "2", "color": "red", "display": "2♥"},
    {"suit": "♥", "value": "3", "color": "red", "display": "3♥"},
    {"suit": "♦", "value": "A", "color": "red", "display": "A♦"},
    {"suit": "♦", "value": "2", "color": "red", "display": "2♦"},
    {"suit": "♦", "value": "3", "color": "red", "display": "3♦"},
    {"suit": "♣", "value": "A", "color": "black", "display": "A♣"},
    {"suit": "♣", "value": "2", "color": "black", "display": "2♣"},
    {"suit": "♣", "value": "3", "color": "black", "display": "3♣"},
  ];

  // Sequence Memory Questions/Patterns
  static const List<Map<String, dynamic>> sequenceMemoryQuestions = [
    {
      "type": "color_sequence",
      "sequence": [0, 1, 2, 0, 1],
      "difficulty": "easy",
      "description": "Remember the color sequence pattern",
    },
    {
      "type": "number_sequence",
      "sequence": [1, 3, 5, 7, 9],
      "difficulty": "medium",
      "description": "Remember the number sequence pattern",
    },
    {
      "type": "shape_sequence",
      "sequence": ["circle", "square", "triangle", "circle", "square"],
      "difficulty": "medium",
      "description": "Remember the shape sequence pattern",
    },
    {
      "type": "mixed_sequence",
      "sequence": [0, "star", 2, "circle", 4],
      "difficulty": "hard",
      "description": "Remember the mixed sequence pattern",
    },
    {
      "type": "reverse_sequence",
      "sequence": [5, 4, 3, 2, 1],
      "difficulty": "hard",
      "description": "Remember the reverse number sequence",
    },
    {
      "type": "fibonacci_sequence",
      "sequence": [1, 1, 2, 3, 5],
      "difficulty": "expert",
      "description": "Remember the Fibonacci sequence",
    },
    {
      "type": "alternating_colors",
      "sequence": [0, 2, 0, 2, 0],
      "difficulty": "easy",
      "description": "Remember the alternating color pattern",
    },
    {
      "type": "geometric_sequence",
      "sequence": [2, 4, 8, 16, 32],
      "difficulty": "expert",
      "description": "Remember the geometric progression",
    },
  ];

  // Memory Matching Game Data
  static const Map<String, Map<String, dynamic>> memoryMatchingData = {
    "easy": {
      "cardCount": 12,
      "maxMisses": 999, // No limit
      "levelVariant": "standard",
      "gridColumns": 4,
    },
    "medium": {
      "cardCount": 24,
      "maxMisses": 4,
      "levelVariant": "challenging",
      "gridColumns": 6,
    },
    "hard": {
      "cardCount": 36,
      "maxMisses": 0,
      "levelVariant": "perfect",
      "gridColumns": 6,
    },
  };

  // Memory Game Card Sets by Content Mode
  static const Map<String, List<Map<String, dynamic>>> memoryGameCards = {
    "animals": [
      {"display": "🐶", "name": "dog", "category": "pet"},
      {"display": "🐱", "name": "cat", "category": "pet"},
      {"display": "🐭", "name": "mouse", "category": "small"},
      {"display": "🐹", "name": "hamster", "category": "small"},
      {"display": "🐰", "name": "rabbit", "category": "small"},
      {"display": "🦊", "name": "fox", "category": "wild"},
      {"display": "🐻", "name": "bear", "category": "wild"},
      {"display": "🐼", "name": "panda", "category": "wild"},
      {"display": "🐨", "name": "koala", "category": "wild"},
      {"display": "🐯", "name": "tiger", "category": "wild"},
      {"display": "🦁", "name": "lion", "category": "wild"},
      {"display": "🐮", "name": "cow", "category": "farm"},
      {"display": "🐷", "name": "pig", "category": "farm"},
      {"display": "🐸", "name": "frog", "category": "amphibian"},
      {"display": "🐵", "name": "monkey", "category": "wild"},
      {"display": "🐧", "name": "penguin", "category": "bird"},
      {"display": "🐦", "name": "bird", "category": "bird"},
      {"display": "🐤", "name": "chick", "category": "bird"},
      {"display": "🦋", "name": "butterfly", "category": "insect"},
      {"display": "🐝", "name": "bee", "category": "insect"},
    ],
    "fruits": [
      {"display": "🍎", "name": "apple", "color": "red"},
      {"display": "🍌", "name": "banana", "color": "yellow"},
      {"display": "🍊", "name": "orange", "color": "orange"},
      {"display": "🍇", "name": "grapes", "color": "purple"},
      {"display": "🍓", "name": "strawberry", "color": "red"},
      {"display": "🍑", "name": "cherry", "color": "red"},
      {"display": "🍍", "name": "pineapple", "color": "yellow"},
      {"display": "🥭", "name": "mango", "color": "orange"},
      {"display": "🥝", "name": "kiwi", "color": "green"},
      {"display": "🍒", "name": "cherries", "color": "red"},
      {"display": "🍐", "name": "pear", "color": "green"},
      {"display": "🥑", "name": "avocado", "color": "green"},
      {"display": "🍅", "name": "tomato", "color": "red"},
      {"display": "🥕", "name": "carrot", "color": "orange"},
      {"display": "🌽", "name": "corn", "color": "yellow"},
      {"display": "🥔", "name": "potato", "color": "brown"},
      {"display": "🍠", "name": "sweet potato", "color": "orange"},
      {"display": "🥜", "name": "peanut", "color": "brown"},
    ],
    "vehicles": [
      {"display": "🚗", "name": "car", "type": "land"},
      {"display": "🚕", "name": "taxi", "type": "land"},
      {"display": "🚙", "name": "suv", "type": "land"},
      {"display": "🚌", "name": "bus", "type": "land"},
      {"display": "🚎", "name": "trolley", "type": "land"},
      {"display": "🏎️", "name": "race car", "type": "land"},
      {"display": "🚓", "name": "police car", "type": "land"},
      {"display": "🚑", "name": "ambulance", "type": "land"},
      {"display": "🚒", "name": "fire truck", "type": "land"},
      {"display": "🚐", "name": "van", "type": "land"},
      {"display": "🛻", "name": "truck", "type": "land"},
      {"display": "🚚", "name": "delivery truck", "type": "land"},
      {"display": "�", "name": "semi truck", "type": "land"},
      {"display": "🚜", "name": "tractor", "type": "land"},
      {"display": "🏍️", "name": "motorcycle", "type": "land"},
      {"display": "🛵", "name": "scooter", "type": "land"},
      {"display": "🚲", "name": "bicycle", "type": "land"},
      {"display": "✈️", "name": "airplane", "type": "air"},
      {"display": "🚁", "name": "helicopter", "type": "air"},
      {"display": "🚢", "name": "ship", "type": "water"},
    ],
    "shapes": [
      {"display": "⚪", "name": "circle", "sides": 0},
      {"display": "⬛", "name": "square", "sides": 4},
      {"display": "🔺", "name": "triangle", "sides": 3},
      {"display": "💎", "name": "diamond", "sides": 4},
      {"display": "⭐", "name": "star", "sides": 5},
      {"display": "❤️", "name": "heart", "sides": 0},
      {"display": "⬢", "name": "hexagon", "sides": 6},
      {"display": "🔷", "name": "blue diamond", "sides": 4},
      {"display": "🔶", "name": "orange diamond", "sides": 4},
      {"display": "🔸", "name": "small orange diamond", "sides": 4},
      {"display": "🔹", "name": "small blue diamond", "sides": 4},
      {"display": "🔴", "name": "red circle", "sides": 0},
      {"display": "🟡", "name": "yellow circle", "sides": 0},
      {"display": "🟢", "name": "green circle", "sides": 0},
      {"display": "🔵", "name": "blue circle", "sides": 0},
      {"display": "🟣", "name": "purple circle", "sides": 0},
      {"display": "🟠", "name": "orange circle", "sides": 0},
      {"display": "⚫", "name": "black circle", "sides": 0},
    ],
    "emojis": [
      {"display": "😀", "name": "grinning", "emotion": "happy"},
      {"display": "😃", "name": "smiley", "emotion": "happy"},
      {"display": "😄", "name": "smile", "emotion": "happy"},
      {"display": "😁", "name": "grin", "emotion": "happy"},
      {"display": "😆", "name": "laughing", "emotion": "happy"},
      {"display": "😅", "name": "sweat smile", "emotion": "happy"},
      {"display": "😂", "name": "joy", "emotion": "happy"},
      {"display": "🤣", "name": "rofl", "emotion": "happy"},
      {"display": "😊", "name": "blush", "emotion": "happy"},
      {"display": "😇", "name": "innocent", "emotion": "happy"},
      {"display": "🙂", "name": "slight smile", "emotion": "happy"},
      {"display": "🙃", "name": "upside down", "emotion": "playful"},
      {"display": "😉", "name": "wink", "emotion": "playful"},
      {"display": "😌", "name": "relieved", "emotion": "calm"},
      {"display": "😍", "name": "heart eyes", "emotion": "love"},
      {"display": "🥰", "name": "smiling hearts", "emotion": "love"},
      {"display": "😘", "name": "kiss", "emotion": "love"},
      {"display": "😗", "name": "kissing", "emotion": "love"},
    ],
  };

  // Memory Game Instructions by Content Mode
  static const Map<String, String> memoryGameInstructions = {
    "animals":
        "Match pairs of the same animals! Remember where you saw each cute creature.",
    "fruits":
        "Find matching fruit pairs! Keep track of where each delicious fruit is hiding.",
    "vehicles":
        "Match the vehicle pairs! Remember where each mode of transport is located.",
    "shapes":
        "Find matching shape pairs! Memorize the location of each geometric shape.",
    "emojis":
        "Match the emoji pairs! Remember where each expression is hiding.",
  };

  // Word Search Game Data
  static const Map<String, List<Map<String, dynamic>>> wordSearchData = {
    "easy": [
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Simple Words",
        "words": ["CAT", "DOG", "SUN", "BIRD", "FISH"],
      },
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Colors",
        "words": ["RED", "BLUE", "GREEN", "PINK", "YELLOW"],
      },
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Body Parts",
        "words": ["ARM", "LEG", "EYE", "NOSE", "HAND"],
      },
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Food",
        "words": ["CAKE", "MILK", "BREAD", "RICE", "SOUP"],
      },
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Family",
        "words": ["MOM", "DAD", "BABY", "AUNT", "UNCLE"],
      },
      {
        "gridSize": 8,
        "timeLimit": 120,
        "theme": "Toys",
        "words": ["BALL", "DOLL", "BIKE", "KITE", "BOOK"],
      },
    ],
    "medium": [
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "Nature",
        "words": ["TREE", "FLOWER", "OCEAN", "MOUNTAIN", "FOREST", "RIVER"],
      },
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "Weather",
        "words": ["SUNNY", "CLOUDY", "RAINY", "SNOWY", "WINDY", "STORM"],
      },
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "Sports",
        "words": ["SOCCER", "TENNIS", "HOCKEY", "BOXING", "RACING", "GOLF"],
      },
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "School",
        "words": ["TEACHER", "STUDENT", "PENCIL", "PAPER", "LESSON", "BOOK"],
      },
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "Transportation",
        "words": ["TRAIN", "PLANE", "BICYCLE", "BOAT", "TRUCK", "SUBWAY"],
      },
      {
        "gridSize": 10,
        "timeLimit": 180,
        "theme": "Music",
        "words": ["PIANO", "GUITAR", "DRUMS", "VIOLIN", "SINGER", "DANCE"],
      },
    ],
    "hard": [
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "Animals",
        "words": [
          "ELEPHANT",
          "BUTTERFLY",
          "DOLPHIN",
          "PENGUIN",
          "GIRAFFE",
          "KANGAROO",
          "TIGER",
        ],
      },
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "Technology",
        "words": [
          "COMPUTER",
          "INTERNET",
          "KEYBOARD",
          "MONITOR",
          "SOFTWARE",
          "DATABASE",
          "NETWORK",
        ],
      },
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "Geography",
        "words": [
          "CONTINENT",
          "MOUNTAIN",
          "DESERT",
          "VOLCANO",
          "ISLAND",
          "PENINSULA",
          "PLATEAU",
        ],
      },
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "Science",
        "words": [
          "CHEMISTRY",
          "PHYSICS",
          "BIOLOGY",
          "MOLECULE",
          "ELECTRON",
          "GRAVITY",
          "ENERGY",
        ],
      },
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "Space",
        "words": [
          "GALAXY",
          "PLANET",
          "ASTEROID",
          "TELESCOPE",
          "SATELLITE",
          "UNIVERSE",
          "COMET",
        ],
      },
      {
        "gridSize": 12,
        "timeLimit": 240,
        "theme": "History",
        "words": [
          "ANCIENT",
          "MEDIEVAL",
          "EMPIRE",
          "DYNASTY",
          "WARRIOR",
          "CASTLE",
          "KINGDOM",
        ],
      },
    ],
  };
}

class GameLevel {
  final String startedAt;
  final String endedAt;
  final String sessionStatus;

  GameLevel({
    required this.startedAt,
    required this.endedAt,
    required this.sessionStatus,
  });

  Duration? get duration {
    try {
      final start = DateTime.parse(startedAt);
      final end = DateTime.parse(endedAt);

      if (start.isAfter(end)) {
        return start.difference(end);
      }
      return end.difference(start);
    } catch (e) {
      return null;
    }
  }

  String get formattedStartTime {
    try {
      final start = DateTime.parse(startedAt);
      final day = start.day.toString().padLeft(2, '0');
      final month = start.month.toString().padLeft(2, '0');
      final year = start.year.toString();
      final hour = start.hour.toString().padLeft(2, '0');
      final minute = start.minute.toString().padLeft(2, '0');
      return '$day/$month/$year $hour:$minute ';
    } catch (e) {
      return startedAt.isNotEmpty ? startedAt : 'N/A';
    }
  }

  String get formattedEndTime {
    try {
      final end = DateTime.parse(endedAt);
      final day = end.day.toString().padLeft(2, '0');
      final month = end.month.toString().padLeft(2, '0');
      final year = end.year.toString();
      final hour = end.hour.toString().padLeft(2, '0');
      final minute = end.minute.toString().padLeft(2, '0');
      return '$day/$month/$year $hour:$minute ';
    } catch (e) {
      return endedAt.isNotEmpty ? endedAt : 'N/A';
    }
  }

  String get formattedStartTimeOnly {
    try {
      final start = DateTime.parse(startedAt);
      final hour = start.hour.toString().padLeft(2, '0');
      final minute = start.minute.toString().padLeft(2, '0');
      return '$hour:$minute UTC';
    } catch (e) {
      return 'N/A';
    }
  }

  String get formattedEndTimeOnly {
    try {
      final end = DateTime.parse(endedAt);
      final hour = end.hour.toString().padLeft(2, '0');
      final minute = end.minute.toString().padLeft(2, '0');
      return '$hour:$minute UTC';
    } catch (e) {
      return 'N/A';
    }
  }

  String get formattedDuration {
    final dur = duration;
    if (dur == null) return 'N/A';

    final totalMinutes = dur.inMinutes;
    final totalSeconds = dur.inSeconds;

    if (totalMinutes > 0) {
      final minutes = totalMinutes;
      final seconds = totalSeconds % 60;
      if (seconds > 0) {
        return '${minutes}m ${seconds}s';
      } else {
        return '${minutes}m';
      }
    } else {
      return '${totalSeconds}s';
    }
  }

  factory GameLevel.fromJson(Map<String, dynamic> json) {
    return GameLevel(
      startedAt: json['started_at']?.toString() ?? '',
      endedAt: json['ended_at']?.toString() ?? '',
      sessionStatus: json['session_status']?.toString() ?? '',
    );
  }
}

class RecentActivity {
  final String gameId;
  final String name;
  final String description;
  final String thumbnailUrl;
  final List<GameLevel> levels;

  RecentActivity({
    required this.gameId,
    required this.name,
    required this.description,
    required this.thumbnailUrl,
    required this.levels,
  });

  String get endedAt {
    if (levels.isEmpty) return '';
    return levels.first.endedAt;
  }

  int get completedSessions {
    return levels.where((level) => level.sessionStatus == 'Completed').length;
  }

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    final levelsList = json['levels'] as List? ?? [];
    final levels = levelsList
        .map((level) => GameLevel.fromJson(level as Map<String, dynamic>))
        .toList();

    return RecentActivity(
      gameId: json['game_id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      thumbnailUrl: json['thumbnail_url']?.toString() ?? '',
      levels: levels,
    );
  }
}

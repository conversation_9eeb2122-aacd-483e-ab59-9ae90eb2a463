import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';

import '../models/recent_activity.dart';

class RecentActivitiesService {
  static Future<List<RecentActivity>?> getRecentActivities({
    required String userId,
    required String tenantCode,
    required String accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.getRecentActivities}',
      );
      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        'Authorization': 'Bearer $accessToken',
      };

      final body = {"tenant_code": tenantCode, "user_id": userId};

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData is Map &&
            responseData['status'] == 'success' &&
            responseData['recent_activities'] != null) {
          final activitiesList = responseData['recent_activities'] as List;
          return activitiesList
              .map((activity) => RecentActivity.fromJson(activity as Map<String, dynamic>))
              .toList();
        }
        return [];
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }
}




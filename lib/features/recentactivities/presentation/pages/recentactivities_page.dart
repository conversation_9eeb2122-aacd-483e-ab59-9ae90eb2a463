import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/database/hive_db_helper.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../data/models/recent_activity.dart';
import '../../data/services/recent_activities_service.dart';

class RecentActivitiesPage extends StatefulWidget {
  const RecentActivitiesPage({super.key});

  @override
  State<RecentActivitiesPage> createState() => _RecentActivitiesPageState();
}

class _RecentActivitiesPageState extends State<RecentActivitiesPage> {
  bool _isLoading = true;
  String? _error;
  List<RecentActivity> _activities = const [];
  bool _showAllActivities = false;
  static const int _initialDisplayLimit = 8;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final hiveUser = await HiveUserService().getCurrentUser();
      final userInfo = await HiveUserService().getUserInfo();

      final userId = hiveUser?.id;
      final accessToken = hiveUser?.accessToken;
      final tenantCode = userInfo?['tenant_code'] ?? hiveUser?.tenantCode;

      if (userId == null || accessToken == null || tenantCode == null) {
        setState(() {
          _error = 'Missing auth or tenant info';
          _isLoading = false;
        });
        return;
      }

      final data = await RecentActivitiesService.getRecentActivities(
        userId: userId,
        tenantCode: tenantCode,
        accessToken: accessToken,
      );

      if (!mounted) return;

      setState(() {
        _activities = data ?? [];
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = 'Failed to load: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'History',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
          ? _buildError(_error!)
          : _activities.isEmpty
          ? _buildEmpty()
          : RefreshIndicator(
              onRefresh: _load,
              child: ListView(
                padding: EdgeInsets.all(AppTheme.spacing16),
                children: [
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(AppTheme.spacing20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: AppTheme.spacing16),
                          _buildActivitiesList(_activities),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildActivitiesList(List<RecentActivity> activities) {
    final List<RecentActivity> displayActivities = _showAllActivities
        ? activities
        : activities.take(_initialDisplayLimit).toList();

    final hasMore = activities.length > _initialDisplayLimit;

    return Column(
      children: [
        ...displayActivities.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return Padding(
            padding: EdgeInsets.only(
              bottom: index == displayActivities.length - 1
                  ? 0
                  : AppTheme.spacing12,
            ),
            child: _ActivityTile(activity: item),
          );
        }),
        if (hasMore) ...[
          SizedBox(height: AppTheme.spacing12),
          _buildToggleButton(activities.length),
        ],
      ],
    );
  }

  Widget _buildToggleButton(int total) {
    final remaining = total - _initialDisplayLimit;

    return Align(
      alignment: Alignment.center,
      child: TextButton.icon(
        onPressed: () {
          setState(() {
            _showAllActivities = !_showAllActivities;
          });
        },
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            vertical: AppTheme.spacing12,
            horizontal: AppTheme.spacing16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            side: BorderSide(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
        ),
        icon: Icon(
          _showAllActivities ? Icons.expand_less : Icons.expand_more,
          color: AppTheme.primaryColor,
          size: 18.sp,
        ),
        label: ResponsiveText(
          _showAllActivities ? 'Show Less' : 'View more',
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64.sp, color: AppTheme.textSecondary),
          SizedBox(height: AppTheme.spacing16),
          ResponsiveText(
            'No recent activities found',
            style: AppTheme.bodyLarge.copyWith(color: AppTheme.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildError(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: AppTheme.errorColor),
          SizedBox(height: AppTheme.spacing16),
          ResponsiveText(
            message,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing16),
          ElevatedButton(onPressed: _load, child: const Text('Try again')),
        ],
      ),
    );
  }
}

class _ActivityTile extends StatefulWidget {
  const _ActivityTile({required this.activity});
  final RecentActivity activity;

  @override
  State<_ActivityTile> createState() => _ActivityTileState();
}

class _ActivityTileState extends State<_ActivityTile> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.dividerColor, width: 1),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: widget.activity.levels.isNotEmpty
                ? () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  }
                : null,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            child: Padding(
              padding: EdgeInsets.all(AppTheme.spacing16),
              child: Row(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: widget.activity.thumbnailUrl.isNotEmpty
                        ? Image.network(
                            widget.activity.thumbnailUrl,
                            fit: BoxFit.cover,
                          )
                        : Icon(
                            Icons.psychology,
                            color: AppTheme.primaryColor,
                            size: 24.sp,
                          ),
                  ),
                  SizedBox(width: AppTheme.spacing12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ResponsiveText(
                          widget.activity.name,
                          style: AppTheme.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        if (widget.activity.description.isNotEmpty)
                          ResponsiveText(
                            widget.activity.description,
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        if (widget.activity.levels.isNotEmpty) ...[
                          SizedBox(height: 4.h),
                          ResponsiveText(
                            '${widget.activity.completedSessions} levels completed',
                            style: AppTheme.labelSmall.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (widget.activity.levels.isNotEmpty)
                    Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppTheme.textSecondary,
                      size: 20.sp,
                    ),
                ],
              ),
            ),
          ),
          if (_isExpanded && widget.activity.levels.isNotEmpty)
            _buildLevelsDetail(),
        ],
      ),
    );
  }

  Widget _buildLevelsDetail() {
    return Container(
      padding: EdgeInsets.fromLTRB(
        AppTheme.spacing16,
        0,
        AppTheme.spacing16,
        AppTheme.spacing16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(color: AppTheme.dividerColor, height: 1),
          SizedBox(height: AppTheme.spacing12),
          ResponsiveText(
            'Level Details',
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: AppTheme.spacing8),
          ...widget.activity.levels.asMap().entries.map((entry) {
            final index = entry.key;
            final level = entry.value;
            return _buildLevelItem(level, index + 1);
          }),
        ],
      ),
    );
  }

  Widget _buildLevelItem(GameLevel level, int levelNumber) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing8),
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: AppTheme.dividerColor.withOpacity(0.5)),
      ),
      child: Row(
        children: [
          Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              color: level.sessionStatus == 'Completed'
                  ? AppTheme.primaryColor
                  : AppTheme.textSecondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: ResponsiveText(
                levelNumber.toString(),
                style: AppTheme.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ResponsiveText(
                      'Level $levelNumber',
                      style: AppTheme.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 6.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: level.sessionStatus == 'Completed'
                            ? AppTheme.primaryColor.withOpacity(0.1)
                            : AppTheme.textSecondary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: ResponsiveText(
                        level.sessionStatus,
                        style: AppTheme.labelSmall.copyWith(
                          color: level.sessionStatus == 'Completed'
                              ? AppTheme.primaryColor
                              : AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                          fontSize: 10.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.play_arrow,
                          size: 14.sp,
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(width: 6.w),
                        ResponsiveText(
                          'Started At: ',
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.textSecondary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Expanded(
                          child: ResponsiveText(
                            level.formattedStartTime,
                            style: AppTheme.labelSmall.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 6.h),
                    Row(
                      children: [
                        Icon(
                          Icons.stop,
                          size: 14.sp,
                          color: AppTheme.errorColor,
                        ),
                        SizedBox(width: 6.w),
                        ResponsiveText(
                          'Ended At: ',
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.textSecondary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Expanded(
                          child: ResponsiveText(
                            level.formattedEndTime,
                            style: AppTheme.labelSmall.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (level.formattedDuration.isNotEmpty &&
                        level.formattedDuration != 'N/A') ...[
                      SizedBox(height: 6.h),
                      Row(
                        children: [
                          Icon(
                            Icons.timer,
                            size: 14.sp,
                            color: AppTheme.primaryColor,
                          ),
                          SizedBox(width: 6.w),
                          ResponsiveText(
                            'Duration: ',
                            style: AppTheme.labelSmall.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 3.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            child: ResponsiveText(
                              level.formattedDuration,
                              style: AppTheme.labelSmall.copyWith(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 11.sp,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class UserProfile {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String? phone;
  final String? avatarUrl;
  final String? gender;
  final String? bio;
  final int gamesPlayedCount;
  final int assignedGamesCount;
  final int rewardsReceived;
  final int levelsCompleted;

  UserProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.avatarUrl,
    this.gender,
    this.bio,
    this.gamesPlayedCount = 0,
    this.assignedGamesCount = 0,
    this.rewardsReceived = 0,
    this.levelsCompleted = 0,
  });

  UserProfile copyWith({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatarUrl,
    String? gender,
    String? bio,
    int? gamesPlayedCount,
    int? assignedGamesCount,
    int? rewardsReceived,
    int? levelsCompleted,
  }) {
    return UserProfile(
      id: id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      gender: gender ?? this.gender,
      bio: bio ?? this.bio,
      gamesPlayedCount: gamesPlayedCount ?? this.gamesPlayedCount,
      assignedGamesCount: assignedGamesCount ?? this.assignedGamesCount,
      rewardsReceived: rewardsReceived ?? this.rewardsReceived,
      levelsCompleted: levelsCompleted ?? this.levelsCompleted,
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/profile/data/services/profile_services.dart';

import '../../../domain/entities/user_profile.dart';

part 'profile_bloc.freezed.dart';
part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc() : super(const _Initial()) {
    on<ProfileEvent>((event, emit) async {
      try {
        if (event is _LoadRequested) {
          debugPrint('ProfileBloc: Load requested');
          emit(const _Loading());

          final loginModel = await HiveUserService().getCurrentUser();
          final partyId = loginModel?.id;
          final accessToken = loginModel?.accessToken ?? '';

          debugPrint('ProfileBloc: User ID: $partyId');
          debugPrint('ProfileBloc: Access Token length: ${accessToken.length}');

          if (partyId == null) {
            debugPrint('ProfileBloc: User ID not found');
            emit(const _Error('User ID not found'));
            return;
          }

          final profile = await ProfileDetailsService.getProfileDetails(
            partyId: partyId,
            accessToken: accessToken,
            tenantCode: loginModel!.tenantCode,
          );

          if (profile != null) {
            debugPrint('ProfileBloc: Profile loaded successfully');
            // debugPrint('Profile data: ${profile.toJson()}');
            emit(_Loaded(profile));
          } else {
            debugPrint('ProfileBloc: Failed to load profile');
            emit(const _Error('Failed to load profile'));
          }
        } else if (event is _UpdateRequested) {
          debugPrint('ProfileBloc: Update requested');

          UserProfile? currentProfile;
          if (state is _Loaded) {
            currentProfile = (state as _Loaded).profile;
          }

          emit(const _Loading());

          final loginModel = await HiveUserService().getCurrentUser();
          final userId = loginModel?.id;
          final accessToken = loginModel?.accessToken ?? '';

          debugPrint('ProfileBloc: Update - User ID: $userId');
          debugPrint(
            'ProfileBloc: Update - Access Token length: ${accessToken.length}',
          );

          if (userId == null) {
            debugPrint('ProfileBloc: Update - User ID not found');
            emit(const _Error('User ID not found'));
            return;
          }

          debugPrint('ProfileBloc: Calling updateProfileDetails API');
          debugPrint('ProfileBloc: Update parameters:');
          debugPrint('  - firstName: ${event.firstName}');
          debugPrint('  - lastName: ${event.lastName}');
          debugPrint('  - email: ${event.email}');
          debugPrint('  - phone: ${event.phone}');
          debugPrint('  - bio: ${event.bio}');
          debugPrint('  - gender: ${event.gender}');
          debugPrint('  - avatarUrl: ${event.avatarUrl}');

          final success = await ProfileDetailsService.updateProfileDetails(
            userId: userId,
            accessToken: accessToken,
            firstName: event.firstName,
            lastName: event.lastName,
            bio: event.bio,
            phoneNumber: event.phone,
            gender: event.gender,
            email: event.email,
            avatarUrl: event.avatarUrl,
          );

          debugPrint('ProfileBloc: API call result: $success');

          if (success) {
            debugPrint(
              'ProfileBloc: Profile updated successfully, reloading from server',
            );

            final updatedProfile =
                await ProfileDetailsService.getProfileDetails(
                  partyId: userId,
                  accessToken: accessToken,
                  tenantCode: loginModel!.tenantCode,
                );

            if (updatedProfile != null) {
              debugPrint('ProfileBloc: Updated profile loaded from server');
              emit(_Loaded(updatedProfile));
            } else {
              UserProfile finalProfile;
              if (currentProfile != null) {
                finalProfile = currentProfile.copyWith(
                  firstName: event.firstName ?? currentProfile.firstName,
                  lastName: event.lastName ?? currentProfile.lastName,
                  phone: event.phone ?? currentProfile.phone,
                  bio: event.bio ?? currentProfile.bio,
                  gender: event.gender ?? currentProfile.gender,
                  email: event.email ?? currentProfile.email,
                  avatarUrl: event.avatarUrl ?? currentProfile.avatarUrl,
                );
              } else {
                finalProfile = UserProfile(
                  id: userId,
                  firstName: event.firstName ?? '',
                  lastName: event.lastName ?? '',
                  email: event.email ?? '',
                  phone: event.phone,
                  bio: event.bio,
                  gender: event.gender,
                  avatarUrl: event.avatarUrl,
                );
              }

              debugPrint('ProfileBloc: Using local profile update as fallback');
              emit(_Loaded(finalProfile));
            }
          } else {
            debugPrint('ProfileBloc: Failed to update profile');
            emit(const _Error('Failed to update profile'));
          }
        } else if (event is _ImageUploadRequested) {
          debugPrint('ProfileBloc: Standalone image upload requested');

          UserProfile? currentProfile;
          if (state is _Loaded) {
            currentProfile = (state as _Loaded).profile;
          }

          emit(const _UploadingImage());

          final loginModel = await HiveUserService().getCurrentUser();
          final userId = loginModel?.id;
          final accessToken = loginModel?.accessToken ?? '';

          if (userId == null) {
            debugPrint('ProfileBloc: Image upload - User ID not found');
            emit(const _Error('User ID not found'));
            return;
          }

          debugPrint('ProfileBloc: Calling updateAvatarComplete');

          final newAvatarUrl = await ProfileDetailsService.updateAvatarComplete(
            userId: userId,
            accessToken: accessToken,
            source: event.source,
          );

          debugPrint('ProfileBloc: Avatar update result: $newAvatarUrl');

          if (newAvatarUrl != null &&
              newAvatarUrl.isNotEmpty &&
              currentProfile != null) {
            final updatedProfile =
                await ProfileDetailsService.getProfileDetails(
                  partyId: userId,
                  accessToken: accessToken,
                  tenantCode: loginModel!.tenantCode,
                );

            if (updatedProfile != null) {
              debugPrint(
                'ProfileBloc: Avatar updated and profile reloaded from server',
              );
              emit(_Loaded(updatedProfile));
            } else {
              final localUpdate = currentProfile.copyWith(
                avatarUrl: newAvatarUrl,
              );
              debugPrint('ProfileBloc: Avatar updated with local fallback');
              emit(_Loaded(localUpdate));
            }
          } else {
            debugPrint('ProfileBloc: Failed to update avatar');
            emit(const _Error('Failed to update avatar'));
          }
        } else if (event is _AvatarUpdateRequested) {
          debugPrint('ProfileBloc: Avatar update with URL requested');

          UserProfile? currentProfile;
          if (state is _Loaded) {
            currentProfile = (state as _Loaded).profile;
          }

          emit(const _Loading());

          final loginModel = await HiveUserService().getCurrentUser();
          final userId = loginModel?.id;
          final accessToken = loginModel?.accessToken ?? '';

          if (userId == null) {
            debugPrint('ProfileBloc: Avatar URL update - User ID not found');
            emit(const _Error('User ID not found'));
            return;
          }

          debugPrint('ProfileBloc: Updating avatar URL: ${event.avatarUrl}');

          final success = await ProfileDetailsService.updateProfileDetails(
            userId: userId,
            accessToken: accessToken,
            avatarUrl: event.avatarUrl,
          );

          if (success) {
            final updatedProfile =
                await ProfileDetailsService.getProfileDetails(
                  partyId: userId,
                  accessToken: accessToken,
                  tenantCode: loginModel!.tenantCode,
                );

            if (updatedProfile != null) {
              debugPrint(
                'ProfileBloc: Avatar URL updated and profile reloaded',
              );
              emit(_Loaded(updatedProfile));
            } else if (currentProfile != null) {
              debugPrint('ProfileBloc: Avatar URL updated with local fallback');
              emit(
                _Loaded(currentProfile.copyWith(avatarUrl: event.avatarUrl)),
              );
            } else {
              emit(
                const _Error('Failed to reload profile after avatar update'),
              );
            }
          } else {
            debugPrint('ProfileBloc: Failed to update avatar URL');
            emit(const _Error('Failed to update avatar'));
          }
        }
      } catch (e) {
        debugPrint('ProfileBloc: Exception occurred: $e');
        emit(_Error(e.toString()));
      }
    });
  }
}

part of 'profile_bloc.dart';

@freezed
class ProfileEvent with _$ProfileEvent {
  const factory ProfileEvent.loadRequested() = _LoadRequested;

  const factory ProfileEvent.updateRequested({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? bio,
    String? gender,
    String? avatarUrl,
  }) = _UpdateRequested;

  const factory ProfileEvent.imageUploadRequested({
    required ImageSource source,
  }) = _ImageUploadRequested;

  const factory ProfileEvent.avatarUpdateRequested({
    required String avatarUrl,
  }) = _AvatarUpdateRequested;
}

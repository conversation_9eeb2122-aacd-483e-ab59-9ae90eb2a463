import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/core/router/app_router.dart';
import 'package:recallloop/core/theme/app_theme.dart';
import 'package:recallloop/features/auth/presentation/bloc/auth/auth_bloc.dart';
import 'package:recallloop/features/profile/data/services/profile_services.dart';
import 'package:recallloop/features/profile/domain/entities/user_profile.dart';
import 'package:recallloop/features/profile/presentation/bloc/profile/profile_bloc.dart';
import 'package:recallloop/shared/presentation/widgets/responsive_layout.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late ProfileBloc _profileBloc;
  String? _signedAvatarUrl;

  @override
  void initState() {
    super.initState();
    _profileBloc = ProfileBloc()..add(const ProfileEvent.loadRequested());
  }

  @override
  void dispose() {
    _profileBloc.close();
    super.dispose();
  }

  void _refreshProfile() {
    _profileBloc.add(const ProfileEvent.loadRequested());
  }

  Widget _buildAvatarImage(UserProfile profile) {
    final imageUrl = _signedAvatarUrl ?? profile.avatarUrl;

    if (imageUrl != null && imageUrl.isNotEmpty) {
      return Image.network(
        imageUrl,
        width: 100,
        height: 100,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Avatar loading error: $error');
          return _buildDefaultAvatarContent(profile);
        },
      );
    } else {
      return _buildDefaultAvatarContent(profile);
    }
  }

  Widget _buildDefaultAvatarContent(UserProfile profile) {
    final initials =
        '${profile.firstName.isNotEmpty ? profile.firstName[0].toUpperCase() : 'U'}${profile.lastName.isNotEmpty ? profile.lastName[0].toUpperCase() : 'U'}';

    return Container(
      width: 100,
      height: 100,
      color: Colors.grey[400],
      child: Center(
        child: Text(
          initials,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Future<void> _fetchSignedAvatarUrl(String? avatarUrl) async {
    if (avatarUrl != null && avatarUrl.isNotEmpty && mounted) {
      try {
        final loginModel = await HiveUserService().getCurrentUser();
        final userId = loginModel?.id;
        final accessToken = loginModel?.accessToken ?? '';
        if (userId != null) {
          final fileName = avatarUrl.split('/').last;
          for (int i = 0; i < 3; i++) {
            final signedUrl = await ProfileDetailsService.getSignedAvatarUrl(
              userId: userId,
              accessToken: accessToken,
              fileName: fileName,
            );
            if (signedUrl != null && mounted) {
              setState(() {
                _signedAvatarUrl = signedUrl;
              });
              return;
            }
            await Future.delayed(const Duration(seconds: 1));
          }
          debugPrint('Failed to fetch signed URL after retries');
        }
      } catch (e) {
        debugPrint('Error fetching signed URL: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            state.when(
              initial: () {},
              loading: () {},
              authenticated: (loginUser) {},
              unauthenticated: () {
                context.go(AppRouter.login);
              },
              error: (message) {},
              signUpSuccess: (signupUser) {},
              signUpError: (message) {},
              showUserModal: (loginUser) {},
            );
          },
        ),
        BlocListener<ProfileBloc, ProfileState>(
          bloc: _profileBloc,
          listener: (context, state) {
            state.when(
              initial: () {},
              loading: () {},
              loaded: (profile) {
                _fetchSignedAvatarUrl(profile.avatarUrl);
              },
              error: (message) {},
              uploadingImage: () {},
            );
          },
        ),
      ],
      child: BlocProvider.value(
        value: _profileBloc,
        child: Scaffold(
          appBar: AppBar(
            title: ResponsiveText(
              'Profile',
              style: AppTheme.headlineSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: BlocBuilder<ProfileBloc, ProfileState>(
            bloc: _profileBloc,
            builder: (context, state) {
              return state.when(
                initial: () => const Center(child: Text('Initializing...')),
                loading: () => const Center(child: CircularProgressIndicator()),
                loaded: (profile) => _buildProfileContent(context, profile),
                error: (message) => Center(child: Text('Error: $message')),
                uploadingImage: () => _buildUploadingState(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, UserProfile profile) {
    final fullName = '${profile.firstName} ${profile.lastName}';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.blue[400]!, Colors.purple[400]!],
                ),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey[300],
                    child: ClipOval(child: _buildAvatarImage(profile)),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    fullName,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profile.email,
                    style: const TextStyle(fontSize: 16, color: Colors.white70),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profile.phone ?? 'No phone number',
                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 400;

              if (isSmallScreen) {
                return Column(
                  children: [
                    Row(
                      children: [
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'My Excercises',
                            profile.assignedGamesCount.toString(),
                            Icons.assignment,
                            Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildStatCard(
                            'Games Played',
                            profile.gamesPlayedCount.toString(),
                            Icons.sports_esports,
                            Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'Rewards',
                            profile.rewardsReceived.toString(),
                            Icons.card_giftcard,
                            Colors.red,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'Levels Completed',
                            profile.levelsCompleted.toString(),
                            Icons.games,
                            Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              } else {
                return Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Games Played',
                        profile.gamesPlayedCount.toString(),
                        Icons.sports_esports,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'Assigned Excercise',
                        profile.assignedGamesCount.toString(),
                        Icons.assignment,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'Rewards',
                        profile.rewardsReceived.toString(),
                        Icons.card_giftcard,
                        Colors.red,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'Levels Completed',
                        profile.levelsCompleted.toString(),
                        Icons.games,
                        Colors.blue,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Profile Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.blue),
                        onPressed: () async {
                          await context.push('/profile/edit', extra: profile);
                          _refreshProfile();
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(Icons.email, 'Email', profile.email),
                  _buildInfoRow(
                    Icons.phone,
                    'Phone',
                    profile.phone ?? 'Not provided',
                  ),
                  _buildInfoRow(
                    Icons.info,
                    'Bio',
                    profile.bio ?? 'No bio available',
                  ),
                  _buildInfoRow(
                    Icons.people,
                    'Gender',
                    profile.gender ?? 'Not specified',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showLogoutDialog(context),
                  icon: const Icon(Icons.logout, color: Colors.red),
                  label: const Text(
                    'Logout',
                    style: TextStyle(color: Colors.red),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    side: const BorderSide(color: Colors.red),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        height: 120,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 6),
            Text(
              value,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(const AuthEvent.logoutRequested());
            },
            child: const Text('Logout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Uploading image...',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text(
            'Please wait while we update your profile picture',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/profile/data/services/profile_services.dart';
import 'package:recallloop/features/profile/data/services/universal_image_picker.dart';
import 'package:recallloop/features/profile/domain/entities/user_profile.dart';
import 'package:recallloop/features/profile/presentation/bloc/profile/profile_bloc.dart';

class EditProfilePage extends StatefulWidget {
  final UserProfile profile;

  const EditProfilePage({super.key, required this.profile});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _bioController;
  String? _selectedGender;
  String? _currentAvatarUrl;
  bool _isLoading = false;
  bool _isUploadingImage = false;

  ImagePickerResult? _selectedImage;
  String? _signedAvatarUrl;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _firstNameController = TextEditingController(
      text: widget.profile.firstName,
    );
    _lastNameController = TextEditingController(text: widget.profile.lastName);
    _emailController = TextEditingController(text: widget.profile.email);
    _phoneController = TextEditingController(text: widget.profile.phone ?? '');
    _bioController = TextEditingController(text: widget.profile.bio ?? '');
    _selectedGender = widget.profile.gender;
    _currentAvatarUrl = widget.profile.avatarUrl;

    _fetchSignedAvatarUrl();
  }

  Future<void> _fetchSignedAvatarUrl() async {
    if (_currentAvatarUrl != null && _currentAvatarUrl!.isNotEmpty) {
      try {
        final loginModel = await HiveUserService().getCurrentUser();
        final userId = loginModel?.id;
        final accessToken = loginModel?.accessToken ?? '';
        if (userId != null) {
          final fileName = _currentAvatarUrl!.split('/').last;
          final signedUrl = await ProfileDetailsService.getSignedAvatarUrl(
            userId: userId,
            accessToken: accessToken,
            fileName: fileName,
          );
          if (signedUrl != null && mounted) {
            setState(() {
              _signedAvatarUrl = signedUrl;
            });
          }
        }
      } catch (e) {
        debugPrint('Error fetching signed URL: $e');
      }
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProfileBloc, ProfileState>(
      listener: (context, state) {
        state.when(
          initial: () {},
          loading: () {
            setState(() {
              _isLoading = true;
              _isUploadingImage = false;
            });
          },
          uploadingImage: () {
            setState(() {
              _isUploadingImage = true;
              _isLoading = false;
            });
          },
          loaded: (profile) {
            setState(() {
              _isLoading = false;
              _isUploadingImage = false;
              _currentAvatarUrl = profile.avatarUrl;
              _selectedImage = null;
              _signedAvatarUrl = null;
            });
            _fetchSignedAvatarUrl();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                context.pop();
              }
            });
          },
          error: (message) {
            setState(() {
              _isLoading = false;
              _isUploadingImage = false;
              _selectedImage = null;
              _signedAvatarUrl = null;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error: $message'),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Edit Profile'),
          elevation: 0,
          backgroundColor: Colors.transparent,
        ),
        body: (_isLoading && !_isUploadingImage)
            ? const Center(child: CircularProgressIndicator())
            : _buildEditForm(),
      ),
    );
  }

  Widget _buildEditForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAvatarSection(),
          const SizedBox(height: 32),
          _buildTextField(
            controller: _firstNameController,
            label: 'First Name',
            icon: Icons.person,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _lastNameController,
            label: 'Last Name',
            icon: Icons.person_outline,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _emailController,
            label: 'Email',
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _phoneController,
            label: 'Phone Number',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _bioController,
            label: 'Bio',
            icon: Icons.info,
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          _buildGenderDropdown(),
          const SizedBox(height: 32),
          _buildSaveButton(),
        ],
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Column(
      children: [
        Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _isUploadingImage ? Colors.blue : Colors.grey.shade300,
                  width: 2,
                ),
              ),
              child: CircleAvatar(
                radius: 60,
                backgroundImage: _getAvatarImage(),
                backgroundColor: Colors.grey.shade100,
              ),
            ),
            if (_isUploadingImage)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                  ),
                ),
              ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: _isUploadingImage ? Colors.grey : Colors.blue,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    _isUploadingImage
                        ? Icons.hourglass_empty
                        : Icons.camera_alt,
                    color: Colors.white,
                    size: 20,
                  ),
                  onPressed: _isUploadingImage ? null : _handleAvatarChange,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          _isUploadingImage
              ? 'Uploading image...'
              : _selectedImage != null
              ? 'Image selected, tap save to upload'
              : 'Tap camera to change avatar',
          style: TextStyle(
            color: _isUploadingImage
                ? Colors.blue
                : _selectedImage != null
                ? Colors.green.shade700
                : Colors.grey[600],
            fontSize: 12,
            fontWeight: _selectedImage != null
                ? FontWeight.w500
                : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  ImageProvider _getAvatarImage() {
    try {
      if (_selectedImage != null) {
        debugPrint('✅ Showing selected image: ${_selectedImage!.name}');
        return MemoryImage(_selectedImage!.bytes);
      }

      if (_signedAvatarUrl != null && _signedAvatarUrl!.isNotEmpty) {
        debugPrint('✅ Showing signed avatar: $_signedAvatarUrl');
        return NetworkImage(_signedAvatarUrl!);
      }

      if (_currentAvatarUrl != null && _currentAvatarUrl!.isNotEmpty) {
        debugPrint('✅ Showing current avatar: $_currentAvatarUrl');
        final urlWithCacheBuster =
            '$_currentAvatarUrl?t=${DateTime.now().millisecondsSinceEpoch}';
        return NetworkImage(urlWithCacheBuster);
      }

      final initials =
          '${widget.profile.firstName.isNotEmpty ? widget.profile.firstName[0] : 'U'}${widget.profile.lastName.isNotEmpty ? widget.profile.lastName[0] : 'U'}';
      final placeholderUrl =
          'https://via.placeholder.com/120x120/4285F4/FFFFFF?text=$initials';
      debugPrint('✅ Showing placeholder: placeholder');
      return NetworkImage(placeholderUrl);
    } catch (e) {
      debugPrint('❌ Error getting avatar image: $e');
      return const NetworkImage(
        'https://via.placeholder.com/120x120/4285F4/FFFFFF?text=U',
      );
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.blue, width: 2),
        ),
      ),
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: InputDecoration(
        labelText: 'Gender',
        prefixIcon: const Icon(Icons.people),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.blue, width: 2),
        ),
      ),
      items: ['male', 'female', 'other'].map((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value.toUpperCase()),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedGender = newValue;
        });
      },
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: (_isLoading || _isUploadingImage) ? null : _saveChanges,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isLoading || _isUploadingImage
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Save Changes',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  void _handleAvatarChange() async {
    try {
      ImageSource? source;

      if (kIsWeb) {
        source = ImageSource.gallery;
      } else {
        source = await UniversalImagePicker.showImageSourceDialog(context);
      }

      if (source != null) {
        debugPrint('Image source selected: $source');
        final result = await UniversalImagePicker.pickImage(source: source);

        if (result != null) {
          debugPrint(
            'Image picked: ${result.name}, size: ${result.bytes.length}',
          );
          setState(() {
            _selectedImage = result;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Image selected. Tap "Save Changes" to upload and update profile.',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          debugPrint('No image selected');
        }
      }
    } catch (e) {
      debugPrint('Error selecting image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _saveChanges() async {
    if (_firstNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('First name is required'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_lastNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Last name is required'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_emailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Email is required'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!RegExp(
      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
    ).hasMatch(_emailController.text.trim())) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid email address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    debugPrint('Validation passed');

    if (_selectedImage != null) {
      debugPrint('Image selected, uploading image first');
      await _uploadImageAndUpdateProfile();
    } else {
      debugPrint('No image selected, updating profile directly');
      _updateProfile();
    }
  }

  Future<void> _uploadImageAndUpdateProfile() async {
    try {
      setState(() {
        _isUploadingImage = true;
      });

      final loginModel = await HiveUserService().getCurrentUser();
      final userId = loginModel?.id;
      final accessToken = loginModel?.accessToken ?? '';

      if (userId == null) {
        throw Exception('User ID not found');
      }

      debugPrint('🔄 Starting image upload...');

      final uploadedUrl = await ProfileDetailsService.uploadAvatarFromBytes(
        userId: userId,
        accessToken: accessToken,
        imageBytes: _selectedImage!.bytes,
        fileName: _selectedImage!.name,
      );

      if (uploadedUrl != null) {
        debugPrint('✅ Image uploaded successfully: $uploadedUrl');

        // Fetch signed URL for the uploaded image
        final fileName = uploadedUrl.split('/').last;
        final signedUrl = await ProfileDetailsService.getSignedAvatarUrl(
          userId: userId,
          accessToken: accessToken,
          fileName: fileName,
        );

        setState(() {
          _currentAvatarUrl = uploadedUrl;
          _signedAvatarUrl = signedUrl ?? uploadedUrl;
          _isUploadingImage = false;
        });

        _updateProfile();
      } else {
        throw Exception('Failed to upload image');
      }
    } catch (e) {
      debugPrint('❌ Error uploading image: $e');
      setState(() {
        _isUploadingImage = false;
        _selectedImage = null;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error uploading image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _updateProfile() {
    debugPrint('🔄 Updating profile with current data');
    context.read<ProfileBloc>().add(
      ProfileEvent.updateRequested(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        bio: _bioController.text.trim().isEmpty
            ? null
            : _bioController.text.trim(),
        gender: _selectedGender,
        avatarUrl: _currentAvatarUrl,
      ),
    );
  }
}

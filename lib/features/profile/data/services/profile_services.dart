import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';

import '../../../../core/urls/api_config.dart';
import '../../../profile/domain/entities/user_profile.dart';
import 'universal_image_picker.dart';

class ProfileDetailsService {
  static const String bucketName = 'image-bucket';
  static const String avatarsFolderPath = 'avatars';

  static Future<UserProfile?> getProfileDetails({
    required String partyId,
    required String accessToken,
    required String tenantCode,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.getProfileDetails}',
      );

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Authorization': 'Bearer $accessToken',
            },
            body: jsonEncode({'party_id': partyId, 'tenant_code': tenantCode}),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded is Map && decoded['status'] == 'success') {
          final data = Map<String, dynamic>.from(decoded['data']);
          return UserProfile(
            id: partyId,
            firstName: data['first_name'] ?? '',
            lastName: data['last_name'] ?? '',
            email: data['email'] ?? '',
            phone: data['phone'],
            avatarUrl: data['avatar_url'],
            gender: data['gender'],
            bio: data['bio'],
            assignedGamesCount: data['assigned_games_count'] ?? 0,
            gamesPlayedCount: data['games_played_count'] ?? 0,
            rewardsReceived: data['rewards_recieved'] ?? 0,
            levelsCompleted: data['levels_completed'] ?? 0,
          );
        }
      }

      debugPrint('Profile API failed: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('Profile API Exception: $e');
      return null;
    }
  }

  static Future<bool> updateProfileDetails({
    required String userId,
    required String accessToken,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? gender,
    String? email,
    String? bio,
    String? phoneNumber,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.updateProfileDetails}',
      );

      final Map<String, dynamic> requestBody = {'user_id': userId};

      if (avatarUrl != null) requestBody['avatar_url'] = avatarUrl;
      if (firstName != null) requestBody['first_name'] = firstName;
      if (lastName != null) requestBody['last_name'] = lastName;
      if (gender != null) requestBody['gender'] = gender;
      if (email != null) requestBody['email'] = email;
      if (bio != null) requestBody['bio'] = bio;
      if (phoneNumber != null) requestBody['phone_number'] = phoneNumber;

      debugPrint('Update Profile Request: ${jsonEncode(requestBody)}');

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Authorization': 'Bearer $accessToken',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(const Duration(seconds: 15));

      debugPrint('Update Profile Response Status: ${response.statusCode}');
      debugPrint('Update Profile Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);

        if (decoded is Map && decoded['status'] == 'success') {
          debugPrint('Profile updated successfully');
          return true;
        } else {
          debugPrint('Profile update failed: ${decoded.toString()}');
          return false;
        }
      } else {
        debugPrint('Profile update API failed: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Profile update API Exception: $e');
      return false;
    }
  }

  /// Upload image to Supabase Storage using bytes (works for both web and mobile)
  /// Images will be stored in the avatars folder
  static Future<String?> uploadAvatarFromBytes({
    required String userId,
    required String accessToken,
    required Uint8List imageBytes,
    required String fileName,
  }) async {
    try {
      // Create a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension =
          fileName.toLowerCase().endsWith('.jpg') ||
              fileName.toLowerCase().endsWith('.jpeg')
          ? 'jpg'
          : fileName.toLowerCase().endsWith('.png')
          ? 'png'
          : 'jpg';

      final cleanFileName = 'avatar_${userId}_$timestamp.$fileExtension';

      final fullFilePath = '$avatarsFolderPath/$cleanFileName';

      debugPrint('Uploading avatar with path: $fullFilePath');
      debugPrint('Image size: ${imageBytes.length} bytes');

      final uploadUrl = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/storage/v1/object/$bucketName/$fullFilePath',
      );

      debugPrint('Upload URL: $uploadUrl');

      final uploadResponse = await http
          .post(
            uploadUrl,
            headers: {
              'Authorization': 'Bearer $accessToken',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Content-Type': fileExtension == 'png'
                  ? 'image/png'
                  : 'image/jpeg',
              'Cache-Control': '3600',
              'x-upsert': 'true',
            },
            body: imageBytes,
          )
          .timeout(const Duration(seconds: 30));

      debugPrint('Upload Response Status: ${uploadResponse.statusCode}');
      debugPrint('Upload Response Body: ${uploadResponse.body}');

      if (uploadResponse.statusCode == 200 ||
          uploadResponse.statusCode == 201) {
        final publicUrl =
            '${dotenv.env['SUPABASE_URL']!}/storage/v1/object/public/$bucketName/$fullFilePath';
        // '${dotenv.env['SUPABASE_URL']!}/storage/v1/object/sign/$bucketName/$fullFilePath';
        debugPrint('Avatar uploaded successfully: $publicUrl');
        return publicUrl;
      } else {
        debugPrint('Failed to upload avatar: ${uploadResponse.statusCode}');
        debugPrint('Upload error body: ${uploadResponse.body}');

        try {
          final errorData = jsonDecode(uploadResponse.body);
          debugPrint('Upload error details: $errorData');
        } catch (e) {
          debugPrint('Could not parse error response: $e');
        }

        return null;
      }
    } catch (e) {
      debugPrint('Avatar upload exception: $e');
      return null;
    }
  }

  static Future<String?> updateAvatarComplete({
    required String userId,
    required String accessToken,
    required ImageSource source,
  }) async {
    try {
      debugPrint('Starting complete avatar update for user: $userId');

      final imageResult = await UniversalImagePicker.pickImage(source: source);

      if (imageResult == null) {
        debugPrint('No image selected');
        return null;
      }

      debugPrint(
        'Image selected: ${imageResult.name}, size: ${imageResult.bytes.length}',
      );

      const maxSize = 5 * 1024 * 1024; // 5MB
      if (imageResult.bytes.length > maxSize) {
        debugPrint('Image too large: ${imageResult.bytes.length} bytes');
        throw Exception(
          'Image size too large. Please select an image under 5MB.',
        );
      }

      final avatarUrl = await uploadAvatarFromBytes(
        userId: userId,
        accessToken: accessToken,
        imageBytes: imageResult.bytes,
        fileName: imageResult.name,
      );

      if (avatarUrl == null) {
        debugPrint('Failed to upload avatar');
        return null;
      }

      debugPrint('Avatar uploaded, now updating profile with URL: $avatarUrl');

      // Update profile with new avatar URL
      final updateSuccess = await updateProfileDetails(
        userId: userId,
        accessToken: accessToken,
        avatarUrl: avatarUrl,
      );

      if (updateSuccess) {
        debugPrint('Avatar updated successfully in profile');
        return avatarUrl;
      } else {
        debugPrint('Failed to update profile with new avatar');
        return null;
      }
    } catch (e) {
      debugPrint('Complete avatar update exception: $e');
      return null;
    }
  }

  static Future<String?> getSignedAvatarUrl({
    required String userId,
    required String accessToken,
    required String fileName,
  }) async {
    try {
      final fullFilePath = '$avatarsFolderPath/$fileName';

      final signUrl = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/storage/v1/object/sign/$bucketName/$fullFilePath',
      );

      final signResponse = await http
          .post(
            signUrl,
            headers: {
              'Authorization': 'Bearer $accessToken',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'expiresIn': 3600, // 1 hour
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (signResponse.statusCode == 200) {
        final signData = jsonDecode(signResponse.body);
        final signedUrl = signData['signedURL'] as String?;
        if (signedUrl != null) {
          return '${dotenv.env['SUPABASE_URL']!}/storage/v1$signedUrl';
        }
      }

      debugPrint('Failed to get signed URL: ${signResponse.statusCode}');
      return null;
    } catch (e) {
      debugPrint('Error getting signed URL: $e');
      return null;
    }
  }

  /// Legacy methods for backward compatibility
  @deprecated
  static Future<String?> uploadAvatar({
    required String userId,
    required String accessToken,
    required XFile imageFile,
  }) async {
    final bytes = await imageFile.readAsBytes();
    return uploadAvatarFromBytes(
      userId: userId,
      accessToken: accessToken,
      imageBytes: bytes,
      fileName: imageFile.name,
    );
  }

  @deprecated
  static Future<XFile?> pickImage({required ImageSource source}) async {
    final result = await UniversalImagePicker.pickImage(source: source);
    if (result != null && result.path != null) {
      return XFile(result.path!);
    }
    return null;
  }
}

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

class UniversalImagePicker {
  static final ImagePicker _picker = ImagePicker();

  static Future<ImagePickerResult?> pickImage({
    required ImageSource source,
    double maxWidth = 1024,
    double maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    try {
      debugPrint('UniversalImagePicker: Starting image pick process');
      debugPrint('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      debugPrint('Source: $source');

      if (kIsWeb) {
        // Web implementation using file_picker
        return await _pickImageWeb();
      } else {
        // Mobile implementation using image_picker
        return await _pickImageMobile(
          source: source,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          imageQuality: imageQuality,
        );
      }
    } catch (e) {
      debugPrint('UniversalImagePicker: Error picking image: $e');
      return null;
    }
  }

  /// Web-specific image picker
  static Future<ImagePickerResult?> _pickImageWeb() async {
    try {
      debugPrint('UniversalImagePicker: Using web file picker');

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
        allowedExtensions: null, // Allow all image types
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.bytes != null) {
        final file = result.files.single;
        debugPrint('UniversalImagePicker: Web file selected: ${file.name}');
        debugPrint(
          'UniversalImagePicker: File size: ${file.bytes!.length} bytes',
        );
        debugPrint('UniversalImagePicker: File extension: ${file.extension}');

        // Validate file type
        final allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        final extension = file.extension?.toLowerCase();

        if (extension == null || !allowedExtensions.contains(extension)) {
          debugPrint('UniversalImagePicker: Invalid file type: $extension');
          throw Exception(
            'Please select a valid image file (jpg, png, gif, etc.)',
          );
        }

        // Validate file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.bytes!.length > maxSize) {
          debugPrint(
            'UniversalImagePicker: File too large: ${file.bytes!.length}',
          );
          throw Exception(
            'Image size too large. Please select an image under 10MB.',
          );
        }

        return ImagePickerResult(
          name: file.name,
          bytes: file.bytes!,
          path: null, // Web doesn't have file paths
        );
      } else {
        debugPrint(
          'UniversalImagePicker: No file selected or file has no bytes',
        );
        return null;
      }
    } catch (e) {
      debugPrint('UniversalImagePicker: Web image picking error: $e');
      rethrow; // Re-throw to handle in UI
    }
  }

  /// Mobile-specific image picker
  static Future<ImagePickerResult?> _pickImageMobile({
    required ImageSource source,
    double maxWidth = 1024,
    double maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    try {
      debugPrint('UniversalImagePicker: Using mobile image picker');
      debugPrint('UniversalImagePicker: Source: $source');

      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: imageQuality,
        requestFullMetadata: false,
      );

      if (image != null) {
        debugPrint(
          'UniversalImagePicker: Mobile image selected: ${image.name}',
        );
        debugPrint('UniversalImagePicker: Image path: ${image.path}');

        final bytes = await image.readAsBytes();
        debugPrint('UniversalImagePicker: Image size: ${bytes.length} bytes');

        // Validate file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (bytes.length > maxSize) {
          debugPrint('UniversalImagePicker: Image too large: ${bytes.length}');
          throw Exception(
            'Image size too large. Please select an image under 10MB.',
          );
        }

        return ImagePickerResult(
          name: image.name,
          bytes: bytes,
          path: image.path,
        );
      } else {
        debugPrint('UniversalImagePicker: No image selected');
        return null;
      }
    } catch (e) {
      debugPrint('UniversalImagePicker: Mobile image picking error: $e');
      rethrow;
    }
  }

  /// Show image source dialog for mobile
  static Future<ImageSource?> showImageSourceDialog(
    BuildContext context,
  ) async {
    if (kIsWeb) {
      debugPrint(
        'UniversalImagePicker: Web platform, returning gallery source',
      );
      return ImageSource.gallery;
    }

    debugPrint('UniversalImagePicker: Showing source selection dialog');

    return showModalBottomSheet<ImageSource>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Select Image Source',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => Navigator.pop(context, ImageSource.gallery),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Column(
                          children: [
                            Icon(
                              Icons.photo_library,
                              size: 40,
                              color: Colors.blue,
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Gallery',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: InkWell(
                      onTap: () => Navigator.pop(context, ImageSource.camera),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Column(
                          children: [
                            Icon(
                              Icons.camera_alt,
                              size: 40,
                              color: Colors.green,
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Camera',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static bool isValidImageFile(String fileName) {
    final allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    final extension = fileName.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }

  static String getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}

class ImagePickerResult {
  final String name;
  final Uint8List bytes;
  final String? path;

  ImagePickerResult({required this.name, required this.bytes, this.path});

  String get extension => name.split('.').last.toLowerCase();

  String get mimeType => UniversalImagePicker.getMimeType(name);

  bool get isValidImage => UniversalImagePicker.isValidImageFile(name);

  String get fileSizeString {
    final sizeInMB = bytes.length / (1024 * 1024);
    if (sizeInMB >= 1) {
      return '${sizeInMB.toStringAsFixed(1)} MB';
    } else {
      final sizeInKB = bytes.length / 1024;
      return '${sizeInKB.toStringAsFixed(1)} KB';
    }
  }

  @override
  String toString() {
    return 'ImagePickerResult(name: $name, size: $fileSizeString, extension: $extension)';
  }
}

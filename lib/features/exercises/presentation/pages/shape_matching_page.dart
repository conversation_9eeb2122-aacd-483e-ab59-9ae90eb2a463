import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum ShapeMatchingPhase { instructions, memorization, recall, results }

enum ShapeType { circle, square, triangle, star, rectangle, diamond, hexagon }

class ShapeMatchingPage extends StatefulWidget {
  const ShapeMatchingPage({super.key, this.extra});
  final Object? extra;

  @override
  State<ShapeMatchingPage> createState() => _ShapeMatchingPageState();
}

class _ShapeMatchingPageState extends State<ShapeMatchingPage> {
  // Exercise state
  ShapeMatchingPhase _currentPhase = ShapeMatchingPhase.instructions;
  final List<ShapeData> _targetShapes = [];
  final List<ShapeData> _allShapes = [];
  final Set<String> _selectedShapes = {};
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  String? _origin;

  @override
  void initState() {
    super.initState();
    _generateShapes();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) _origin = from;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _generateShapes() {
    final random = Random();
    _targetShapes.clear();
    _allShapes.clear();
    _selectedShapes.clear();

    // Generate 6 unique target shapes
    final availableShapes = ShapeType.values.toList()..shuffle(random);
    final availableColors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
      AppTheme.warningColor,
      AppTheme.successColor,
      AppTheme.errorColor,
    ]..shuffle(random);

    for (int i = 0; i < 6; i++) {
      _targetShapes.add(
        ShapeData(
          id: 'target_$i',
          type: availableShapes[i],
          color: availableColors[i],
        ),
      );
    }

    // Add 3 distractor shapes
    final distractorShapes = ShapeType.values.toList()..shuffle(random);
    final distractorColors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
    ]..shuffle(random);

    for (int i = 0; i < 3; i++) {
      _allShapes.add(
        ShapeData(
          id: 'distractor_$i',
          type: distractorShapes[i],
          color: distractorColors[i],
        ),
      );
    }

    // Combine and shuffle all shapes
    _allShapes.addAll(_targetShapes);
    _allShapes.shuffle(random);
  }

  void _startMemorization() {
    setState(() {
      _currentPhase = ShapeMatchingPhase.memorization;
      _timeRemaining = 6; // 6 seconds to memorize
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecall();
      }
    });
  }

  void _startRecall() {
    setState(() {
      _currentPhase = ShapeMatchingPhase.recall;
    });
  }

  void _toggleShapeSelection(String shapeId) {
    setState(() {
      if (_selectedShapes.contains(shapeId)) {
        _selectedShapes.remove(shapeId);
      } else {
        _selectedShapes.add(shapeId);
      }
    });
  }

  void _submitAnswer() {
    int correctSelections = 0;
    for (String shapeId in _selectedShapes) {
      if (_targetShapes.any((shape) => shape.id == shapeId)) {
        correctSelections++;
      }
    }

    // Calculate score (correct selections - incorrect selections, minimum 0)
    int incorrectSelections = _selectedShapes.length - correctSelections;
    _score = (correctSelections - incorrectSelections).clamp(0, 6);

    setState(() {
      _currentPhase = ShapeMatchingPhase.results;
    });
  }

  void _restartExercise() {
    _generateShapes();
    setState(() {
      _currentPhase = ShapeMatchingPhase.instructions;
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Shape Matching',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'home') {
                context.go(AppRouter.home);
              } else {
                context.go(AppRouter.exercises);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600.w),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case ShapeMatchingPhase.instructions:
        return _buildInstructionsPhase();
      case ShapeMatchingPhase.memorization:
        return _buildMemorizationPhase();
      case ShapeMatchingPhase.recall:
        return _buildRecallPhase();
      case ShapeMatchingPhase.results:
        return _buildResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.category,
            size: 60.sp,
            color: AppTheme.primaryColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Shape Matching Exercise',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                '1. You will see 6 colored shapes for 6 seconds\n'
                '2. Memorize their colors and types carefully\n'
                '3. Then select the shapes you remember\n'
                '4. Try to get all 6 correct!',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startMemorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemorizationPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Memorize these shapes: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: AppTheme.spacing16,
              mainAxisSpacing: AppTheme.spacing16,
              childAspectRatio: 1.0,
            ),
            itemCount: _targetShapes.length,
            itemBuilder: (context, index) {
              final shape = _targetShapes[index];
              return _buildShapeWidget(shape, size: 60.w);
            },
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Focus on the colors and shapes!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.quiz, color: AppTheme.primaryColor, size: 32.sp),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Select the shapes you remember',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Tap on the shapes you saw earlier (${_selectedShapes.length}/6 selected)',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        Expanded(
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
              childAspectRatio: 1.0,
            ),
            itemCount: _allShapes.length,
            itemBuilder: (context, index) {
              final shape = _allShapes[index];
              final isSelected = _selectedShapes.contains(shape.id);

              return GestureDetector(
                onTap: () => _toggleShapeSelection(shape.id),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(AppTheme.spacing8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withOpacity(0.2)
                        : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.dividerColor,
                      width: isSelected ? 3.w : 1.w,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Center(child: _buildShapeWidget(shape, size: 50.w)),
                ),
              );
            },
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _selectedShapes.isNotEmpty ? _submitAnswer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsPhase() {
    int correctSelections = 0;
    int incorrectSelections = 0;

    for (String shapeId in _selectedShapes) {
      if (_targetShapes.any((shape) => shape.id == shapeId)) {
        correctSelections++;
      } else {
        incorrectSelections++;
      }
    }

    int missedShapes = _targetShapes.length - correctSelections;
    double percentage = (correctSelections / _targetShapes.length) * 100;

    Color scoreColor;
    String scoreMessage;
    IconData scoreIcon;

    if (percentage >= 80) {
      scoreColor = AppTheme.successColor;
      scoreMessage = 'Excellent visual memory!';
      scoreIcon = Icons.star;
    } else if (percentage >= 60) {
      scoreColor = AppTheme.accentColor;
      scoreMessage = 'Good shape recognition!';
      scoreIcon = Icons.thumb_up;
    } else {
      scoreColor = AppTheme.warningColor;
      scoreMessage = 'Keep practicing!';
      scoreIcon = Icons.trending_up;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(scoreIcon, size: 60.sp, color: scoreColor),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Exercise Complete!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(color: scoreColor.withOpacity(0.3), width: 2.w),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              Divider(color: scoreColor.withOpacity(0.3)),
              SizedBox(height: AppTheme.spacing16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildScoreStat(
                    icon: Icons.check_circle,
                    label: 'Correct',
                    value: '$correctSelections',
                    color: AppTheme.successColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.cancel,
                    label: 'Incorrect',
                    value: '$incorrectSelections',
                    color: AppTheme.errorColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.help_outline,
                    label: 'Missed',
                    value: '$missedShapes',
                    color: AppTheme.warningColor,
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _restartExercise,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Try Again',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScoreStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.labelSmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildShapeWidget(ShapeData shape, {required double size}) {
    return CustomPaint(
      size: Size(size, size),
      painter: ShapePainter(shapeType: shape.type, color: shape.color),
    );
  }
}

class ShapeData {
  final String id;
  final ShapeType type;
  final Color color;

  ShapeData({required this.id, required this.type, required this.color});
}

class ShapePainter extends CustomPainter {
  final ShapeType shapeType;
  final Color color;

  ShapePainter({required this.shapeType, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 * 0.8;

    switch (shapeType) {
      case ShapeType.circle:
        canvas.drawCircle(center, radius, paint);
        break;
      case ShapeType.square:
        final rect = Rect.fromCenter(
          center: center,
          width: radius * 1.6,
          height: radius * 1.6,
        );
        canvas.drawRect(rect, paint);
        break;
      case ShapeType.triangle:
        final path = Path();
        path.moveTo(center.dx, center.dy - radius);
        path.lineTo(center.dx - radius * 0.866, center.dy + radius * 0.5);
        path.lineTo(center.dx + radius * 0.866, center.dy + radius * 0.5);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case ShapeType.star:
        _drawStar(canvas, center, radius, paint);
        break;
      case ShapeType.rectangle:
        final rect = Rect.fromCenter(
          center: center,
          width: radius * 2,
          height: radius * 1.2,
        );
        canvas.drawRect(rect, paint);
        break;
      case ShapeType.diamond:
        final path = Path();
        path.moveTo(center.dx, center.dy - radius);
        path.lineTo(center.dx + radius, center.dy);
        path.lineTo(center.dx, center.dy + radius);
        path.lineTo(center.dx - radius, center.dy);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case ShapeType.hexagon:
        _drawHexagon(canvas, center, radius, paint);
        break;
    }
  }

  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    final outerRadius = radius;
    final innerRadius = radius * 0.4;

    for (int i = 0; i < 10; i++) {
      final angle = (i * pi / 5) - pi / 2;
      final currentRadius = i.isEven ? outerRadius : innerRadius;
      final x = center.dx + currentRadius * cos(angle);
      final y = center.dy + currentRadius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawHexagon(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = (i * pi / 3) - pi / 2;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

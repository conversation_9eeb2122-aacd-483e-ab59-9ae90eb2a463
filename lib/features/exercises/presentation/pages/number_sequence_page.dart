import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum SequencePhase { instructions, memorization, recall, results }

class NumberSequencePage extends StatefulWidget {
  const NumberSequencePage({super.key, this.extra});
  final Object? extra;

  @override
  State<NumberSequencePage> createState() => _NumberSequencePageState();
}

class _NumberSequencePageState extends State<NumberSequencePage> {
  // Exercise state
  SequencePhase _currentPhase = SequencePhase.instructions;
  List<int> _targetSequence = [];
  final List<int> _userSequence = [];
  List<int> _availableNumbers = [];
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  String? _origin;

  @override
  void initState() {
    super.initState();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) _origin = from;
    }
    _generateSequence();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _generateSequence() {
    final random = Random();
    _targetSequence.clear();
    _userSequence.clear();
    _availableNumbers.clear();

    // Generate a sequence of 5 unique numbers (1-9)
    final numbers = List.generate(9, (index) => index + 1);
    numbers.shuffle(random);
    _targetSequence = numbers.take(5).toList();
    _availableNumbers = List.from(_targetSequence)..shuffle(random);
  }

  void _startMemorization() {
    setState(() {
      _currentPhase = SequencePhase.memorization;
      _timeRemaining = 7; // 7 seconds to memorize
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecall();
      }
    });
  }

  void _startRecall() {
    setState(() {
      _currentPhase = SequencePhase.recall;
    });
  }

  void _addNumberToSequence(int number) {
    if (_userSequence.length < _targetSequence.length) {
      setState(() {
        _userSequence.add(number);
        _availableNumbers.remove(number);
      });
    }
  }

  void _removeLastNumber() {
    if (_userSequence.isNotEmpty) {
      setState(() {
        final removedNumber = _userSequence.removeLast();
        _availableNumbers.add(removedNumber);
        _availableNumbers.sort();
      });
    }
  }

  void _submitAnswer() {
    int correctPositions = 0;
    for (
      int i = 0;
      i < _userSequence.length && i < _targetSequence.length;
      i++
    ) {
      if (_userSequence[i] == _targetSequence[i]) {
        correctPositions++;
      }
    }

    _score = correctPositions;

    setState(() {
      _currentPhase = SequencePhase.results;
    });
  }

  void _restartExercise() {
    _generateSequence();
    setState(() {
      _currentPhase = SequencePhase.instructions;
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Number Sequence Recall',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.accentColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop, otherwise navigate appropriately
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'home') {
                context.go(AppRouter.home);
              } else {
                context.go(AppRouter.exercises);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600.w),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case SequencePhase.instructions:
        return _buildInstructionsPhase();
      case SequencePhase.memorization:
        return _buildMemorizationPhase();
      case SequencePhase.recall:
        return _buildRecallPhase();
      case SequencePhase.results:
        return _buildResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.format_list_numbered,
            size: 60.sp,
            color: AppTheme.accentColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Number Sequence Recall',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                '1. You will see a sequence of 5 numbers for 7 seconds\n'
                '2. Memorize the order carefully\n'
                '3. Then recreate the sequence in the correct order\n'
                '4. Try to get the exact sequence!',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startMemorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemorizationPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Memorize this sequence: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: AppTheme.spacing12,
            runSpacing: AppTheme.spacing12,
            children: _targetSequence.asMap().entries.map((entry) {
              final index = entry.key;
              final number = entry.value;
              return Container(
                width: 55.w,
                height: 55.h,
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  border: Border.all(color: AppTheme.accentColor, width: 2.w),
                ),
                child: Center(
                  child: ResponsiveText(
                    '$number',
                    style: AppTheme.headlineMedium.copyWith(
                      fontWeight: FontWeight.w800,
                      color: AppTheme.accentColor,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.warningColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Remember the exact order of these numbers!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.quiz, color: AppTheme.accentColor, size: 32.sp),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Recreate the sequence',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Tap numbers in the correct order (${_userSequence.length}/${_targetSequence.length})',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        // User's sequence display
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(color: AppTheme.dividerColor, width: 1.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                'Your Sequence:',
                style: AppTheme.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing12),
              Column(
                children: [
                  Wrap(
                    spacing: AppTheme.spacing8,
                    runSpacing: AppTheme.spacing8,
                    children: List.generate(5, (index) {
                      if (index < _userSequence.length) {
                        return Container(
                          width: 45.w,
                          height: 45.h,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(
                              AppTheme.radiusMedium,
                            ),
                            border: Border.all(
                              color: AppTheme.primaryColor,
                              width: 2.w,
                            ),
                          ),
                          child: Center(
                            child: ResponsiveText(
                              '${_userSequence[index]}',
                              style: AppTheme.titleMedium.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ),
                        );
                      } else {
                        return Container(
                          width: 45.w,
                          height: 45.h,
                          decoration: BoxDecoration(
                            color: AppTheme.dividerColor.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(
                              AppTheme.radiusMedium,
                            ),
                            border: Border.all(
                              color: AppTheme.dividerColor,
                              width: 1.w,
                            ),
                          ),
                          child: Center(
                            child: ResponsiveText(
                              '?',
                              style: AppTheme.titleMedium.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppTheme.textTertiary,
                              ),
                            ),
                          ),
                        );
                      }
                    }),
                  ),
                  if (_userSequence.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: AppTheme.spacing8),
                      child: TextButton.icon(
                        onPressed: _removeLastNumber,
                        icon: Icon(
                          Icons.backspace,
                          color: AppTheme.errorColor,
                          size: 20.sp,
                        ),
                        label: ResponsiveText(
                          'Undo',
                          style: AppTheme.labelMedium.copyWith(
                            color: AppTheme.errorColor,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        // Available numbers
        ResponsiveText(
          'Available Numbers:',
          style: AppTheme.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        Wrap(
          spacing: AppTheme.spacing12,
          runSpacing: AppTheme.spacing12,
          children: _availableNumbers.map((number) {
            return GestureDetector(
              onTap: () => _addNumberToSequence(number),
              child: Container(
                width: 55.w,
                height: 55.h,
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  border: Border.all(color: AppTheme.accentColor, width: 2.w),
                ),
                child: Center(
                  child: ResponsiveText(
                    '$number',
                    style: AppTheme.titleLarge.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.accentColor,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const Spacer(),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _userSequence.length == _targetSequence.length
                ? _submitAnswer
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Sequence',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsPhase() {
    double percentage = (_score / _targetSequence.length) * 100;

    Color scoreColor;
    String scoreMessage;
    IconData scoreIcon;

    if (percentage >= 80) {
      scoreColor = AppTheme.successColor;
      scoreMessage = 'Perfect sequence!';
      scoreIcon = Icons.star;
    } else if (percentage >= 60) {
      scoreColor = AppTheme.accentColor;
      scoreMessage = 'Good memory!';
      scoreIcon = Icons.thumb_up;
    } else {
      scoreColor = AppTheme.warningColor;
      scoreMessage = 'Keep practicing!';
      scoreIcon = Icons.trending_up;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(scoreIcon, size: 60.sp, color: scoreColor),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Exercise Complete!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(color: scoreColor.withOpacity(0.3), width: 2.w),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              ResponsiveText(
                'You got $_score out of ${_targetSequence.length} positions correct',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        _buildSequenceComparison(),
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _restartExercise,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Try Again',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSequenceComparison() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.dividerColor, width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Sequence Comparison',
            style: AppTheme.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          // Target sequence
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                'Correct Sequence:',
                style: AppTheme.labelMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              Wrap(
                spacing: AppTheme.spacing8,
                runSpacing: AppTheme.spacing8,
                children: _targetSequence.map((number) {
                  return Container(
                    width: 35.w,
                    height: 35.h,
                    decoration: BoxDecoration(
                      color: AppTheme.successColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                      border: Border.all(
                        color: AppTheme.successColor,
                        width: 1.w,
                      ),
                    ),
                    child: Center(
                      child: ResponsiveText(
                        '$number',
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.successColor,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          // User sequence
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                'Your Answer:',
                style: AppTheme.labelMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              Wrap(
                spacing: AppTheme.spacing8,
                runSpacing: AppTheme.spacing8,
                children: _userSequence.asMap().entries.map((entry) {
                  final index = entry.key;
                  final number = entry.value;
                  final isCorrect =
                      index < _targetSequence.length &&
                      _targetSequence[index] == number;

                  return Container(
                    width: 35.w,
                    height: 35.h,
                    decoration: BoxDecoration(
                      color: isCorrect
                          ? AppTheme.successColor.withOpacity(0.1)
                          : AppTheme.errorColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                      border: Border.all(
                        color: isCorrect
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        width: 1.w,
                      ),
                    ),
                    child: Center(
                      child: ResponsiveText(
                        '$number',
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isCorrect
                              ? AppTheme.successColor
                              : AppTheme.errorColor,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

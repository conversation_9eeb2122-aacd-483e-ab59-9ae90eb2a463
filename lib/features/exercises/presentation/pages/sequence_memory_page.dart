import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/constants/game_constants.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum SequenceMemoryPhase {
  instructions,
  display,
  recall,
  roundResults,
  finalResults,
}

class SequenceMemoryPage extends StatefulWidget {
  const SequenceMemoryPage({super.key, this.extra});
  final Object? extra;

  @override
  State<SequenceMemoryPage> createState() => _SequenceMemoryPageState();
}

class _SequenceMemoryPageState extends State<SequenceMemoryPage>
    with TickerProviderStateMixin {
  // Game state
  SequenceMemoryPhase _currentPhase = SequenceMemoryPhase.instructions;
  late String _gameTitle;
  late String _instructions;

  late int _targetScore;
  late int _rewardPoints;
  late String _difficulty;
  late int _totalRounds;
  late int _currentRound;
  late int _score;
  late bool _allRoundsPerfect;
  late bool _lastRoundPerfect;
  late int _lastCorrectPositions;
  late int _lastTotalPositions;
  // Sequence state
  int _sequenceLength = 1;
  late List<int> _targetSequence;
  late List<int> _playerSequence;
  late int _currentDisplayIndex;
  // Keep track of previously used sequence/pattern to avoid repetition
  List<int>? _prevSequence;
  int? _lastPatternCandidateIdx;
  String? _lastPatternType;
  late int _currentRecallIndex;
  late bool _isDisplaying;
  late bool _isRecalling;
  late int _timeRemaining;
  int _displayTimeMs = 1000;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Game configuration
  late String _gameType;
  late String _gameId;
  late String _levelId;
  late String _sessionId;
  late int _totalTime;
  String? _patternType; // from API level

  // Content mode
  String _contentMode =
      ''; // numbers | colors | symbols | letters | cards | MATCH_GAME | WORD_SEARCH
  List<String> _contentLabels = [];
  List<Map<String, dynamic>> _cardData = []; // For storing card information

  // Memory matching game variables (for MATCH_GAME mode)
  List<bool> _cardFlipped = []; // Track which cards are flipped
  List<bool> _cardMatched = []; // Track which cards are matched
  List<int> _flippedCards = []; // Currently flipped card indices (max 2)
  int _attempts = 0; // Number of flip attempts
  int _misses = 0; // Number of misses
  int _maxMisses = 0; // Maximum allowed misses
  bool _gameCompleted = false; // Track if game is finished
  String _levelVariant = 'standard'; // standard | challenging | perfect
  int _cardCount = 12; // Number of cards in grid

  // Timer
  Timer? _timer;

  // Word search variables
  List<List<String>> _wordSearchGrid = [];
  List<String> _wordsToFind = [];
  List<List<bool>> _selectedCells = [];
  List<List<bool>> _foundCells = [];
  final List<String> _foundWords = [];
  int _wordSearchScore = 0;
  int _wordSearchTimeLimit = 60;
  bool _wordSearchSubmitted = false;
  int? _dragStartRow;
  int? _dragStartCol;
  int? _dragEndRow;
  int? _dragEndCol;
  int _wordSearchGridSize = 12;
  String _wordSearchTheme = 'Simple Words';

  String? _origin;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(_scaleController);
    _initializeGame();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) _origin = from;
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _initializeGame() async {
    // Pick selected game by type from constants
    _gameType = 'sequence_memory';
    Map<String, dynamic> selectedGame;

    // Try to find by type first (e.g., 'sequence_memory')
    try {
      selectedGame = GameConstants.sequenceMemoryGames.firstWhere(
        (g) => (g['type'] as String) == _gameType,
      );
    } catch (e) {
      // If not found by type, try to find by name (e.g., 'Sequence Memory')
      try {
        selectedGame = GameConstants.sequenceMemoryGames.firstWhere(
          (g) => (g['name'] as String) == _gameType,
        );
      } catch (e) {
        // Default fallback
        selectedGame = const {
          'name': 'Sequence Memory Exercise',
          'difficulty': 'medium',
          'type': 'sequence_memory',
        };
      }
    }

    _gameTitle =
        (selectedGame['name'] as String?) ?? 'Sequence Memory Exercise';
    _difficulty = (selectedGame['difficulty'] as String?) ?? 'medium';

    final loadedGame = await HiveUserService().getAssignedGames();
    if (loadedGame != null) {
      setState(() {
        _instructions = loadedGame.instructions;
        _gameId = loadedGame.id;
        _targetScore = loadedGame.targetScore ?? 0;
        // Level-based setup
        _currentRound = loadedGame.level;
        _totalRounds = loadedGame.levels.length;
        final currentLevel = loadedGame.levels.firstWhere(
          (level) => level.levelNumber == _currentRound,
          orElse: () => loadedGame.levels.first,
        );
        _rewardPoints = currentLevel.rewards?.first.points ?? 0;
        _levelId = currentLevel.levelId;
        _timeRemaining = currentLevel.timer ?? 60;
        _totalTime = _timeRemaining;
        _patternType = (currentLevel.patternType ?? '').isEmpty
            ? null
            : currentLevel.patternType;
        // Initialize reward points from the current level, defaulting to 0
        _rewardPoints =
            (currentLevel.rewards != null && currentLevel.rewards!.isNotEmpty)
            ? (currentLevel.rewards![0].points ?? 0)
            : 0;

        // Keep _difficulty from constants selection
      });

      // Get game configuration from constants
      // Apply difficulty-based settings from current level
      try {
        final String? levelDifficulty = loadedGame.levels
            .firstWhere(
              (l) => l.levelNumber == _currentRound,
              orElse: () => loadedGame.levels.first,
            )
            .difficultyLevel;
        _applyDifficulty(levelDifficulty!);
      } catch (_) {
        // Fallback to selected game difficulty
        _applyDifficulty(_difficulty);
      }
    }

    // Decide content mode from API level patternType if available; otherwise fallback to selected game type
    String? typeFromApi = _patternType;
    final String? type = typeFromApi; // Force word search mode for testing
    // (typeFromApi ?? (selectedGame['type'] as String?) ?? 'sequence_memory');
    if (type == 'SEQUENCE_NUMBER') {
      _contentMode = 'numbers';
    } else if (type == 'CARD_SEQUENCE') {
      _contentMode = 'cards';
    } else if (type == 'card_sequence' || type == 'card') {
      _contentMode = 'card';
    } else if (type == 'Object match' || type == 'match_game') {
      _contentMode = 'MATCH_GAME';
    } else if (type == 'word_search') {
      _contentMode = 'WORD_SEARCH';
    } else if (type == 'sound_sequence') {
      // Not implemented: fall back to symbols
      _contentMode = 'symbols';
    } else {
      _contentMode = 'numbers';
    }

    _buildContentLabels();

    _initializeSequence();

    // Initialize memory matching game if in MATCH_GAME mode
    if (_contentMode == 'MATCH_GAME') {
      _setupMemoryGame();
      setState(() {
        _currentPhase = SequenceMemoryPhase.recall;
      });
    } else if (_contentMode == 'WORD_SEARCH') {
      _setupWordSearchGame();
      setState(() {
        _currentPhase = SequenceMemoryPhase.recall;
      });
    }

    _fadeController.forward();
  }

  void _initializeSequence() {
    _targetSequence = [];
    _playerSequence = [];
    _currentDisplayIndex = 0;
    _currentRecallIndex = 0;
    _isDisplaying = false;
    _isRecalling = false;
    _score = 0;
    _allRoundsPerfect = true;
    _lastRoundPerfect = false;
    _lastCorrectPositions = 0;
    _lastTotalPositions = 0;
    _currentPhase = SequenceMemoryPhase.instructions;
  }

  void _generateSequence() {
    _targetSequence.clear();
    final random = Random();

    // Try to pick a meaningful pattern from constants based on API patternType and difficulty
    List<Map<String, dynamic>> candidates = GameConstants
        .sequenceMemoryQuestions
        .where((q) {
          final matchesType = _patternType != null
              ? (q['type'] == _patternType)
              : true;
          final matchesDifficulty = q['difficulty'] == _difficulty;
          return matchesType && matchesDifficulty;
        })
        .toList();

    List<int>? patternSeq;
    if (candidates.isNotEmpty) {
      // Try to pick a candidate different from the last used candidate
      int attempts = 0;
      int chosenIdx = random.nextInt(candidates.length);
      while (candidates.length > 1 &&
          _lastPatternCandidateIdx != null &&
          chosenIdx == _lastPatternCandidateIdx &&
          attempts < 5) {
        chosenIdx = random.nextInt(candidates.length);
        attempts++;
      }
      final chosen = candidates[chosenIdx];
      final seq = chosen['sequence'] as List<dynamic>?;
      if (seq != null && seq.isNotEmpty) {
        final ints = seq.whereType<int>().map((e) => (e) % 12).toList();
        if (ints.isNotEmpty) {
          // Fit to desired sequence length by cycling or trimming
          patternSeq = List<int>.generate(
            _sequenceLength,
            (i) => ints[i % ints.length],
          );
          // If pattern is identical to previous, rotate it to differ
          if (_prevSequence != null &&
              _prevSequence!.length == patternSeq.length &&
              List<int>.from(patternSeq).toString() ==
                  _prevSequence!.toString()) {
            patternSeq = List<int>.generate(
              _sequenceLength,
              (i) => ints[(i + 1) % ints.length],
            );
          }
          _lastPatternCandidateIdx = chosenIdx;
        }
      }
    }

    if (patternSeq != null) {
      _targetSequence.addAll(patternSeq);
    } else {
      // Fallback: random sequence over grid indices (0-11)
      for (int i = 0; i < _sequenceLength; i++) {
        int next = random.nextInt(12);
        // Avoid immediate duplicates and triple repeats
        if (_targetSequence.isNotEmpty && next == _targetSequence.last) {
          int alt = random.nextInt(12);
          int guard = 0;
          while (alt == _targetSequence.last && guard < 10) {
            alt = random.nextInt(12);
            guard++;
          }
          next = alt;
        }
        if (_targetSequence.length >= 2 &&
            next == _targetSequence[_targetSequence.length - 1] &&
            next == _targetSequence[_targetSequence.length - 2]) {
          // force a different value to break triple repeats
          int alt = random.nextInt(12);
          int guard = 0;
          while (alt == next && guard < 12) {
            alt = random.nextInt(12);
            guard++;
          }
          next = alt;
        }
        _targetSequence.add(next);
      }
    }

    // Ensure no immediate duplicates and no triple repeats even for pattern-based sequences
    for (int i = 1; i < _targetSequence.length; i++) {
      if (_targetSequence[i] == _targetSequence[i - 1]) {
        int alt = random.nextInt(12);
        int guard = 0;
        while (alt == _targetSequence[i - 1] && guard < 12) {
          alt = random.nextInt(12);
          guard++;
        }
        _targetSequence[i] = alt;
      }
      if (i >= 2 &&
          _targetSequence[i] == _targetSequence[i - 1] &&
          _targetSequence[i] == _targetSequence[i - 2]) {
        int alt = random.nextInt(12);
        int guard = 0;
        while (alt == _targetSequence[i] && guard < 12) {
          alt = random.nextInt(12);
          guard++;
        }
        _targetSequence[i] = alt;
      }
    }

    // If resulting sequence still matches previous, shuffle lightly by rotating
    if (_prevSequence != null &&
        _prevSequence!.length == _targetSequence.length &&
        List<int>.from(_targetSequence).toString() ==
            _prevSequence!.toString()) {
      if (_targetSequence.isNotEmpty) {
        final first = _targetSequence.removeAt(0);
        _targetSequence.add(first);
      }
    }

    // Remember for next round
    _prevSequence = List<int>.from(_targetSequence);

    // Lightly adapt per-item display time using level timer when available
    if (_totalTime > 0 && _sequenceLength > 0) {
      // Make the entire pattern display exactly within the API-provided time budget
      final perItemMs = (_totalTime * 1000) ~/ _sequenceLength;
      _displayTimeMs = perItemMs;
    }

    // Debug
    print(
      'Generated sequence (type=${_patternType ?? 'random'}): '
      '$_targetSequence, displayMs=$_displayTimeMs',
    );
  }

  void _startGame() async {
    await _startSession();
    print(_contentMode);
    if (_contentMode == 'MATCH_GAME') {
      // For memory matching game, skip display phase and go directly to recall
      setState(() {
        _currentPhase = SequenceMemoryPhase.recall;
      });
    } else {
      setState(() {
        _currentPhase = SequenceMemoryPhase.display;
      });
      _startDisplaySequence();
    }
  }

  Future<void> _startSession() async {
    final activeSessions = await AssignedGamesInfoService.getActiveSession(
      _gameId,
    );

    if (activeSessions != null &&
        activeSessions['details'] == "UNFINISHED_SESSION_FOUND") {
      final shouldContinue = await _showContinueDialog();
      if (shouldContinue == true) {
        setState(() {
          _sessionId = activeSessions['session_ids'][0];
        });
      } else {
        final config = await AssignedGamesInfoService.startGame(
          gameId: _gameId,
          levelId: _levelId,
        );
        setState(() {
          _sessionId = config?['session_id'];
        });
      }
    } else {
      final config = await AssignedGamesInfoService.startGame(
        gameId: _gameId,
        levelId: _levelId,
      );
      setState(() {
        _sessionId = config?['session_id'];
      });
    }
  }

  Future<bool> _showContinueDialog() async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Continue Previous Session?'),
              content: const Text(
                'You have an unfinished session. Would you like to continue?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text("No"),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text("Yes"),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _startDisplaySequence() {
    _generateSequence();
    setState(() {
      _currentDisplayIndex = 0;
      _isDisplaying = true;
      _currentPhase = SequenceMemoryPhase.display;
    });

    _showNextSequenceItem();
  }

  void _showNextSequenceItem() {
    if (_currentDisplayIndex < _targetSequence.length) {
      setState(() {
        _currentDisplayIndex++;
      });

      // Show next item after configured display time
      Timer(Duration(milliseconds: _displayTimeMs), () {
        if (_isDisplaying && mounted) {
          _showNextSequenceItem();
        }
      });
    } else {
      // All items shown, move to recall immediately to honor total time budget
      if (mounted) {
        setState(() {
          _isDisplaying = false;
          _currentPhase = SequenceMemoryPhase.recall;
          _playerSequence.clear();
        });
      }
    }
  }

  void _onSequenceItemTap(int index) {
    if (_currentPhase == SequenceMemoryPhase.recall && !_isRecalling) {
      setState(() {
        // Allow undo of the last tapped item by tapping it again
        if (_playerSequence.isNotEmpty && _playerSequence.last == index) {
          _playerSequence.removeLast();
          if (_currentRecallIndex > 0) _currentRecallIndex--;
        } else if (_playerSequence.length < _sequenceLength) {
          _playerSequence.add(index);
          _currentRecallIndex++;
        }
      });
    }
  }

  void _submitSequence() {
    if (_playerSequence.length == _targetSequence.length) {
      _evaluateSequence();
    }
  }

  void _evaluateSequence() {
    int correctPositions = 0;
    for (int i = 0; i < _targetSequence.length; i++) {
      if (_playerSequence[i] == _targetSequence[i]) {
        correctPositions++;
      }
    }

    bool isPerfect = correctPositions == _targetSequence.length;

    setState(() {
      _lastCorrectPositions = correctPositions;
      _lastTotalPositions = _targetSequence.length;
      _lastRoundPerfect = isPerfect;
      if (!isPerfect) {
        _allRoundsPerfect = false;
      }
    });

    _showRoundResult();
  }

  void _showRoundResult() {
    setState(() {
      _currentPhase = SequenceMemoryPhase.roundResults;
    });
    _endCurrentLevel();
  }

  Future<void> _endCurrentLevel() async {
    print('DEBUG: _endCurrentLevel called');
    // End current level with score based on round success
    final loadedGame = await HiveUserService().getAssignedGames();
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == _currentRound,
      orElse: () => loadedGame.levels.first,
    );
    await AssignedGamesInfoService.endGame(
      gameId: _gameId,
      levelId: currentLevel!.levelId,
      sessionId: _sessionId,
      score: _lastRoundPerfect ? _targetScore.toString() : '0',
    );
    final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    if (games != null) {
      await HiveUserService().saveAssignedGames(games);
    }
    print('DEBUG: _endCurrentLevel completed');
  }

  Future<void> _saveGameResults() async {
    final config = await AssignedGamesInfoService.endGame(
      gameId: _gameId,
      levelId: _levelId,
      sessionId: _sessionId,
      score: _allRoundsPerfect ? _targetScore.toString() : '0',
    );
  }

  void _restartGame() {
    // _initializeGame();
    setState(() {
      _currentPhase = SequenceMemoryPhase.instructions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ResponsiveText(
              _gameTitle,
              style: AppTheme.headlineSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2),
          ],
        ),
        backgroundColor: AppTheme.secondaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'home') {
                context.go(AppRouter.home);
              } else {
                context.go(AppRouter.exercises);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 1000,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case SequenceMemoryPhase.instructions:
        return _buildInstructionsPhase();
      case SequenceMemoryPhase.display:
        return _buildDisplayPhase();
      case SequenceMemoryPhase.recall:
        return _buildRecallPhase();
      case SequenceMemoryPhase.roundResults:
        return _buildRoundResultsPhase();
      case SequenceMemoryPhase.finalResults:
        return _buildFinalResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.memory,
            size: 60.sp,
            color: AppTheme.secondaryColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          _gameTitle,
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                _instructions.trim().isEmpty
                    ? (_contentMode == 'MATCH_GAME'
                          ? 'Match pairs of items! Remember where you saw each one.\n'
                                'Tap cards to flip them and find matching pairs.'
                          : '1. Watch the sequence of lights\n'
                                '2. Remember the order\n'
                                '3. Tap the lights in the same order\n'
                                '4. Try to get the sequence correct!')
                    : _instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startGame,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDisplayPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.visibility,
                    color: AppTheme.primaryColor,
                    size: 24.sp,
                  ),
                  SizedBox(width: AppTheme.spacing8),

                  Text(
                    _contentMode == 'MATCH_GAME'
                        ? 'Find matching pairs'
                        : 'Watch the sequence',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(width: AppTheme.spacing8),
                  ResponsiveText(
                    'Level '
                    '${_totalRounds > 0 ? (((_currentRound - 1) % _totalRounds) + 1) : _currentRound}'
                    ' / '
                    '$_totalRounds'
                    ' • Time: '
                    '$_totalTime'
                    's',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing8),
              Text(
                'Step $_currentDisplayIndex of ${_targetSequence.length}',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        Container(
          width: 300.w,
          height: 300.h,
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _buildSequenceGrid(),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    print('DEBUG: _contentMode = $_contentMode'); // Debug print
    if (_contentMode == 'WORD_SEARCH') {
      print('DEBUG: Rendering word search grid'); // Debug print
      return _buildWordSearchGrid();
    }
    print('DEBUG: Rendering sequence memory UI'); // Debug print
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),

          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.quiz, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                _contentMode == 'MATCH_GAME'
                    ? 'Find matching pairs'
                    : 'Repeat the sequence',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        Container(
          width: 300.w,
          height: 300.h,
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _buildSequenceGrid(),
        ),
        SizedBox(height: AppTheme.spacing16),
        _contentMode == 'MATCH_GAME'
            ? _buildMemoryGameStats()
            : Container(
                padding: EdgeInsets.all(AppTheme.spacing12),
                decoration: BoxDecoration(
                  color: AppTheme.successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),
                child: ResponsiveText(
                  'Tapped: ${_playerSequence.length}/$_sequenceLength',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.successColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
        SizedBox(height: AppTheme.spacing16),
        if (_contentMode != 'MATCH_GAME')
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: (_playerSequence.length == _targetSequence.length)
                  ? _submitSequence
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.secondaryColor,
                padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
              ),
              child: ResponsiveText(
                'Submit Sequence',
                style: AppTheme.labelLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textOnPrimary,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSequenceGrid() {
    if (_contentMode == 'MATCH_GAME') {
      return _buildMemoryMatchGrid();
    } else if (_contentMode == 'WORD_SEARCH') {
      return _buildWordSearchGrid();
    }

    return GridView.builder(
      padding: EdgeInsets.all(4.w),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: 12,
      itemBuilder: (context, index) {
        bool isHighlighted = false;
        bool isSelected = false;

        if (_currentPhase == SequenceMemoryPhase.display) {
          // Show current item being displayed
          if (_currentDisplayIndex > 0 &&
              _currentDisplayIndex <= _targetSequence.length &&
              index == _targetSequence[_currentDisplayIndex - 1]) {
            isHighlighted = true;
          }
        } else if (_currentPhase == SequenceMemoryPhase.recall) {
          if (_playerSequence.contains(index)) {
            isSelected = true;
          }
        }

        // Determine visuals by content mode
        final String label = _contentLabels.isNotEmpty
            ? _contentLabels[index % _contentLabels.length]
            : '${index + 1}';
        Color? highlightColor;
        Color? textColor;
        if (_contentMode == 'colors') {
          highlightColor = _parseHexColor(label);
        } else if (_contentMode == 'cards') {
          // For cards, get the color from card data
          final cardIndex = index % _cardData.length;
          final cardColor = _cardData[cardIndex]['color'] as String;
          textColor = cardColor == 'red' ? Colors.red : Colors.black;
        }

        return GestureDetector(
          onTap: () => _onSequenceItemTap(index),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            decoration: BoxDecoration(
              color: isHighlighted
                  ? (_contentMode == 'colors' && highlightColor != null
                        ? highlightColor
                        : AppTheme.primaryColor)
                  : isSelected
                  ? AppTheme.successColor
                  : AppTheme.dividerColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(
                color: isHighlighted || isSelected
                    ? (isHighlighted &&
                              _contentMode == 'colors' &&
                              highlightColor != null
                          ? highlightColor
                          : AppTheme.primaryColor)
                    : AppTheme.dividerColor,
                width: isHighlighted || isSelected ? 2.w : 1.w,
              ),
              boxShadow: isHighlighted || isSelected
                  ? [
                      BoxShadow(
                        color:
                            (isHighlighted
                                    ? (_contentMode == 'colors' &&
                                              highlightColor != null
                                          ? highlightColor
                                          : AppTheme.primaryColor)
                                    : AppTheme.successColor)
                                .withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Center(
              child: _contentMode == 'colors'
                  ? const SizedBox.shrink()
                  : _contentMode == 'cards'
                  ? Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.w),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Text(
                        label,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w700,
                          color: textColor ?? AppTheme.textPrimary,
                        ),
                      ),
                    )
                  : Text(
                      label,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: isHighlighted || isSelected
                            ? Colors.white
                            : AppTheme.textPrimary,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  void _applyDifficulty(String difficulty) {
    final cfg = GameConstants.sequenceMemoryData[difficulty];
    if (cfg != null) {
      setState(() {
        _sequenceLength = (cfg['sequenceLength'] as int?) ?? _sequenceLength;
        _displayTimeMs = (cfg['displayTime'] as int?) ?? _displayTimeMs;
        // If API targetScore is missing, fall back to maxScore for this difficulty
        if (_targetScore == 0) {
          _targetScore = (cfg['maxScore'] as int?) ?? _targetScore;
        }
      });
    }
  }

  void _buildContentLabels() {
    if (_contentMode == 'MATCH_GAME') {
      // For memory matching game, labels are handled differently
      return;
    }

    List<String> pool;
    switch (_contentMode) {
      case 'colors':
        pool = GameConstants.sequenceColors;
        break;
      case 'symbols':
        pool = GameConstants.sequenceSymbols;
        break;
      case 'letters':
        pool = GameConstants.sequenceLetters;
        break;
      case 'cards':
        // For cards, we use the display property from card data
        _cardData = List.from(GameConstants.sequenceCards);
        pool = _cardData.map((card) => card['display'] as String).toList();
        break;
      case 'numbers':
      default:
        pool = GameConstants.sequenceNumbers;
        break;
    }
    // Ensure we have labels for 12 cells by cycling
    _contentLabels = pool.take(12).toList();
  }

  void _setupMemoryGame() {
    // Get memory game config from constants based on difficulty
    final config = GameConstants.memoryMatchingData[_difficulty];
    if (config != null) {
      _cardCount = config['cardCount'] as int;
      _maxMisses = config['maxMisses'] as int;
      _levelVariant = config['levelVariant'] as String;
    } else {
      // Fallback defaults
      _cardCount = 12;
      _levelVariant = 'standard';
      _maxMisses = 999;
    }

    // Load card data based on content mode
    _loadCardDataForContentMode();

    // Initialize game state
    _cardFlipped = List.filled(_cardCount, false);
    _cardMatched = List.filled(_cardCount, false);
    _flippedCards = [];
    _attempts = 0; // Number of flip attempts
    _misses = 0; // Number of misses
    _gameCompleted = false; // Track if game is finished

    // Generate matching pairs
    _generateMemoryPairs();

    // Start the countdown timer for memory matching game
    _startMemoryGameTimer();
  }

  void _setupWordSearchGame() {
    print('DEBUG: Setting up word search game');

    // Load word search data based on difficulty
    _loadWordSearchData();

    // Force clear all arrays
    _wordSearchGrid = [];
    _selectedCells = [];
    _foundCells = [];
    _foundWords.clear();

    // Reset drag positions
    _dragStartRow = -1;
    _dragStartCol = -1;
    _dragEndRow = -1;
    _dragEndCol = -1;

    print('DEBUG: Starting word search setup...');

    // Generate the grid
    _generateWordSearchGrid();

    // Initialize selection and found cell arrays after grid is generated
    final gridSize = _wordSearchGrid.length;
    _selectedCells = List.generate(
      gridSize,
      (i) => List.filled(gridSize, false),
    );
    _foundCells = List.generate(gridSize, (i) => List.filled(gridSize, false));

    print('DEBUG: Word search setup complete');
  }

  void _loadCardDataForContentMode() {
    final cardSet = GameConstants.memoryGameCards['animals'];
    if (cardSet != null) {
      _cardData = List.from(cardSet);
    } else {
      _cardData = [];
    }
  }

  void _generateMemoryPairs() {
    final random = Random();
    final pairCount = _cardCount ~/ 2;

    List<int> cardIndices = [];
    for (int i = 0; i < pairCount && i < _cardData.length; i++) {
      cardIndices.add(i);
      cardIndices.add(i);
    }

    cardIndices.shuffle(random);
    _targetSequence = cardIndices.take(_cardCount).toList();
  }

  void _onMemoryCardTap(int index) {
    if (_gameCompleted || _cardMatched[index] || _flippedCards.length >= 2)
      return;

    setState(() {
      _attempts++;
      _cardFlipped[index] = true;
      _flippedCards.add(index);

      if (_flippedCards.length == 2) {
        final firstCardIndex = _flippedCards[0];
        final secondCardIndex = _flippedCards[1];
        final firstCardDataIndex = _targetSequence[firstCardIndex];
        final secondCardDataIndex = _targetSequence[secondCardIndex];

        if (firstCardDataIndex == secondCardDataIndex) {
          _cardMatched[firstCardIndex] = true;
          _cardMatched[secondCardIndex] = true;
          _flippedCards.clear();

          final totalMatched = _cardMatched.where((matched) => matched).length;
          print(
            'DEBUG: Memory game - Total matched: $totalMatched, Card count: $_cardCount',
          );
          if (totalMatched == _cardCount) {
            print(
              'DEBUG: All pairs matched! Game completed - showing submit button',
            );
            _gameCompleted = true;
            // Don't auto-submit, just mark as completed to show submit button
          }
        } else {
          _misses++;
          Timer(const Duration(milliseconds: 1000), () {
            if (mounted) {
              setState(() {
                _cardFlipped[firstCardIndex] = false;
                _cardFlipped[secondCardIndex] = false;
                _flippedCards.clear();
              });
            }
          });
        }
      }
    });
  }

  Widget _buildMemoryMatchGrid() {
    final crossAxisCount = _cardCount == 12 ? 4 : 6;

    return GridView.builder(
      padding: EdgeInsets.all(4.w),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: _cardCount,
      itemBuilder: (context, index) {
        if (index >= _cardFlipped.length ||
            index >= _cardMatched.length ||
            index >= _targetSequence.length ||
            _cardData.isEmpty) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: Center(child: Text('?')),
          );
        }

        final bool isFlipped = _cardFlipped[index];
        final bool isMatched = _cardMatched[index];
        final cardDataIndex = _targetSequence[index];

        if (cardDataIndex >= _cardData.length) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: Center(child: Text('?')),
          );
        }

        final cardData = _cardData[cardDataIndex];
        final String label = cardData['display'] as String;

        return GestureDetector(
          onTap: () => _onMemoryCardTap(index),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: isMatched
                  ? AppTheme.successColor.withOpacity(0.3)
                  : isFlipped
                  ? Colors.white
                  : AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(
                color: isMatched
                    ? AppTheme.successColor
                    : AppTheme.primaryColor,
                width: isMatched || isFlipped ? 2.w : 1.w,
              ),
            ),
            child: Center(
              child: isFlipped || isMatched
                  ? Text(
                      label,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    )
                  : Icon(Icons.help_outline, color: Colors.white, size: 24.sp),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMemoryGameStats() {
    final pairsFound = _cardMatched.where((matched) => matched).length ~/ 2;
    final totalPairs = _cardCount ~/ 2;

    return Column(
      children: [
        // Timer display
        Container(
          padding: EdgeInsets.all(AppTheme.spacing12),
          decoration: BoxDecoration(
            color: _timeRemaining <= 10
                ? AppTheme.warningColor.withOpacity(0.1)
                : AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(
              color: _timeRemaining <= 10
                  ? AppTheme.warningColor.withOpacity(0.3)
                  : AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.timer,
                color: _timeRemaining <= 10
                    ? AppTheme.warningColor
                    : AppTheme.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: AppTheme.spacing8),
              Text(
                'Time: ${_formatTime(_timeRemaining)}',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: _timeRemaining <= 10
                      ? AppTheme.warningColor
                      : AppTheme.primaryColor,
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        // Game stats
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStatCard(
              icon: Icons.touch_app,
              label: 'Attempts',
              value: '$_attempts',
              color: AppTheme.primaryColor,
            ),
            _buildStatCard(
              icon: Icons.close,
              label: 'Misses',
              value: '$_misses',
              color: AppTheme.warningColor,
            ),
            _buildStatCard(
              icon: Icons.check_circle,
              label: 'Pairs',
              value: '$pairsFound/$totalPairs',
              color: AppTheme.successColor,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        ResponsiveText(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color? _parseHexColor(String hex) {
    try {
      String cleaned = hex.replaceAll('#', '');
      if (cleaned.length == 6) {
        cleaned = 'FF$cleaned';
      }
      return Color(int.parse('0x$cleaned'));
    } catch (_) {
      return null;
    }
  }

  Widget _buildWordSearchGrid() {
    print('DEBUG: _buildWordSearchGrid called');
    print('DEBUG: Current grid size: ${_wordSearchGrid.length}');

    // Only generate grid if it doesn't exist or is empty
    if (_wordSearchGrid.isEmpty ||
        _wordSearchGrid.length != _wordSearchGridSize) {
      print('DEBUG: Generating new grid');
      _generateWordSearchGrid();

      // Initialize selection and found cell arrays only when grid is generated
      final gridSize = _wordSearchGridSize;
      _selectedCells = List.generate(
        gridSize,
        (i) => List.filled(gridSize, false),
      );
      _foundCells = List.generate(
        gridSize,
        (i) => List.filled(gridSize, false),
      );
    }

    // Verify grid is properly generated
    if (_wordSearchGrid.isEmpty ||
        _wordSearchGrid.length != _wordSearchGridSize) {
      print('ERROR: Grid generation failed');
      return SizedBox(
        height: 400.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, color: Colors.red, size: 48.sp),
              SizedBox(height: 16.h),
              Text('Grid generation failed', style: AppTheme.bodyMedium),
              SizedBox(height: 8.h),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _setupWordSearchGame();
                  });
                },
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    print(
      'DEBUG: Grid ready for display: ${_wordSearchGrid[0].take(3).join(' ')}',
    );

    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left side - Word Search Grid
            Expanded(
              flex: 3,
              child: Column(
                children: [
                  // Header with theme
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      vertical: 12.h,
                      horizontal: 16.w,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      _wordSearchTheme,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 1.2,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Word Search Grid
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.grey.shade300, width: 2),
                    ),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final availableWidth = constraints.maxWidth - 24;
                        final cellSize = availableWidth / _wordSearchGridSize;

                        return GestureDetector(
                          onPanStart: (details) => _onWordSearchDragStart(
                            details,
                            _wordSearchGridSize,
                            cellSize,
                          ),
                          onPanUpdate: (details) => _onWordSearchDragUpdate(
                            details,
                            _wordSearchGridSize,
                            cellSize,
                          ),
                          onPanEnd: (details) => _onWordSearchDragEnd(),
                          child: SizedBox(
                            width: availableWidth,
                            height: availableWidth,
                            child: GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: _wordSearchGridSize,
                                    crossAxisSpacing: 2,
                                    mainAxisSpacing: 2,
                                    childAspectRatio: 1,
                                  ),
                              itemCount:
                                  _wordSearchGridSize *
                                  _wordSearchGridSize, // _wordSearchGridSize x _wordSearchGridSize grid
                              itemBuilder: (context, index) {
                                final row = index ~/ _wordSearchGridSize;
                                final col = index % _wordSearchGridSize;

                                // Get the letter for this cell
                                String cellLetter = 'X'; // Default fallback
                                if (row < _wordSearchGrid.length &&
                                    col < _wordSearchGrid[row].length) {
                                  cellLetter = _wordSearchGrid[row][col];
                                }

                                return Container(
                                  decoration: BoxDecoration(
                                    color: _getWordSearchCellColor(row, col),
                                    borderRadius: BorderRadius.circular(4.r),
                                    border:
                                        (_selectedCells.isNotEmpty &&
                                            row < _selectedCells.length &&
                                            col < _selectedCells[row].length &&
                                            _selectedCells[row][col])
                                        ? Border.all(
                                            color: Colors.blue.shade600,
                                            width: 2,
                                          )
                                        : null,
                                  ),
                                  child: Center(
                                    child: Text(
                                      cellLetter,
                                      style: TextStyle(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.w700,
                                        color:
                                            (_foundCells.isNotEmpty &&
                                                row < _foundCells.length &&
                                                col < _foundCells[row].length &&
                                                _foundCells[row][col])
                                            ? Colors.white
                                            : Colors.black87,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 20.w),

            // Right side - Word List
            Expanded(
              flex: 2,
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: Colors.orange.shade300, width: 2),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Words header
                    Text(
                      'WORDS',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.orange.shade800,
                        letterSpacing: 1.2,
                      ),
                    ),
                    SizedBox(height: 12.h),

                    // Word list
                    ..._wordsToFind.map((word) {
                      final isFound = _foundWords.contains(word);
                      return Container(
                        margin: EdgeInsets.only(bottom: 8.h),
                        padding: EdgeInsets.symmetric(
                          vertical: 8.h,
                          horizontal: 12.w,
                        ),
                        decoration: BoxDecoration(
                          color: isFound ? Colors.green.shade100 : Colors.white,
                          borderRadius: BorderRadius.circular(6.r),
                          border: Border.all(
                            color: isFound
                                ? Colors.green.shade400
                                : Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            if (isFound)
                              Icon(
                                Icons.check_circle,
                                color: Colors.green.shade600,
                                size: 16.sp,
                              ),
                            if (isFound) SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                word,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  color: isFound
                                      ? Colors.green.shade700
                                      : Colors.black87,
                                  decoration: isFound
                                      ? TextDecoration.lineThrough
                                      : null,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),

                    SizedBox(height: 16.h),

                    // Progress indicator
                    Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search,
                            color: Colors.blue.shade600,
                            size: 20.sp,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            '${_foundWords.length}/${_wordsToFind.length}',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 16.h),

                    // Submit button for word search
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            (_foundWords.length == _wordsToFind.length ||
                                _timeRemaining <= 0)
                            ? _submitWordSearch
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              (_foundWords.length == _wordsToFind.length ||
                                  _timeRemaining <= 0)
                              ? AppTheme.secondaryColor
                              : Colors.grey,
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                        ),
                        child: Text(
                          (_foundWords.length == _wordsToFind.length)
                              ? 'Submit Game'
                              : (_timeRemaining <= 0)
                              ? 'Time Up - Submit'
                              : 'Find all words to submit',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getWordSearchCellColor(int row, int col) {
    if (_selectedCells[row][col]) {
      return Colors.blue.shade600.withOpacity(0.2);
    } else if (_foundCells[row][col]) {
      return Colors.green.shade600.withOpacity(0.2);
    } else {
      return Colors.white;
    }
  }

  void _onWordSearchDragStart(
    DragStartDetails details,
    int gridSize,
    double cellSize,
  ) {
    final row = (details.localPosition.dy / cellSize).floor().clamp(
      0,
      gridSize - 1,
    );
    final col = (details.localPosition.dx / cellSize).floor().clamp(
      0,
      gridSize - 1,
    );

    setState(() {
      // Clear previous selection
      _selectedCells = List.generate(
        gridSize,
        (i) => List.filled(gridSize, false),
      );

      // Set drag start position
      _dragStartRow = row;
      _dragStartCol = col;
      _dragEndRow = row;
      _dragEndCol = col;

      // Mark starting cell as selected
      _selectedCells[row][col] = true;
    });
  }

  void _onWordSearchDragUpdate(
    DragUpdateDetails details,
    int gridSize,
    double cellSize,
  ) {
    final row = (details.localPosition.dy / cellSize).floor().clamp(
      0,
      gridSize - 1,
    );
    final col = (details.localPosition.dx / cellSize).floor().clamp(
      0,
      gridSize - 1,
    );

    if (_dragStartRow != null && _dragStartCol != null) {
      setState(() {
        // Clear previous selection
        _selectedCells = List.generate(
          gridSize,
          (i) => List.filled(gridSize, false),
        );

        // Update drag end position
        _dragEndRow = row;
        _dragEndCol = col;

        // Select cells in straight line from start to end
        _selectCellsInLine(_dragStartRow!, _dragStartCol!, row, col);
      });
    }
  }

  void _onWordSearchDragEnd() {
    if (_dragStartRow != null &&
        _dragStartCol != null &&
        _dragEndRow != null &&
        _dragEndCol != null) {
      final selectedWord = _getSelectedWord();
      print('DEBUG: Selected word: $selectedWord');

      if (selectedWord.isNotEmpty &&
          _wordsToFind.contains(selectedWord.toUpperCase())) {
        // Word found - mark cells as found and add to found words
        setState(() {
          _markFoundCells();
          if (!_foundWords.contains(selectedWord.toUpperCase())) {
            _foundWords.add(selectedWord.toUpperCase());
          }

          // Clear selection
          _selectedCells = List.generate(
            _wordSearchGrid.length,
            (i) => List.filled(_wordSearchGrid[0].length, false),
          );
        });

        // Check if all words are found
        if (_foundWords.length == _wordsToFind.length) {
          _completeWordSearch();
        }
      } else {
        // Word not found - just clear selection
        setState(() {
          _selectedCells = List.generate(
            _wordSearchGrid.length,
            (i) => List.filled(_wordSearchGrid[0].length, false),
          );
        });
      }
    }

    // Reset drag positions
    _dragStartRow = null;
    _dragStartCol = null;
    _dragEndRow = null;
    _dragEndCol = null;
  }

  void _selectCellsInLine(int startRow, int startCol, int endRow, int endCol) {
    final rowDiff = endRow - startRow;
    final colDiff = endCol - startCol;

    // Determine if this is a valid straight line (horizontal, vertical, or diagonal)
    if (rowDiff != 0 && colDiff != 0 && rowDiff.abs() != colDiff.abs()) {
      // Not a straight line - only select start cell
      _selectedCells[startRow][startCol] = true;
      return;
    }

    final steps = [
      rowDiff.abs(),
      colDiff.abs(),
    ].reduce((a, b) => a > b ? a : b);
    final rowStep = steps == 0 ? 0 : rowDiff ~/ steps;
    final colStep = steps == 0 ? 0 : colDiff ~/ steps;

    // Select all cells in the line
    for (int i = 0; i <= steps; i++) {
      final row = startRow + (rowStep * i);
      final col = startCol + (colStep * i);
      if (row >= 0 &&
          row < _selectedCells.length &&
          col >= 0 &&
          col < _selectedCells[0].length) {
        _selectedCells[row][col] = true;
      }
    }
  }

  String _getSelectedWord() {
    if (_dragStartRow == null ||
        _dragStartCol == null ||
        _dragEndRow == null ||
        _dragEndCol == null) {
      return '';
    }

    final rowDiff = _dragEndRow! - _dragStartRow!;
    final colDiff = _dragEndCol! - _dragStartCol!;

    // Check if it's a valid straight line
    if (rowDiff != 0 && colDiff != 0 && rowDiff.abs() != colDiff.abs()) {
      return '';
    }

    final steps = [
      rowDiff.abs(),
      colDiff.abs(),
    ].reduce((a, b) => a > b ? a : b);
    final rowStep = steps == 0 ? 0 : rowDiff ~/ steps;
    final colStep = steps == 0 ? 0 : colDiff ~/ steps;

    String word = '';
    for (int i = 0; i <= steps; i++) {
      final row = _dragStartRow! + (rowStep * i);
      final col = _dragStartCol! + (colStep * i);
      if (row >= 0 &&
          row < _wordSearchGrid.length &&
          col >= 0 &&
          col < _wordSearchGrid[0].length) {
        word += _wordSearchGrid[row][col];
      }
    }

    // Check both forward and reverse
    final reversedWord = word.split('').reversed.join('');
    if (_wordsToFind.contains(word.toUpperCase())) {
      return word.toUpperCase();
    } else if (_wordsToFind.contains(reversedWord.toUpperCase())) {
      return reversedWord.toUpperCase();
    }

    return '';
  }

  void _markFoundCells() {
    // Mark the currently selected cells as found
    for (int i = 0; i < _selectedCells.length; i++) {
      for (int j = 0; j < _selectedCells[0].length; j++) {
        if (_selectedCells[i][j]) {
          _foundCells[i][j] = true;
        }
      }
    }
  }

  void _completeWordSearch() {
    // _stopTimer();

    // Calculate score based on found words and time remaining
    final foundWordsScore = (_foundWords.length / _wordsToFind.length * 100)
        .round();
    final timeBonus = (_timeRemaining / _wordSearchTimeLimit * 20).round();
    _wordSearchScore = foundWordsScore + timeBonus;

    setState(() {
      _currentPhase = SequenceMemoryPhase.roundResults;
      _wordSearchSubmitted = true;
    });
  }

  void _submitWordSearch() {
    // Stop the timer
    _timer?.cancel();

    // Calculate score and set game state like other games
    final foundWordsScore = (_foundWords.length / _wordsToFind.length * 100)
        .round();
    final timeBonus = (_timeRemaining / _wordSearchTimeLimit * 20).round();
    _wordSearchScore = foundWordsScore + timeBonus;

    setState(() {
      _lastCorrectPositions = _foundWords.length;
      _lastTotalPositions = _wordsToFind.length;
      _lastRoundPerfect = _foundWords.length == _wordsToFind.length;
      if (!_lastRoundPerfect) {
        _allRoundsPerfect = false;
      }
      _wordSearchSubmitted = true;
    });

    _showRoundResult();
  }

  Widget _buildRoundResultsPhase() {
    final bool passedRound = _lastRoundPerfect;
    final bool isLastRound = _currentRound >= _totalRounds;

    // Calculate time taken for word search
    final timeTaken = _contentMode == 'WORD_SEARCH'
        ? _wordSearchTimeLimit - _timeRemaining
        : _totalTime - _timeRemaining;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: _lastRoundPerfect
                ? AppTheme.successColor.withOpacity(0.1)
                : AppTheme.warningColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            border: Border.all(
              color: _lastRoundPerfect
                  ? AppTheme.successColor
                  : AppTheme.warningColor,
              width: 3.w,
            ),
          ),
          child: Icon(
            _lastRoundPerfect ? Icons.check_circle : Icons.info,
            size: 60.sp,
            color: _lastRoundPerfect
                ? AppTheme.successColor
                : AppTheme.warningColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          _lastRoundPerfect ? 'Excellent!' : 'Good Try!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: _lastRoundPerfect
                ? AppTheme.successColor
                : AppTheme.warningColor,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),

        // Word Search specific results
        if (_contentMode == 'WORD_SEARCH') ...[
          Container(
            padding: EdgeInsets.all(AppTheme.spacing16),
            margin: EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(
                color: AppTheme.primaryColor.withOpacity(0.3),
                width: 1.w,
              ),
            ),
            child: Column(
              children: [
                ResponsiveText(
                  'Word Search Results',
                  style: AppTheme.titleLarge.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing16),

                // Stats row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildResultStat(
                      icon: Icons.search,
                      label: 'Words Found',
                      value: '${_foundWords.length}/${_wordsToFind.length}',
                      color: AppTheme.successColor,
                    ),
                    _buildResultStat(
                      icon: Icons.timer,
                      label: 'Time Taken',
                      value: '${timeTaken}s',
                      color: AppTheme.primaryColor,
                    ),
                    _buildResultStat(
                      icon: Icons.category,
                      label: 'Theme',
                      value: _wordSearchTheme,
                      color: AppTheme.secondaryColor,
                    ),
                  ],
                ),

                SizedBox(height: AppTheme.spacing16),

                // Found words display
                if (_foundWords.isNotEmpty) ...[
                  ResponsiveText(
                    'Words You Found:',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    runSpacing: AppTheme.spacing8,
                    children: _foundWords
                        .map(
                          (word) => Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppTheme.spacing12,
                              vertical: AppTheme.spacing8,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.successColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusSmall,
                              ),
                              border: Border.all(
                                color: AppTheme.successColor,
                                width: 1.w,
                              ),
                            ),
                            child: ResponsiveText(
                              word,
                              style: AppTheme.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppTheme.successColor,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ],

                // Missed words display
                if (_foundWords.length < _wordsToFind.length) ...[
                  SizedBox(height: AppTheme.spacing12),
                  ResponsiveText(
                    'Words You Missed:',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    runSpacing: AppTheme.spacing8,
                    children: _wordsToFind
                        .where((word) => !_foundWords.contains(word))
                        .map(
                          (word) => Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppTheme.spacing12,
                              vertical: AppTheme.spacing8,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.warningColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                AppTheme.radiusSmall,
                              ),
                              border: Border.all(
                                color: AppTheme.warningColor,
                                width: 1.w,
                              ),
                            ),
                            child: ResponsiveText(
                              word,
                              style: AppTheme.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppTheme.warningColor,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ],
              ],
            ),
          ),
        ] else ...[
          // Regular sequence memory results
          ResponsiveText(
            'You got $_lastCorrectPositions out of $_lastTotalPositions correct',
            style: AppTheme.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing8),
          ResponsiveText(
            'Time taken: ${timeTaken}s',
            style: AppTheme.bodyLarge.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],

        SizedBox(height: AppTheme.spacing16),
        Container(
          child: ResponsiveText(
            _lastRoundPerfect && _currentRound == _totalRounds
                ? 'Score $_targetScore'
                : '',
            style: AppTheme.bodyMedium.copyWith(
              color: _lastRoundPerfect
                  ? AppTheme.successColor
                  : AppTheme.warningColor,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Container(
          child: ResponsiveText(
            _lastRoundPerfect ? 'You earned $_rewardPoints points!' : '',
            style: AppTheme.bodyMedium.copyWith(
              color: _lastRoundPerfect
                  ? AppTheme.successColor
                  : AppTheme.warningColor,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        SizedBox(height: AppTheme.spacing32),
        // Buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // Always go back to exercises list
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    context.go(AppRouter.exercises);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Back to Exercise',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  if (_lastRoundPerfect && _currentRound < _totalRounds) {
                    // Move to next level if passed and not on last round
                    _continueToNextRound();
                  } else if (!_lastRoundPerfect) {
                    // Retry same level if failed
                    _retrySameLevel();
                  } else {
                    // Restart exercise if passed last round
                    _restartExercise();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  _lastRoundPerfect && _currentRound < _totalRounds
                      ? 'Continue to Next Level'
                      : !_lastRoundPerfect
                      ? 'Retry'
                      : 'Restart',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildResultStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        ResponsiveText(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFinalResultsPhase() {
    final bool achievedPerfect = _allRoundsPerfect;
    final bool passedLastLevel =
        _lastRoundPerfect && _currentRound >= _totalRounds;
    final int finalScore = (achievedPerfect || passedLastLevel)
        ? _targetScore
        : 0;
    final int rewardPoints = achievedPerfect ? 0 : 0;

    Color finalScoreColor;
    String finalScoreMessage;
    IconData finalScoreIcon;

    if (achievedPerfect) {
      finalScoreColor = AppTheme.successColor;
      finalScoreMessage = 'Perfect sequence!';
      finalScoreIcon = Icons.star;
    } else if (passedLastLevel) {
      finalScoreColor = AppTheme.successColor;
      finalScoreMessage = 'Level completed!';
      finalScoreIcon = Icons.check_circle;
    } else {
      finalScoreColor = AppTheme.warningColor;
      finalScoreMessage = 'Keep practicing!';
      finalScoreIcon = Icons.trending_up;
    }

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        vertical: AppTheme.spacing24,
        horizontal: AppTheme.spacing16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            ),
            child: Icon(finalScoreIcon, size: 60.sp, color: finalScoreColor),
          ),
          SizedBox(height: AppTheme.spacing24),
          ResponsiveText(
            'Exercise Complete!',
            style: AppTheme.displaySmall.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing16),
          Container(
            padding: EdgeInsets.all(AppTheme.spacing24),
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              border: Border.all(
                color: finalScoreColor.withOpacity(0.3),
                width: 2.w,
              ),
            ),
            child: Column(
              children: [
                ResponsiveText(
                  'You got $finalScore points',
                  style: AppTheme.displayLarge.copyWith(
                    fontWeight: FontWeight.w800,
                    color: finalScoreColor,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  finalScoreMessage,
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                // Show reward if achieved perfect sequence
                if (achievedPerfect && rewardPoints > 0) ...[
                  SizedBox(height: AppTheme.spacing16),
                  Divider(color: finalScoreColor.withOpacity(0.3)),
                  SizedBox(height: AppTheme.spacing16),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing12,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.stars,
                          color: AppTheme.accentColor,
                          size: 20.sp,
                        ),
                        SizedBox(width: AppTheme.spacing8),
                        ResponsiveText(
                          'Bonus: $rewardPoints points',
                          style: AppTheme.labelMedium.copyWith(
                            color: AppTheme.accentColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                SizedBox(height: AppTheme.spacing16),
                // Always show target score and reward points summary
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing12,
                        vertical: AppTheme.spacing8,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusMedium,
                        ),
                        border: Border.all(
                          color: finalScoreColor.withOpacity(0.3),
                          width: 1.w,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.flag,
                            color: AppTheme.textSecondary,
                            size: 18.sp,
                          ),
                          SizedBox(width: AppTheme.spacing8),
                          ResponsiveText(
                            'Target Score: $_targetScore',
                            style: AppTheme.labelLarge.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: AppTheme.spacing12),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing12,
                        vertical: AppTheme.spacing8,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusMedium,
                        ),
                        border: Border.all(
                          color: finalScoreColor.withOpacity(0.3),
                          width: 1.w,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.stars,
                            color: AppTheme.accentColor,
                            size: 18.sp,
                          ),
                          SizedBox(width: AppTheme.spacing8),
                          ResponsiveText(
                            'Reward Points: $_rewardPoints',
                            style: AppTheme.labelLarge.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                Divider(color: finalScoreColor.withOpacity(0.3)),
                SizedBox(height: AppTheme.spacing16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildScoreStat(
                      icon: Icons.check_circle,
                      label: 'Correct',
                      value: '$_lastCorrectPositions',
                      color: AppTheme.successColor,
                    ),
                    _buildScoreStat(
                      icon: Icons.help_outline,
                      label: 'Total',
                      value: '$_lastTotalPositions',
                      color: AppTheme.warningColor,
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: AppTheme.spacing32),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _restartExercise,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  ),
                  child: ResponsiveText(
                    'Try Again',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textOnPrimary,
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    } else {
                      context.go(AppRouter.exercises);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  ),
                  child: ResponsiveText(
                    'Continue',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textOnPrimary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _startMemoryGameTimer() {
    _gameCompleted = false;
    _startGame();
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_timeRemaining > 0) {
          _timeRemaining--;
        } else {
          timer.cancel();
          if (!_gameCompleted) {
            _gameCompleted = true;
            _endCurrentLevel();
            _handleMemoryGameCompletion(false);
          }
        }
      });
    });
  }

  void _handleMemoryGameCompletion(bool completedSuccessfully) {
    // Stop the timer
    _timer?.cancel();

    final pairsFound = _cardMatched.where((matched) => matched).length ~/ 2;
    final totalPairs = _cardCount ~/ 2;

    setState(() {
      _lastCorrectPositions = pairsFound;
      _lastTotalPositions = totalPairs;
      _lastRoundPerfect = completedSuccessfully && pairsFound == totalPairs;
      if (!_lastRoundPerfect) {
        _allRoundsPerfect = false;
      }
      _gameCompleted = true;
    });

    _showRoundResult();
  }

  Widget _buildScoreStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        ResponsiveText(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _restartExercise() async {
    final loadedGame = await HiveUserService().getAssignedGames();
    final firstLevel = loadedGame?.levels.first;
    setState(() {
      _currentRound = 1;
      if (firstLevel != null) {
        _levelId = firstLevel.levelId;
        _timeRemaining = firstLevel.timer ?? 60;
        _difficulty = firstLevel.difficultyLevel ?? 'easy';
        _totalTime = _timeRemaining;
        _patternType = (firstLevel.patternType ?? '').isEmpty
            ? null
            : firstLevel.patternType;
        String? type = (_patternType ?? 'sequence_memory');
        // If API keeps returning the same pattern type, rotate between types to vary gameplay
        final List<String> supportedTypes = <String>[
          'number_sequence',
          'color_sequence',
          'letter_sequence',
          'symbol_sequence',
        ];
        if (_lastPatternType != null &&
            supportedTypes.contains(type) &&
            type == _lastPatternType) {
          // pick a different type deterministically by index rotation
          final idx = supportedTypes.indexOf(type);
          type = supportedTypes[(idx + 1) % supportedTypes.length];
          _patternType = type;
        }
        if (type == 'SEQUENCE_NUMBER') {
          _contentMode = 'numbers';
        } else if (type == 'Object match') {
          _contentMode = 'MATCH_GAME';
        } else if (type == 'CARD_SEQUENCE') {
          _contentMode = 'cards';
        } else if (type == 'symbol_sequence') {
          _contentMode = 'symbols';
        } else if (type == 'word_search') {
          _contentMode = 'WORD_SEARCH';
        } else {
          _contentMode = 'numbers'; // Default fallback
        }
        _buildContentLabels();
        _lastPatternType = type;
      }
      _allRoundsPerfect = true;
      _lastRoundPerfect = false;
      _lastCorrectPositions = 0;
      _lastTotalPositions = 0;
    });

    // Apply the new difficulty settings
    _applyDifficulty(_difficulty);

    _initializeSequence();

    // Skip instructions phase and go directly to display phase for next level
    setState(() {
      _currentPhase = SequenceMemoryPhase.instructions;
    });

    // Start the display phase immediately
    // _startDisplayPhase();
  }

  Future<void> _retrySameLevel() async {
    // Preserve current round and restart session for this level only
    final loadedGame = await HiveUserService().getAssignedGames();
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == _currentRound,
      orElse: () => throw Exception("Level not found"),
    );

    // Define difficulty progression order
    // final List<String> difficultyLevels = ['easy', 'medium', 'hard'];
    // final currentDifficultyIndex = difficultyLevels.indexOf(_difficulty);
    // final nextDifficultyIndex =
    //     (currentDifficultyIndex + 1) % difficultyLevels.length;
    // final nextDifficulty = difficultyLevels[nextDifficultyIndex];

    setState(() {
      _score = 0;
      _allRoundsPerfect = true;
      _lastRoundPerfect = false;
      _lastCorrectPositions = 0;
      _lastTotalPositions = 0;
      _gameCompleted = false;
      _difficulty = currentLevel?.difficultyLevel ?? 'easy';
      // Reset timer to original time for current level
      _timeRemaining = currentLevel?.timer ?? 60;
      _totalTime = _timeRemaining;

      // Reset pattern type and content mode
      _patternType = (currentLevel?.patternType ?? '').isEmpty
          ? null
          : currentLevel?.patternType;
      _rewardPoints = currentLevel?.rewards?.first.points ?? 0;

      // Set content mode based on pattern type (only number and card sequence)
      String? type = _patternType;
      if (type == 'SEQUENCE_NUMBER') {
        _contentMode = 'numbers';
      } else if (type == 'Object match') {
        _contentMode = 'MATCH_GAME';
      } else if (type == 'CARD_SEQUENCE') {
        _contentMode = 'cards';
      } else if (type == 'symbol_sequence') {
        _contentMode = 'symbols';
      } else if (type == 'word_search') {
        _contentMode = 'WORD_SEARCH';
      } else {
        _contentMode = 'numbers'; // Default fallback
      }
    });
    _applyDifficulty(_difficulty);
    print(_contentMode);
    // _initializeSequence();
    _buildContentLabels();
    _initializeSequence();

    // Go to instructions phase to restart the level
    setState(() {
      _currentPhase = SequenceMemoryPhase.display;
    });
    _startGame();
  }

  void _loadWordSearchData() {
    // Determine difficulty based on current round or default to easy
    String difficulty = 'easy';
    if (_currentRound <= 2) {
      difficulty = 'easy';
    } else if (_currentRound <= 4) {
      difficulty = 'medium';
    } else {
      difficulty = 'hard';
    }

    // Get available word sets for current difficulty
    final wordSets = GameConstants.wordSearchData[difficulty];

    if (wordSets != null && wordSets.isNotEmpty) {
      // Randomly select one word set from available sets
      final random = Random();
      final selectedIndex = random.nextInt(wordSets.length);
      final selectedWordSet = wordSets[selectedIndex];

      // Load selected word set data
      _wordSearchGridSize = selectedWordSet['gridSize'] ?? 8;
      _wordSearchTimeLimit = selectedWordSet['timeLimit'] ?? 120;
      _wordSearchTheme = selectedWordSet['theme'] ?? 'Words';
      _wordsToFind = List<String>.from(selectedWordSet['words'] ?? []);

      print(
        'DEBUG: Loaded word search data - Difficulty: $difficulty, Theme: $_wordSearchTheme, Grid: ${_wordSearchGridSize}x$_wordSearchGridSize, Words: $_wordsToFind',
      );
    } else {
      // Fallback to default values
      _wordSearchGridSize = 8;
      _wordSearchTimeLimit = 120;
      _wordSearchTheme = 'Simple Words';
      _wordsToFind = ['CAT', 'DOG', 'SUN', 'BIRD', 'FISH'];
      print('DEBUG: Using fallback word search data');
    }
  }

  void _generateWordSearchGrid() {
    final gridSize = _wordSearchGridSize;
    _wordSearchGrid = List.generate(
      gridSize,
      (index) => List.generate(gridSize, (index) => ''),
    );
    final random = Random();
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        _wordSearchGrid[i][j] = String.fromCharCode(65 + random.nextInt(26));
      }
    }
    for (String word in _wordsToFind) {
      _placeWordInGrid(word, random);
    }
  }

  void _placeWordInGrid(String word, Random random) {
    final gridSize = _wordSearchGridSize;
    final directions = [
      [0, 1], // horizontal
      [1, 0], // vertical
      [1, 1], // diagonal
      [1, -1], // anti-diagonal
    ];

    bool placed = false;
    int attempts = 0;

    while (!placed && attempts < 100) {
      final direction = directions[random.nextInt(directions.length)];
      final startRow = random.nextInt(gridSize);
      final startCol = random.nextInt(gridSize);

      if (_canPlaceWord(word, startRow, startCol, direction[0], direction[1])) {
        _placeWord(word, startRow, startCol, direction[0], direction[1]);
        placed = true;
      }
      attempts++;
    }
  }

  bool _canPlaceWord(
    String word,
    int startRow,
    int startCol,
    int rowDir,
    int colDir,
  ) {
    final gridSize = _wordSearchGridSize;

    for (int i = 0; i < word.length; i++) {
      final row = startRow + (i * rowDir);
      final col = startCol + (i * colDir);

      if (row < 0 || row >= gridSize || col < 0 || col >= gridSize) {
        return false;
      }
    }
    return true;
  }

  void _placeWord(
    String word,
    int startRow,
    int startCol,
    int rowDir,
    int colDir,
  ) {
    for (int i = 0; i < word.length; i++) {
      final row = startRow + (i * rowDir);
      final col = startCol + (i * colDir);
      _wordSearchGrid[row][col] = word[i];
    }
  }

  void _continueToNextRound() async {
    print(_currentRound);
    final loadedGame = await HiveUserService().getAssignedGames();
    final nextLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == _currentRound + 1,
      orElse: () => throw Exception("Level not found"),
    );
    print(nextLevel?.difficultyLevel ?? 'easy');
    // Define difficulty progression order
    // final List<String> difficultyLevels = ['easy', 'medium', 'hard'];
    // final currentDifficultyIndex = difficultyLevels.indexOf(_difficulty);
    // final nextDifficultyIndex =
    //     (currentDifficultyIndex + 1) % difficultyLevels.length;
    // final nextDifficulty = difficultyLevels[nextDifficultyIndex];

    setState(() {
      _currentRound++;
      // Update difficulty to next level
      // _difficulty = nextDifficulty;

      if (nextLevel != null) {
        _levelId = nextLevel.levelId;
        _timeRemaining = nextLevel.timer ?? 60;
        _difficulty = nextLevel.difficultyLevel ?? 'easy';
        _totalTime = _timeRemaining;
        _patternType = (nextLevel.patternType ?? '').isEmpty
            ? null
            : nextLevel.patternType;
        String? type = (_patternType ?? 'sequence_memory');
        // If API keeps returning the same pattern type, rotate between types to vary gameplay
        final List<String> supportedTypes = <String>[
          'SEQUENCE_NUMBER',
          'Object match',
          'Word search',
          'CARD_SEQUENCE',
        ];
        // if (_lastPatternType != null &&
        //     supportedTypes.contains(type) &&
        //     type == _lastPatternType) {
        //   // pick a different type deterministically by index rotation
        //   final idx = supportedTypes.indexOf(type);
        //   type = supportedTypes[(idx + 1) % supportedTypes.length];
        //   _patternType = type;
        // }
        print(_patternType);
        type = _patternType;
        if (type == 'SEQUENCE_NUMBER') {
          _contentMode = 'numbers';
        } else if (type == 'Object match') {
          _contentMode = 'MATCH_GAME';
        } else if (type == 'CARD_SEQUENCE') {
          _contentMode = 'cards';
        } else if (type == 'symbol_sequence') {
          _contentMode = 'symbols';
        } else if (type == 'word_search') {
          _contentMode = 'WORD_SEARCH';
        } else {
          _contentMode = 'numbers';
        }
        _buildContentLabels();
        _lastPatternType = type;
      }
      _allRoundsPerfect = true;
      _lastRoundPerfect = false;
      _lastCorrectPositions = 0;
      _lastTotalPositions = 0;
    });

    // Apply the new difficulty settings
    _applyDifficulty(_difficulty);
    print(_contentMode);
    _initializeSequence();

    // Skip instructions phase and go directly to display phase for next level
    setState(() {
      _currentPhase = SequenceMemoryPhase.display;
    });
    _startGame();
    // Start the display phase immediately
    // _startDisplayPhase();
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../data/models/game_category_model.dart';
import '../../domain/entities/exercise.dart';
import '../bloc/exercises/exercises_bloc.dart';

class ExercisesPage extends StatefulWidget {
  const ExercisesPage({super.key});

  @override
  State<ExercisesPage> createState() => _ExercisesPageState();
}

class _ExercisesPageState extends State<ExercisesPage> {
  @override
  void initState() {
    super.initState();
    context.read<ExercisesBloc>().add(const ExercisesEvent.loadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          ).createShader(bounds),
          child: ResponsiveText(
            'Cognitive Exercises',
            style: AppTheme.headlineSmall.copyWith(
              fontWeight: FontWeight.w800,
              color: Colors.white,
              letterSpacing: -0.5,
            ),
          ),
        ),
        backgroundColor: Colors.white.withValues(alpha: 0.95),
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      body: Container(
        decoration: _buildBackgroundGradient(),
        height: double.infinity,
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFF8FAFC), // Light gray-blue
          Color(0xFFE2E8F0), // Slightly darker gray-blue
          Color(0xFFF1F5F9), // Very light blue
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExercisesList(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: _buildExercisesList(),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Scrollbar(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: ResponsiveContainer(
                  maxWidth: 1200.w,
                  padding: EdgeInsets.all(AppTheme.spacing32),
                  child: _buildDesktopExercisesList(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildExercisesList() {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        return state.when(
          initial: () => const Center(child: CircularProgressIndicator()),
          loading: () => Center(
            child: CircularProgressIndicator(color: AppTheme.primaryColor),
          ),

          loaded:
              (
                exercises,
                filteredExercises,
                categories,
                currentFilter,
                difficultyFilter,
              ) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(),
                    SizedBox(height: AppTheme.spacing24),
                    _buildExerciseCategories(categories),
                    SizedBox(height: AppTheme.spacing24),
                    Expanded(child: _buildExerciseGrid(filteredExercises)),
                  ],
                );
              },
          exerciseInProgress: (exercise) => Center(
            child: ResponsiveText(
              'Exercise in progress: ${exercise.title}',
              style: AppTheme.bodyMedium,
            ),
          ),
          error: (message) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: AppTheme.textTertiary,
                ),
                SizedBox(height: AppTheme.spacing16),
                ResponsiveText(
                  'Unable to load exercises',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  message,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDesktopExercisesList() {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        return state.when(
          initial: () => const Center(child: CircularProgressIndicator()),
          loading: () => Center(
            child: CircularProgressIndicator(color: AppTheme.primaryColor),
          ),

          loaded:
              (
                exercises,
                filteredExercises,
                categories,
                currentFilter,
                difficultyFilter,
              ) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(),
                    SizedBox(height: AppTheme.spacing24),
                    _buildExerciseCategories(categories),
                    SizedBox(height: AppTheme.spacing24),
                    _buildExerciseGrid(filteredExercises),
                    SizedBox(
                      height: AppTheme.spacing48 * 2,
                    ), // Extra bottom padding for web scrolling
                  ],
                );
              },
          exerciseInProgress: (exercise) => Center(
            child: ResponsiveText(
              'Exercise in progress: ${exercise.title}',
              style: AppTheme.bodyMedium,
            ),
          ),
          error: (message) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: AppTheme.textTertiary,
                ),
                SizedBox(height: AppTheme.spacing16),
                ResponsiveText(
                  'Unable to load exercises',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  message,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          // Modern glassmorphism icon container
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.15),
                  AppTheme.secondaryColor.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Container(
              margin: EdgeInsets.all(AppTheme.spacing12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.psychology,
                color: AppTheme.primaryColor,
                size: 32.sp,
              ),
            ),
          ),
          SizedBox(width: AppTheme.spacing20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Enhanced title with gradient text
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                  ).createShader(bounds),
                  child: ResponsiveText(
                    'Train Your Brain',
                    style: AppTheme.titleLarge.copyWith(
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  'Choose from various cognitive exercises designed to improve memory, focus, and mental agility.',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                    height: 1.5,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCategories(List<GameCategoryModel> categories) {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        final currentFilter = state.maybeWhen(
          loaded: (_, __, ___, currentFilter, ____) => currentFilter,
          orElse: () => null,
        );

        // Ensure "All" category is first
        final sortedCategories = [...categories];
        final allCategoryIndex = sortedCategories.indexWhere(
          (cat) => cat.id == 'all',
        );
        if (allCategoryIndex > 0) {
          final allCategory = sortedCategories.removeAt(allCategoryIndex);
          sortedCategories.insert(0, allCategory);
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: sortedCategories.asMap().entries.map((entry) {
              final index = entry.key;
              final category = entry.value;
              final isSelected =
                  (category.id == 'all' && currentFilter == null) ||
                  (category.id == currentFilter);

              return Padding(
                padding: EdgeInsets.only(right: AppTheme.spacing12),
                child: _buildCategoryChip(
                  name: category.name,
                  icon: _getCategoryIconByIndex(index, category.iconName),
                  color: _getCategoryColorByIndex(index, category.colorCode),
                  isSelected: isSelected,
                  onTap: () => _onCategorySelected(category.id),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildCategoryChip({
    required String name,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: AppTheme.spacing20,
          vertical: AppTheme.spacing12,
        ),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [color, color.withValues(alpha: 0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.9),
                    Colors.white.withValues(alpha: 0.8),
                  ],
                ),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          border: Border.all(
            color: isSelected ? color : color.withValues(alpha: 0.3),
            width: isSelected ? 2.w : 1.5.w,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? color.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isSelected ? 12 : 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(AppTheme.spacing4),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withValues(alpha: 0.2)
                    : color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : color,
                size: 16.sp,
              ),
            ),
            SizedBox(width: AppTheme.spacing8),
            ResponsiveText(
              name,
              style: AppTheme.labelMedium.copyWith(
                color: isSelected ? Colors.white : color,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onCategorySelected(String categoryId) {
    context.read<ExercisesBloc>().add(
      ExercisesEvent.filterChanged(
        filterType: categoryId == 'all' ? null : categoryId,
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'Word Recall':
        return Icons.memory;
      case 'calculate':
        return Icons.calculate;
      case 'record_voice_over':
        return Icons.record_voice_over;
      default:
        return Icons.apps;
    }
  }

  IconData _getCategoryIconByIndex(int index, String? iconName) {
    final defaultIcons = [
      Icons.apps,
      Icons.memory,
      Icons.calculate,
      Icons.record_voice_over,
      Icons.pattern,
      Icons.psychology,
    ];

    // Try to get specific icon first
    if (iconName != null) {
      final specificIcon = _getCategoryIcon(iconName);
      if (specificIcon != Icons.apps) {
        return specificIcon;
      }
    }

    return defaultIcons[index % defaultIcons.length];
  }

  Color _getCategoryColorByIndex(int index, String? colorCode) {
    final defaultColors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
      AppTheme.warningColor,
      AppTheme.successColor,
      AppTheme.errorColor,
    ];

    if (colorCode != null && colorCode.isNotEmpty) {
      try {
        return Color(int.parse(colorCode.replaceFirst('#', '0xFF')));
      } catch (e) {
        print('Failed to parse color code: $colorCode');
      }
    }

    return defaultColors[index % defaultColors.length];
  }

  Color _getCategoryColor(String colorCode) {
    try {
      return Color(int.parse(colorCode.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor;
    }
  }

  Widget _buildExerciseGrid(List<Exercise> exercises) {
    if (exercises.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64.sp, color: AppTheme.textTertiary),
            SizedBox(height: AppTheme.spacing16),
            ResponsiveText(
              'No exercises found',
              style: AppTheme.titleMedium.copyWith(
                color: AppTheme.textTertiary,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Try selecting a different category or check back later',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (ResponsiveBreakpoints.isMobile(context)) {
      // Use ListView for mobile for better scrolling experience
      return ListView.builder(
        itemCount: exercises.length,
        itemBuilder: (context, index) {
          final exercise = exercises[index];
          return Padding(
            padding: EdgeInsets.only(bottom: AppTheme.spacing16),
            child: _buildExerciseCard(exercise),
          );
        },
      );
    } else {
      // Use GridView for tablet and desktop
      return GridView.builder(
        shrinkWrap: ResponsiveBreakpoints.isDesktop(context),
        physics: ResponsiveBreakpoints.isDesktop(context)
            ? const AlwaysScrollableScrollPhysics()
            : null,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: ResponsiveBreakpoints.isDesktop(context) ? 3 : 2,
          crossAxisSpacing: AppTheme.spacing16,
          mainAxisSpacing: AppTheme.spacing16,
          childAspectRatio: ResponsiveBreakpoints.isDesktop(context)
              ? 1.2
              : 1.1,
        ),
        itemCount: exercises.length,
        itemBuilder: (context, index) {
          final exercise = exercises[index];
          return _buildExerciseCard(exercise);
        },
      );
    }
  }

  Widget _buildExerciseCard(Exercise exercise) {
    final color = _getCategoryColorForExercise(
      exercise.exerciseType,
      categoryName: exercise.categoryName,
    );
    final isMobile = ResponsiveBreakpoints.isMobile(context);

    return GestureDetector(
      onTap: () => _navigateToExercise(exercise),
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withValues(alpha: 0.95),
              Colors.white.withValues(alpha: 0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isMobile
            ? _buildMobileCardContent(exercise, color)
            : _buildTabletDesktopCardContent(exercise, color),
      ),
    );
  }

  Widget _buildMobileCardContent(Exercise exercise, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          exercise.title,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
        ),
        // Game Type directly below title
        if (exercise.exerciseType.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(top: 2.h),
            child: ResponsiveText(
              _formatGameType(exercise.exerciseType),
              style: AppTheme.labelSmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          exercise.description,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: AppTheme.spacing8),
        Row(
          children: [
            if (exercise.categoryName != null &&
                exercise.categoryName!.isNotEmpty)
              _buildExerciseTag(
                icon: Icons.category,
                text: exercise.categoryName!,
                color: AppTheme.textTertiary,
              ),
            if (exercise.categoryName != null &&
                exercise.categoryName!.isNotEmpty &&
                exercise.assignedType != null &&
                exercise.assignedType!.isNotEmpty)
              SizedBox(width: AppTheme.spacing8),
            if (exercise.assignedType != null &&
                exercise.assignedType!.isNotEmpty)
              _buildExerciseTag(
                icon: Icons.assignment,
                text: _formatAssignedType(exercise.assignedType!),
                color: _getAssignedTypeColor(exercise.assignedType!),
              ),
            // Show score if available and there's space
            if (exercise.lastScore != null &&
                (exercise.categoryName == null ||
                    exercise.categoryName!.isEmpty) &&
                (exercise.assignedType == null ||
                    exercise.assignedType!.isEmpty)) ...[
              SizedBox(width: AppTheme.spacing8),
              _buildExerciseTag(
                icon: Icons.score,
                text: '${exercise.lastScore}%',
                color: _getScoreColor(exercise.lastScore!),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildTabletDesktopCardContent(Exercise exercise, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                child: exercise.iconPath.startsWith('http')
                    ? CachedNetworkImage(
                        imageUrl: exercise.iconPath,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Icon(
                          _getExerciseIcon(exercise.exerciseType),
                          color: color,
                          size: 24.sp,
                        ),
                        errorWidget: (context, url, error) => Icon(
                          _getExerciseIcon(exercise.exerciseType),
                          color: color,
                          size: 24.sp,
                        ),
                      )
                    : Icon(
                        _getExerciseIcon(exercise.exerciseType),
                        color: color,
                        size: 24.sp,
                      ),
              ),
            ),
            const Spacer(),
            // Game Type in top right corner
            if (exercise.exerciseType.isNotEmpty)
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: 4.h,
                ),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: color.withOpacity(0.3),
                    width: 0.5.w,
                  ),
                ),
                child: ResponsiveText(
                  _formatGameType(exercise.exerciseType),
                  style: AppTheme.labelSmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: AppTheme.spacing12),
        ResponsiveText(
          exercise.title,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          exercise.description,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: AppTheme.spacing8),
        Row(
          children: [
            Expanded(
              child: Wrap(
                spacing: AppTheme.spacing8,
                runSpacing: AppTheme.spacing4,
                children: [
                  if (exercise.categoryName != null &&
                      exercise.categoryName!.isNotEmpty)
                    _buildExerciseTag(
                      icon: Icons.category,
                      text: exercise.categoryName!,
                      color: AppTheme.textTertiary,
                    ),
                  if (exercise.assignedType != null &&
                      exercise.assignedType!.isNotEmpty)
                    _buildExerciseTag(
                      icon: Icons.assignment,
                      text: _formatAssignedType(exercise.assignedType!),
                      color: _getAssignedTypeColor(exercise.assignedType!),
                    ),
                ],
              ),
            ),
            SizedBox(width: AppTheme.spacing8),
            Icon(Icons.play_circle_filled, color: color, size: 32.sp),
          ],
        ),
      ],
    );
  }

  Widget _buildExerciseTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12.sp),
          SizedBox(width: 2.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Future<void> _navigateToExercise(Exercise exercise) async {
  //   final games = await AssignedGamesInfoService.loadGameConfig(exercise.id);
  //   await HiveUserService().saveAssignedGames(games!);
  //   switch (exercise.categoryName) {
  //     case 'Word Recall':
  //       context.go(AppRouter.wordRecall, extra: {'from': 'exercises'});
  //       break;
  //     case 'number_sequence':
  //       context.go(AppRouter.numberSequence, extra: {'from': 'exercises'});
  //       break;
  //     case 'shape_matching':
  //       context.go(AppRouter.shapeMatching, extra: {'from': 'exercises'});
  //       break;
  //     case 'Pattern Recognition':
  //       context.go(AppRouter.patternRecognition, extra: {'from': 'exercises'});
  //       break;
  //     case 'object_memory':
  //       context.go(AppRouter.objectMemory, extra: {'from': 'exercises'});
  //       break;
  //     case 'Sequence Memory':
  //       context.go(AppRouter.sequenceMemory, extra: {'from': 'exercises'});
  //       break;
  //     case 'Visual Memory':
  //       context.go(AppRouter.visualMemory, extra: {'from': 'exercises'});
  //       break;
  //     default:
  //       // For other exercises, show coming soon
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(
  //           content: Text('${exercise.title} coming soon!'),
  //           backgroundColor: AppTheme.accentColor,
  //         ),
  //       );
  //       break;
  //   }
  // }
  Future<void> _navigateToExercise(Exercise exercise) async {
    final games = await AssignedGamesInfoService.loadGameConfig(exercise.id);
    await HiveUserService().saveAssignedGames(games!);
    switch (exercise.categoryName) {
      case 'Word Recall':
        context.go(AppRouter.wordRecall, extra: {'from': 'exercises'});
        break;
      case 'number_sequence':
        context.go(AppRouter.numberSequence, extra: {'from': 'exercises'});
        break;
      case 'shape_matching':
        context.go(AppRouter.shapeMatching, extra: {'from': 'exercises'});
        break;
      case 'Pattern Recognition':
        context.go(AppRouter.patternRecognition, extra: {'from': 'exercises'});
        break;
      case 'object_memory':
        context.go(AppRouter.objectMemory, extra: {'from': 'exercises'});
        break;
      case 'Sequence Memory':
        context.go(AppRouter.sequenceMemory, extra: {'from': 'exercises'});
        break;
      case 'Visual Memory': // Update this case to pass the correct 'from' parameter
        context.go(AppRouter.visualMemory, extra: {'from': 'exercises'});
        break;
      default:
        // For other exercises, show coming soon
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${exercise.title} coming soon!'),
            backgroundColor: AppTheme.accentColor,
          ),
        );
        break;
    }
  }

  IconData _getExerciseIcon(String type) {
    switch (type) {
      case 'memory':
        return Icons.memory;
      case 'verbal':
        return Icons.record_voice_over;
      case 'pattern':
        return Icons.pattern;
      case 'logic':
        return Icons.calculate;
      case 'spatial':
        return Icons.rotate_right;
      default:
        return Icons.psychology;
    }
  }

  Color _getCategoryColorForExercise(
    String exerciseType, {
    String? categoryName,
  }) {
    if (categoryName != null && categoryName.isNotEmpty) {
      final categories = context.read<ExercisesBloc>().state.maybeWhen(
        loaded:
            (
              exercises,
              filteredExercises,
              categories,
              currentFilter,
              difficultyFilter,
            ) => categories,
        orElse: () => <GameCategoryModel>[],
      );

      final matchingCategoryIndex = categories.indexWhere(
        (cat) => cat.name.toLowerCase() == categoryName.toLowerCase(),
      );

      if (matchingCategoryIndex != -1) {
        final matchingCategory = categories[matchingCategoryIndex];
        return _getCategoryColorByIndex(
          matchingCategoryIndex,
          matchingCategory.colorCode,
        );
      }
    }

    // Fallback to default colors based on exercise type
    final defaultColors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
      AppTheme.warningColor,
      AppTheme.successColor,
      AppTheme.errorColor,
    ];

    switch (exerciseType.toLowerCase()) {
      case 'memory':
        return defaultColors[1];
      case 'verbal':
        return defaultColors[3];
      case 'pattern':
        return defaultColors[2];
      case 'logic':
        return defaultColors[2];
      case 'spatial':
        return defaultColors[4];
      default:
        return defaultColors[0];
    }
  }

  Color _getScoreColor(int score) {
    if (score >= 80) {
      return AppTheme.successColor;
    } else if (score >= 60) {
      return AppTheme.accentColor;
    } else {
      return AppTheme.warningColor;
    }
  }

  String _formatGameType(String gameType) {
    // Format game type for display
    if (gameType.isEmpty) return '';

    // Convert to title case and handle common abbreviations
    switch (gameType.toLowerCase()) {
      case 'memory':
        return 'Memory';
      case 'logic':
        return 'Logic';
      case 'verbal':
        return 'Verbal';
      case 'spatial':
        return 'Spatial';
      case 'pattern':
        return 'Pattern';
      default:
        // Capitalize first letter of each word
        return gameType
            .split('_')
            .map(
              (word) => word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                  : '',
            )
            .join(' ');
    }
  }

  String _formatAssignedType(String assignedType) {
    // Format assigned type for display
    if (assignedType.isEmpty) return '';

    switch (assignedType.toLowerCase()) {
      case 'mandatory':
        return 'Required';
      case 'optional':
        return 'Optional';
      case 'recommended':
        return 'Recommended';
      case 'bonus':
        return 'Bonus';
      default:
        // Capitalize first letter of each word
        return assignedType
            .split('_')
            .map(
              (word) => word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                  : '',
            )
            .join(' ');
    }
  }

  Color _getAssignedTypeColor(String assignedType) {
    // Return color based on assigned type
    switch (assignedType.toLowerCase()) {
      case 'mandatory':
      case 'required':
        return AppTheme.errorColor;
      case 'recommended':
        return AppTheme.warningColor;
      case 'optional':
        return AppTheme.accentColor;
      case 'bonus':
        return AppTheme.successColor;
      default:
        return AppTheme.textTertiary;
    }
  }
}

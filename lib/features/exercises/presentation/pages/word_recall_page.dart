import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/constants/game_constants.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum ExercisePhase {
  instructions,
  memorization,
  recall,
  roundResults,
  finalResults,
}

class RoundResult {
  final int round;
  final int score;
  final int correctSelections;
  final int incorrectSelections;
  final int missedWords;
  final double percentage;
  final List<String> targetWords;
  final Set<String> selectedWords;
  // OOO-only fields
  final String? explanation; // why the correct answer is right
  final String? correctAnswer;

  RoundResult({
    required this.round,
    required this.score,
    required this.correctSelections,
    required this.incorrectSelections,
    required this.missedWords,
    required this.percentage,
    required this.targetWords,
    required this.selectedWords,
    this.explanation,
    this.correctAnswer,
  });

  Color get scoreColor {
    if (percentage == 100) {
      return AppTheme.successColor;
    } else {
      return AppTheme.accentColor;
    }
  }

  String get scoreMessage {
    if (percentage == 100) {
      return 'Excellent memory!';
    } else {
      return 'Keep practicing!';
    }
  }

  IconData get scoreIcon {
    if (percentage == 100) {
      return Icons.star;
    } else {
      return Icons.trending_up;
    }
  }
}

class WordRecallPage extends StatefulWidget {
  const WordRecallPage({super.key, this.extra});
  final Object? extra;

  @override
  State<WordRecallPage> createState() => _WordRecallPageState();
}

class _WordRecallPageState extends State<WordRecallPage> {
  // Exercise state
  ExercisePhase _currentPhase = ExercisePhase.instructions;
  List<String> _targetWords = [];
  List<String> _allWords = [];
  final Set<String> _selectedWords = {};
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  int timer = 0;
  String _gameId = "";
  String _instructions = '';
  final int _minCorrect = 5;
  // Multi-round state
  final currentLevel = [];
  int _currentRound = 1;
  int _totalRounds = 1;
  int _currentLevelNo = 1;
  int _totalScore = 0;
  int _targetScore = 0;
  int _scoreEarned = 0;
  final int _currentReward = 0;
  String _gameType = 'MEMORY_RECALL';
  int _rewardPoints = 0;
  String _levelId = '';
  final List<RoundResult> _roundResults = [];
  String gameInstructions = '';
  // Word pools
  String? _sessionId;
  bool _isOddOneOut = false;
  bool _isScramble = false;
  bool _isCrossword = false;
  bool _isClassic = false;
  // Mode: derive OOO from game type
  // bool get _isOddOneOut => _gameType.toLowerCase() == 'ODD_ONE_OUT"';
  // bool get _isScramble => _gameType.toLowerCase() == 'WORD_SCRAMBLE';
  // bool get _isCrossword => _gameType.toLowerCase() == 'CROSS_WORD';
  // bool get _isClassic => !_isOddOneOut && !_isScramble && !_isCrossword;
  int _selectionLimit = 5;
  // OOO difficulty and question text
  String _oooDifficulty = 'easy';
  String _currentQuestion = '';
  String _currentReason = '';
  String _currentAnswer = '';
  int _oooRetryOffset = 0; // rotate questions on retry
  int _oooSessionOffset = 0; // randomize question starting point per session

  // Crossword-specific state (used when gameType == 'cross')
  int _gridSize = 0;
  List<List<String?>> _solutionGrid = [];
  List<List<String?>> _userGrid = [];
  List<Map<String, dynamic>> _crossWords = [];
  List<Map<String, dynamic>> _cluesAcross = [];
  List<Map<String, dynamic>> _cluesDown = [];
  int? _selectedRow;
  int? _selectedCol;
  String _selectedDirection = 'across';
  bool passed = false;
  bool _calculatingFinalResult = false;
  // Focus and controllers for auto-advance typing
  List<List<FocusNode?>> _focusNodes = [];
  List<List<TextEditingController?>> _textControllers = [];
  // Scramble-specific state
  List<String> _scrambledWords = [];
  final Map<int, String> _scrambleInputs = {};
  // Persistent controllers for scramble inputs
  List<TextEditingController> _scrambleControllers = [];
  String initialPattern = '';
  String? _origin;

  @override
  void initState() {
    super.initState();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) _origin = from;
    }
    _generateWords();
  }

  @override
  void dispose() {
    _timer?.cancel();
    // Dispose focus nodes and controllers
    for (final row in _focusNodes) {
      for (final n in row) {
        n?.dispose();
      }
    }
    for (final row in _textControllers) {
      for (final c in row) {
        c?.dispose();
      }
    }
    for (final c in _scrambleControllers) {
      c.dispose();
    }
    super.dispose();
  }

  Future<void> _generateWords({
    bool preserveCurrentRound = false,
    bool levelPassed = true,
  }) async {
    final random = Random();
    _targetWords.clear();
    _allWords.clear();
    _selectedWords.clear();
    _scrambledWords.clear();
    _scrambleInputs.clear();
    // Reset scramble controllers for a fresh round
    for (final c in _scrambleControllers) {
      c.dispose();
    }
    _scrambleControllers.clear();
    final loadedGame = await HiveUserService().getAssignedGames();

    gameInstructions = loadedGame?.instructions ?? '';
    if (!preserveCurrentRound) {
      _currentRound = loadedGame!.level;
      _currentLevelNo = loadedGame.gameStatus != 'completed'
          ? loadedGame.level
          : 1;
      _currentRound = _currentLevelNo;
    }
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == _currentRound,
      orElse: () => throw Exception("Level not found"),
    );
    final difficulty = currentLevel?.difficultyLevel;
    _oooDifficulty = currentLevel!.difficultyLevel!;
    print(currentLevel.timer);
    setState(() {
      initialPattern = loadedGame!.levels.first.patternType!;
      _instructions = loadedGame.instructions;
      _timeRemaining = currentLevel.timer!;
      _gameId = loadedGame.id;
      // _currentReward = currentLevel.rewards?[0].points ?? 0;
      _targetScore = loadedGame.targetScore ?? 0;
      _totalRounds = loadedGame.levels.length;
      _levelId = currentLevel.levelId;
      _rewardPoints = currentLevel.rewards?.first.points ?? 0;
      _gameType = currentLevel.patternType!;
      _isOddOneOut = currentLevel.patternType == 'ODD_ONE_OUT';
      _isScramble = currentLevel.patternType == 'WORD_SCRAMBLE';
      _isCrossword = currentLevel.patternType == 'CROSS_WORD';
      _isClassic = !_isOddOneOut && !_isScramble && !_isCrossword;
      passed = false;
      // _selectionLimit = currentLevel.patternType == 'ODD_ONE_OUT'
      //     ? 1
      //     : currentLevel.patternType == 'CROSS_WORD'
      //     ? 3
      //     : 5;
    });
    print(currentLevel.patternType);

    // int get _selectionLimit => _isOddOneOut ? 1 : _minCorrect;
    print(_isOddOneOut);
    // Branch: Crossword
    if (_isCrossword) {
      try {
        // Set difficulty from backend on first load
        if (!preserveCurrentRound) {
          _oooDifficulty = (difficulty?.toString().toLowerCase() ?? 'easy');
          if (!['easy', 'medium', 'hard'].contains(_oooDifficulty)) {
            _oooDifficulty = 'easy';
          }
        }

        // Load Crossword levels by difficulty; cycle through by round
        final List<dynamic> levels =
            (GameConstants.crossword['levels'][_oooDifficulty]
                as List<dynamic>);
        if (!preserveCurrentRound) {
          _oooSessionOffset = Random().nextInt(levels.length);
        }
        final idx =
            (_currentRound - 1 + _oooRetryOffset + _oooSessionOffset) %
            levels.length;
        final Map<String, dynamic> lvlCfg = Map<String, dynamic>.from(
          levels[idx] as Map,
        );
        final int gridSize = (lvlCfg['gridSize'] as num).toInt();
        final List<Map<String, dynamic>> words = (lvlCfg['words'] as List)
            .map((e) => Map<String, dynamic>.from(e as Map))
            .toList();

        // Build solution grid and user grid
        List<List<String?>> solution = List.generate(
          gridSize,
          (_) => List<String?>.filled(gridSize, null),
        );
        for (int n = 0; n < words.length; n++) {
          final w = words[n];
          final String word = w['word'].toString().toUpperCase();
          final String direction = w['direction'].toString().toLowerCase();
          final int r = (w['row'] as num).toInt();
          final int c = (w['col'] as num).toInt();
          print('Placing word: $word at ($r,$c) direction: $direction');
          for (int i = 0; i < word.length; i++) {
            final rr = direction == 'down' ? r + i : r;
            final cc = direction == 'across' ? c + i : c;
            if (rr >= 0 && rr < gridSize && cc >= 0 && cc < gridSize) {
              // Check for intersection conflicts
              if (solution[rr][cc] != null && solution[rr][cc] != word[i]) {
                print(
                  'ERROR: Intersection conflict at ($rr,$cc) - existing: ${solution[rr][cc]}, trying to place: ${word[i]}',
                );
              } else {
                solution[rr][cc] = word[i];
                print('Placed ${word[i]} at ($rr,$cc)');
              }
            }
          }
        }

        // Split clues into across and down; also compute proper crossword numbering
        List<Map<String, dynamic>> across = [];
        List<Map<String, dynamic>> down = [];

        // Create a map to track starting positions and assign proper numbers
        Map<String, int> positionNumbers = {};
        int currentNumber = 1;

        // Sort words by row, then by column to assign numbers properly
        List<Map<String, dynamic>> sortedWords = List.from(words);
        sortedWords.sort((a, b) {
          int rowCompare = (a['row'] as num).compareTo(b['row'] as num);
          if (rowCompare != 0) return rowCompare;
          return (a['col'] as num).compareTo(b['col'] as num);
        });

        // Assign numbers based on grid positions
        for (final w in sortedWords) {
          final String posKey = '${w['row']}_${w['col']}';
          if (!positionNumbers.containsKey(posKey)) {
            positionNumbers[posKey] = currentNumber++;
          }
        }

        for (int i = 0; i < words.length; i++) {
          final w = words[i];
          final String posKey = '${w['row']}_${w['col']}';
          final entry = {
            'number': positionNumbers[posKey],
            'word': w['word'].toString().toUpperCase(),
            'clue': w['clue'],
            'direction': w['direction'],
            'row': (w['row'] as num).toInt(),
            'col': (w['col'] as num).toInt(),
          };
          if (w['direction'].toString().toLowerCase() == 'across') {
            across.add(entry);
          } else {
            down.add(entry);
          }
        }

        // Sort clues by number for display
        across.sort(
          (a, b) => (a['number'] as int).compareTo(b['number'] as int),
        );
        down.sort((a, b) => (a['number'] as int).compareTo(b['number'] as int));

        setState(() {
          _gridSize = gridSize;
          _solutionGrid = solution;

          _userGrid = List.generate(
            gridSize,
            (_) => List<String?>.filled(gridSize, null),
          );
          _crossWords = words;
          _cluesAcross = across;
          _cluesDown = down;
          // Initialize focus nodes and controllers aligned to active cells
          _focusNodes = List.generate(
            gridSize,
            (r) => List.generate(
              gridSize,
              (c) => solution[r][c] != null ? FocusNode() : null,
            ),
          );
          _textControllers = List.generate(
            gridSize,
            (r) => List.generate(
              gridSize,
              (c) => solution[r][c] != null
                  ? TextEditingController(
                      text: (_userGrid[r][c] ?? '').toString().toUpperCase(),
                    )
                  : null,
            ),
          );
          _targetWords = words
              .map((e) => e['word'].toString().toUpperCase())
              .toList();

          _allWords = [];
          _currentQuestion = 'Fill the crossword by answering the clues';
          _currentReason = '';
          _currentAnswer = '';
          timer = currentLevel.timer!;
          _timeRemaining = timer;
          _selectionLimit = _targetWords.length;
        });
        return; // Early exit; crossword uses same phases
      } catch (e) {
        // Fallback to classic generation if constants are malformed
        // ignore: avoid_print
        print('Crossword generation failed: $e');
      }
    }

    // Branch: Odd-One-Out
    if (_isOddOneOut) {
      try {
        // Set difficulty from backend on first load
        if (!preserveCurrentRound) {
          _oooDifficulty = (difficulty?.toString().toLowerCase() ?? 'easy');
          if (!['easy', 'medium', 'hard'].contains(_oooDifficulty)) {
            _oooDifficulty = 'easy';
          }
        }

        final List<Map<String, dynamic>> bank = List<Map<String, dynamic>>.from(
          GameConstants.oddOneOutByDifficulty[_oooDifficulty] ?? [],
        );
        if (bank.isEmpty) {
          throw Exception('Odd-One-Out bank empty for $_oooDifficulty');
        }
        if (!preserveCurrentRound) {
          _oooSessionOffset = Random().nextInt(bank.length);
        }
        final idx =
            (_currentRound - 1 + _oooRetryOffset + _oooSessionOffset) %
            bank.length;
        final Map<String, dynamic> q = Map<String, dynamic>.from(bank[idx]);
        final String question = q['question']?.toString() ?? '';
        final List<String> options = (q['options'] as List)
            .map((e) => e.toString().toUpperCase())
            .toList();
        final String answer = q['answer']?.toString().toUpperCase() ?? '';
        final String reason = q['reason']?.toString() ?? '';

        setState(() {
          _currentQuestion = question;
          _currentReason = reason;
          _currentAnswer = answer;
          _allWords = List<String>.from(options);
          _targetWords = [answer];
          _selectedWords.clear();
          timer = currentLevel.timer!;
          _timeRemaining = timer;
          _selectionLimit = 1;
        });
        return;
      } catch (e) {
        // ignore: avoid_print
        print('Odd-One-Out generation failed: $e');
      }
    }

    // Get words from constants instead of service
    final wordPool = List<String>.from(
      GameConstants.wordRecallData[difficulty] ?? [],
    );
    wordPool.shuffle();
    List<String> wordsToUse = wordPool;
    print(_currentRound);
    // Select 5 random target words
    final shuffledPool = List<String>.from(wordsToUse)..shuffle(random);
    _targetWords = shuffledPool.take(_minCorrect).toList();
    // Remove target words from pool for distractors
    final distractorPool = List<String>.from(wordsToUse)
      ..removeWhere((w) => _targetWords.contains(w));
    distractorPool.shuffle(random);
    final distractorCount = 3; // You can adjust this number
    final distractors = distractorPool.take(distractorCount).toList();
    if (_isClassic) {
      setState(() {
        _selectionLimit = _targetWords.length;
      });
    }
    _allWords = [..._targetWords, ...distractors]..shuffle(random);

    // Prepare scrambled set if in scramble mode
    if (_isScramble) {
      _scrambledWords = _targetWords.map((w) {
        final chars = w.split('');
        // Shuffle until different from original if possible
        String s;
        int guard = 0;
        do {
          chars.shuffle(random);
          s = chars.join();
          guard++;
        } while (s.toUpperCase() == w.toUpperCase() && guard < 5);
        return s.toUpperCase();
      }).toList();
      // Initialize persistent controllers per scrambled word
      _scrambleControllers = List.generate(
        _scrambledWords.length,
        (i) => TextEditingController(text: (_scrambleInputs[i] ?? '')),
      );
      if (_isClassic) {
        setState(() {
          _selectionLimit = _targetWords.length;
        });
      }
    }
  }

  Map<String, dynamic>? _findIntersection(
    Map<String, dynamic> word1,
    Map<String, dynamic> word2,
  ) {
    final String w1 = word1['word'].toString().toUpperCase();
    final String w2 = word2['word'].toString().toUpperCase();
    final String dir1 = word1['direction'].toString().toLowerCase();
    final String dir2 = word2['direction'].toString().toLowerCase();
    final int r1 = (word1['row'] as num).toInt();
    final int c1 = (word1['col'] as num).toInt();
    final int r2 = (word2['row'] as num).toInt();
    final int c2 = (word2['col'] as num).toInt();

    // Only check intersections between across and down words
    if (dir1 == dir2) return null;

    // Determine which word is across and which is down
    final acrossWord = dir1 == 'across' ? word1 : word2;
    final downWord = dir1 == 'down' ? word1 : word2;
    final String across = (dir1 == 'across' ? w1 : w2);
    final String down = (dir1 == 'down' ? w1 : w2);
    final int acrossRow = (acrossWord['row'] as num).toInt();
    final int acrossCol = (acrossWord['col'] as num).toInt();
    final int downRow = (downWord['row'] as num).toInt();
    final int downCol = (downWord['col'] as num).toInt();

    // Check if they intersect
    if (acrossRow >= downRow &&
        acrossRow < downRow + down.length &&
        downCol >= acrossCol &&
        downCol < acrossCol + across.length) {
      final int acrossIndex = downCol - acrossCol;
      final int downIndex = acrossRow - downRow;

      if (acrossIndex >= 0 &&
          acrossIndex < across.length &&
          downIndex >= 0 &&
          downIndex < down.length) {
        final String acrossLetter = across[acrossIndex];
        final String downLetter = down[downIndex];

        if (acrossLetter == downLetter) {
          return {'row': acrossRow, 'col': downCol, 'letter': acrossLetter};
        }
      }
    }
    return null;
  }

  Future<void> _startMemorization() async {
    // For Odd-One-Out and Scramble, skip memorization countdown and go straight to recall with timer
    if (_isOddOneOut || _isScramble || _isCrossword) {
      _startRecallWithTimer();
    } else {
      setState(() {
        _currentPhase = ExercisePhase.memorization;
        _instructions = gameInstructions;
      });

      _timer?.cancel();
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeRemaining--;
        });
        if (_timeRemaining <= 0) {
          timer.cancel();
          _startRecall();
        }
      });
    }
    final activeSessions = await AssignedGamesInfoService.getActiveSession(
      _gameId,
    );
    print(activeSessions);
    print(activeSessions?['details']);
    if (activeSessions?['details'] == "UNFINISHED_SESSION_FOUND") {
      final shouldContinue = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Resume Game?"),
          content: Text(
            "You have an unfinished session. Do you want to continue?",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text("No"),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text("Yes"),
            ),
          ],
        ),
      );

      if (shouldContinue == true) {
        setState(() {
          _sessionId = activeSessions?['session_ids'][0];
        });
      } else {
        final config = await AssignedGamesInfoService.startGame(
          gameId: _gameId,
          levelId: _levelId,
        );
        setState(() {
          _sessionId = config?['session_id'];
        });
      }
    } else {
      final config = await AssignedGamesInfoService.startGame(
        gameId: _gameId,
        levelId: _levelId,
      );
      setState(() {
        _sessionId = config?['session_id'];
      });
    }
  }

  void _startRecall() {
    setState(() {
      _currentPhase = ExercisePhase.recall;
      _instructions = gameInstructions;
    });
  }

  void _startRecallWithTimer() {
    // Used for Crossword to run a countdown during recall
    setState(() {
      _currentPhase = ExercisePhase.recall;
      _instructions = gameInstructions;
    });
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (t) {
      setState(() {
        _timeRemaining--;
      });
      if (_timeRemaining <= 0) {
        t.cancel();
        // Auto-submit answer (will score 0 if nothing selected)
        _submitAnswer();
      }
    });
  }

  void _toggleWordSelection(String word) {
    setState(() {
      if (_selectedWords.contains(word)) {
        _selectedWords.remove(word);
      } else {
        if (_isOddOneOut) {
          _selectedWords
            ..clear()
            ..add(word);
        } else if (_selectedWords.length < _minCorrect) {
          _selectedWords.add(word);
        }
      }
    });
  }

  Future<void> _submitAnswer() async {
    int correctSelections = 0;
    for (String word in _selectedWords) {
      if (_targetWords.contains(word)) {
        correctSelections++;
      }
    }
    print('correctSelections+$correctSelections');
    print('_targetWords+ $_targetWords');

    // Calculate score (correct selections - incorrect selections, minimum 0)
    int incorrectSelections = _selectedWords.length - correctSelections;
    _score = (correctSelections - incorrectSelections).clamp(
      0,
      _selectionLimit,
    );

    // Calculate percentage for this round
    double roundPercentage = (correctSelections / _targetWords.length) * 100;
    print('d+$roundPercentage');
    // Save round result
    _roundResults.add(
      RoundResult(
        round: _currentRound,
        score: _score,
        correctSelections: correctSelections,
        incorrectSelections: incorrectSelections,
        missedWords: _targetWords.length - correctSelections,
        percentage: roundPercentage,
        targetWords: List.from(_targetWords),
        selectedWords: Set.from(_selectedWords),
        explanation: (!_isOddOneOut || correctSelections >= _selectionLimit)
            ? null
            : _currentReason,
        correctAnswer: _isOddOneOut
            ? (_currentAnswer.isNotEmpty
                  ? _currentAnswer
                  : (_targetWords.isNotEmpty ? _targetWords.first : ''))
            : null,
      ),
    );

    _totalScore += _score;

    final bool isFinalRound = _currentRound >= _totalRounds;

    if (isFinalRound) {
      setState(() {
        _currentPhase = ExercisePhase.finalResults;
        _calculatingFinalResult = true;
      });

      final loadedGame = await HiveUserService().getAssignedGames();
      final currentLevel = loadedGame?.levels.firstWhere(
        (level) => level.levelNumber == _currentRound,
        orElse: () => throw Exception("Level not found"),
      );
      await AssignedGamesInfoService.endGame(
        gameId: _gameId,
        levelId: currentLevel!.levelId,
        sessionId: _sessionId!,
        score: _gameType == 'CROSS_WORD'
            ? (correctSelections >= _targetWords.length
                  ? _targetScore.toString()
                  : '0')
            : (correctSelections >= _selectionLimit
                  ? _targetScore.toString()
                  : '0'),
      );
      final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
      await HiveUserService().saveAssignedGames(games!);

      bool roundPassed = _gameType == 'CROSS_WORD'
          ? correctSelections >= _targetWords.length
          : correctSelections >= _selectionLimit;

      // Use a short delay to allow the loader to be visible
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        setState(() {
          passed = roundPassed;
          _calculatingFinalResult = false;
        });
      }
    } else {
      final loadedGame = await HiveUserService().getAssignedGames();
      final currentLevel = loadedGame?.levels.firstWhere(
        (level) => level.levelNumber == _currentRound,
        orElse: () => throw Exception("Level not found"),
      );
      await AssignedGamesInfoService.endGame(
        gameId: _gameId,
        levelId: currentLevel!.levelId,
        sessionId: _sessionId!,
        score: _gameType == 'CROSS_WORD'
            ? (correctSelections >= _targetWords.length
                  ? _targetScore.toString()
                  : '0')
            : (correctSelections >= _selectionLimit
                  ? _targetScore.toString()
                  : '0'),
      );
      final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
      await HiveUserService().saveAssignedGames(games!);

      bool roundPassed = _gameType == 'CROSS_WORD'
          ? correctSelections >= _targetWords.length
          : correctSelections >= _selectionLimit;

      setState(() {
        _currentPhase = ExercisePhase.roundResults;
        passed = roundPassed;
      });
    }
  }

  Future<void> _continueToNextRound() async {
    int updatedRound = _currentRound + 1;
    final loadedGame = await HiveUserService().getAssignedGames();
    setState(() {
      _currentRound = updatedRound;
    });
    print(_currentRound);
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == updatedRound,
      orElse: () => throw Exception("Level not found"),
    );
    setState(() {
      _levelId = currentLevel!.levelId;
    });

    final config = await AssignedGamesInfoService.startGame(
      gameId: _gameId,
      levelId: currentLevel!.levelId,
    );
    setState(() {
      _sessionId = config?['session_id'];
    });
    _oooRetryOffset = 0; // reset rotation for next round
    await _generateWords(preserveCurrentRound: true);
    if (_isOddOneOut || _isScramble || _isCrossword) {
      // For OOO, go straight to recall with fresh content
      setState(() {
        _score = 0;
        _instructions = gameInstructions;
      });
      _startRecallWithTimer();
    } else {
      setState(() {
        _currentPhase = ExercisePhase.memorization;
        _score = 0;
        _timeRemaining = currentLevel.timer!;
      });
      _timer?.cancel();
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeRemaining--;
        });
        if (_timeRemaining <= 0) {
          timer.cancel();
          _startRecall();
        }
      });
    }
  }

  Future<void> _retryCurrentRound() async {
    // Retry the current round - don't change the round number
    final loadedGame = await HiveUserService().getAssignedGames();
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) =>
          level.levelNumber ==
          _currentRound, // Use _currentRound instead of _currentLevelNo
      orElse: () => throw Exception("Level not found"),
    );

    setState(() {
      _levelId = currentLevel!.levelId;
      _timeRemaining = currentLevel.timer!;
    });

    final config = await AssignedGamesInfoService.startGame(
      gameId: _gameId,
      levelId: currentLevel!.levelId,
    );

    setState(() {
      _sessionId = config?['session_id'];
    });

    final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    await HiveUserService().saveAssignedGames(games!);
    // setState(() {
    //   _isOddOneOut = currentLevel.patternType == 'ODD_ONE_OUT';
    //   _isScramble = currentLevel.patternType == 'WORD_SCRAMBLE';
    //   _isCrossword = currentLevel.patternType == 'CROSS_WORD';
    //   _isClassic = !_isOddOneOut && !_isScramble && !_isCrossword;
    // });
    // Rotate to a new question on retry
    _oooRetryOffset++;
    await _generateWords(preserveCurrentRound: true);
    if (_isOddOneOut || _isScramble || _isCrossword) {
      setState(() {
        _score = 0;
        _instructions = gameInstructions;
        // _oooDifficulty = currentLevel.difficultyLevel
        // Keep _currentRound unchanged
      });
      _startRecallWithTimer();
    } else {
      setState(() {
        _currentPhase = ExercisePhase.memorization;
        _score = 0;
        _timeRemaining = currentLevel.timer!;
        // Keep _currentRound unchanged
      });
      _timer?.cancel();
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeRemaining--;
        });
        if (_timeRemaining <= 0) {
          timer.cancel();
          _startRecall();
        }
      });
    }
  }

  void _restartExercise() {
    // This should reset everything to round 1
    setState(() {
      _currentRound = 1; // Reset to round 1
      _currentLevelNo = 1; // Reset level number
      _totalScore = 0;
      _roundResults.clear();
      _currentPhase = ExercisePhase.instructions;
      // _isOddOneOut = initialPattern == 'ODD_ONE_OUT';
      // _isScramble = initialPattern == 'WORD_SCRAMBLE';
      // _isCrossword = initialPattern == 'CROSS_WORD';
      // _isClassic = !_isOddOneOut && !_isScramble && !_isCrossword;
      _score = 0;
      _gameType = initialPattern;
      // Rotate OOO questions on full restart and reset per-session randomness
      _oooRetryOffset++;
      _oooSessionOffset =
          0; // will be set when generating new words for session
    });

    // Regenerate words for round 1
    _generateWords(preserveCurrentRound: true); // Don't preserve, start fresh
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Word Recall Exercise',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.secondaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'home') {
                context.go(AppRouter.home);
              } else {
                context.go(AppRouter.exercises);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildWebLayout(),
      ),
    );
  }

  Widget _buildWebLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 1000,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case ExercisePhase.instructions:
        return _buildInstructionsPhase();
      case ExercisePhase.memorization:
        return _buildMemorizationPhase();
      case ExercisePhase.recall:
        return _buildRecallPhase();
      case ExercisePhase.roundResults:
        return _buildRoundResultsPhase();
      case ExercisePhase.finalResults:
        return _buildFinalResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.record_voice_over,
            size: 60.sp,
            color: AppTheme.secondaryColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        ResponsiveText(
          'Word Recall Exercise',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing8),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing12),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                gameInstructions.trim().isEmpty
                    ? '1. Complete 5 rounds of word recall\n'
                          '2. Each round: memorize 5 words for 8 seconds\n'
                          '3. Then select the words you remember\n'
                          '4. Try to get at least 4 correct in each round!'
                    : _instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        if (_isOddOneOut)
          Container(
            padding: EdgeInsets.all(AppTheme.spacing8),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(color: AppTheme.dividerColor),
            ),
          ),
        SizedBox(height: AppTheme.spacing12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startMemorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemorizationPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                _isOddOneOut
                    ? 'Round $_currentRound of $_totalRounds - Get ready: $_timeRemaining seconds'
                    : 'Round $_currentRound of $_totalRounds - Memorize these words: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                  fontSize: 10.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        if (!_isOddOneOut)
          Container(
            padding: EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: _targetWords.map((word) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing4),
                  child: ResponsiveText(
                    word.toUpperCase(),
                    style: AppTheme.headlineMedium.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textPrimary,
                      letterSpacing: 2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              }).toList(),
            ),
          ),
        SizedBox(height: AppTheme.spacing24),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            _isOddOneOut
                ? 'You will see a list. Pick the word that does not belong.'
                : 'Focus and try to remember all the words!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing12),
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.secondaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.quiz, color: AppTheme.secondaryColor, size: 24.sp),
              SizedBox(height: AppTheme.spacing8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (_isOddOneOut || _isScramble || _isCrossword) ...[
                    Icon(
                      Icons.timer,
                      color: AppTheme.secondaryColor,
                      size: 14.sp,
                    ),
                    SizedBox(width: AppTheme.spacing4),
                    ResponsiveText(
                      '$_timeRemaining s',
                      style: AppTheme.labelLarge.copyWith(
                        color: AppTheme.secondaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: AppTheme.spacing4),
              ResponsiveText(
                _isOddOneOut
                    ? 'Round $_currentRound of $_totalRounds - Select the odd one out'
                    : _isCrossword
                    ? 'Round $_currentRound of $_totalRounds - Fill the crossword'
                    : _isScramble
                    ? 'Round $_currentRound of $_totalRounds - Unscramble the words'
                    : 'Round $_currentRound of $_totalRounds - Select the words you remember',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing4),
              ResponsiveText(
                _isCrossword
                    ? 'Fill the grid using the clues'
                    : _isScramble
                    ? 'Type the correct word for each scramble'
                    : _isOddOneOut
                    ? 'Pick the one that does not belong'
                    : 'Select the words you remember',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        if (_isCrossword)
          _buildCrosswordUI()
        else if (_isScramble)
          _buildScrambleUI()
        else
          GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
              childAspectRatio: 2.5,
            ),
            itemCount: _allWords.length,
            itemBuilder: (context, index) {
              final word = _allWords[index];
              final isSelected = _selectedWords.contains(word);

              return GestureDetector(
                onTap: () => _toggleWordSelection(word),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(AppTheme.spacing16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withOpacity(0.2)
                        : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.dividerColor,
                      width: isSelected ? 2.w : 1.w,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Center(
                    child: ResponsiveText(
                      word.toUpperCase(),
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textPrimary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
        SizedBox(height: AppTheme.spacing12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isCrossword
                ? _submitCrosswordAnswer
                : _isScramble
                ? _submitScrambleAnswer
                : (_selectedWords.isNotEmpty ? _submitAnswer : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build Crossword UI (grid + clues)
  Widget _buildCrosswordUI() {
    if (_gridSize <= 0) {
      return Center(
        child: ResponsiveText(
          'Loading puzzle...',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
        ),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // TOP: Grid
        FractionallySizedBox(
          widthFactor: 0.95,
          child: AspectRatio(
            aspectRatio: 1,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppTheme.dividerColor, width: 1.0),
              ),
              child: Column(
                children: List.generate(_gridSize, (r) {
                  return Expanded(
                    child: Row(
                      children: List.generate(_gridSize, (c) {
                        final isActive = _solutionGrid[r][c] != null;
                        final isSelected =
                            _selectedRow == r && _selectedCol == c;

                        return Expanded(
                          child: GestureDetector(
                            onTap: isActive
                                ? () {
                                    setState(() {
                                      _selectedRow = r;
                                      _selectedCol = c;
                                    });
                                    // Focus the tapped cell
                                    final fn = _focusNodes[r][c];
                                    if (fn != null) {
                                      FocusScope.of(context).requestFocus(fn);
                                    }
                                  }
                                : null,
                            child: Container(
                              margin: EdgeInsets.all(1.0),
                              decoration: BoxDecoration(
                                color: isActive
                                    ? (isSelected
                                          ? AppTheme.primaryColor.withOpacity(
                                              0.12,
                                            )
                                          : AppTheme.surfaceColor)
                                    : AppTheme.dividerColor.withOpacity(0.4),
                                border: Border.all(
                                  color: isSelected
                                      ? AppTheme.primaryColor
                                      : AppTheme.dividerColor,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              child: Center(
                                child: isActive
                                    ? TextField(
                                        focusNode: _focusNodes[r][c],
                                        textAlign: TextAlign.center,
                                        decoration: const InputDecoration(
                                          border: InputBorder.none,
                                          counterText: '',
                                          contentPadding: EdgeInsets.zero,
                                        ),
                                        style: AppTheme.titleMedium.copyWith(
                                          fontWeight: FontWeight.w700,
                                          color: AppTheme.textPrimary,
                                        ),
                                        maxLength: 1,
                                        controller: _textControllers[r][c],
                                        onChanged: (val) {
                                          final t = val.toUpperCase();
                                          setState(() {
                                            _userGrid[r][c] = t.isEmpty
                                                ? null
                                                : t[0];
                                            // auto-advance along selected direction
                                            int nr = r;
                                            int nc = c;
                                            if (_selectedDirection ==
                                                'across') {
                                              // move right to next active
                                              int cc = c + 1;
                                              while (cc < _gridSize &&
                                                  _solutionGrid[r][cc] ==
                                                      null) {
                                                cc++;
                                              }
                                              if (cc < _gridSize &&
                                                  _solutionGrid[r][cc] !=
                                                      null) {
                                                nr = r;
                                                nc = cc;
                                              }
                                            } else {
                                              // move down to next active
                                              int rr = r + 1;
                                              while (rr < _gridSize &&
                                                  _solutionGrid[rr][c] ==
                                                      null) {
                                                rr++;
                                              }
                                              if (rr < _gridSize &&
                                                  _solutionGrid[rr][c] !=
                                                      null) {
                                                nr = rr;
                                                nc = c;
                                              }
                                            }
                                            _selectedRow = nr;
                                            _selectedCol = nc;
                                          });
                                          // After setState, request focus to next active cell
                                          final next =
                                              _focusNodes[_selectedRow!][_selectedCol!];
                                          if (next != null) {
                                            FocusScope.of(
                                              context,
                                            ).requestFocus(next);
                                          }
                                        },
                                      )
                                    : null,
                              ),
                            ),
                          ),
                        );
                      }),
                    ),
                  );
                }),
              ),
            ),
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        // Direction selector
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ChoiceChip(
              label: Text('Across', style: AppTheme.labelSmall),
              selected: _selectedDirection == 'across',
              visualDensity: const VisualDensity(horizontal: -2, vertical: -2),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              onSelected: (_) {
                setState(() => _selectedDirection = 'across');
              },
            ),
            SizedBox(width: AppTheme.spacing8),
            ChoiceChip(
              label: Text('Down', style: AppTheme.labelSmall),
              selected: _selectedDirection == 'down',
              visualDensity: const VisualDensity(horizontal: -2, vertical: -2),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              onSelected: (_) {
                setState(() => _selectedDirection = 'down');
              },
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing16),
        // BOTTOM: Clues as simple text
        _buildSimpleClues(),
      ],
    );
  }

  Widget _buildSimpleClues() {
    return Column(
      children: [
        ResponsiveText(
          'Across',
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        ..._cluesAcross.map((clue) {
          return ResponsiveText(
            '${clue['number']}. ${clue['clue']}',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          );
        }),
        SizedBox(height: AppTheme.spacing16),
        ResponsiveText(
          'Down',
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        ..._cluesDown.map((clue) {
          return ResponsiveText(
            '${clue['number']}. ${clue['clue']}',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          );
        }),
      ],
    );
  }

  Widget _buildScrambleUI() {
    return Column(
      children: [
        ...List.generate(_scrambledWords.length, (index) {
          final scrambled = _scrambledWords[index];
          final controller = index < _scrambleControllers.length
              ? _scrambleControllers[index]
              : TextEditingController();
          return Padding(
            padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.all(AppTheme.spacing12),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                      border: Border.all(color: AppTheme.dividerColor),
                    ),
                    child: Center(
                      child: ResponsiveText(
                        scrambled,
                        style: AppTheme.titleMedium.copyWith(
                          fontWeight: FontWeight.w700,
                          letterSpacing: 2,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: AppTheme.spacing12),
                Expanded(
                  flex: 6,
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      hintText: 'Type correct word',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusMedium,
                        ),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing12,
                        vertical: AppTheme.spacing12,
                      ),
                    ),
                    textCapitalization: TextCapitalization.characters,
                    enableSuggestions: false,
                    autocorrect: false,
                    onChanged: (val) {
                      _scrambleInputs[index] = val.toUpperCase();
                      // Keep field text uppercase without moving cursor
                      final sel = controller.selection;
                      final upper = val.toUpperCase();
                      if (controller.text != upper) {
                        controller.value = TextEditingValue(
                          text: upper,
                          selection: sel,
                          composing: TextRange.empty,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  void _submitScrambleAnswer() {
    // Check each input against targetWords (1:1 order)
    final Set<String> correct = {};
    for (int i = 0; i < _targetWords.length; i++) {
      final targetOriginal = _targetWords[i];
      final targetUpper = targetOriginal.toUpperCase();
      final input = (_scrambleInputs[i] ?? '').toUpperCase().trim();
      if (input.isNotEmpty && input == targetUpper) {
        // Add the original-case target word so _submitAnswer()'s
        // case-sensitive contains() comparison succeeds.
        correct.add(targetOriginal);
      }
    }
    setState(() {
      _selectedWords
        ..clear()
        ..addAll(correct);
    });
    _submitAnswer();
  }

  // Build selected words from user grid and submit via existing flow
  void _submitCrosswordAnswer() {
    // Determine which words are fully correct
    final Set<String> correct = {};
    for (final w in _crossWords) {
      final word = w['word'].toString().toUpperCase();
      final dir = w['direction'].toString().toLowerCase();
      final r0 = (w['row'] as num).toInt();
      final c0 = (w['col'] as num).toInt();
      bool ok = true;
      for (int i = 0; i < word.length; i++) {
        final r = dir == 'down' ? r0 + i : r0;
        final c = dir == 'across' ? c0 + i : c0;
        final ch = _userGrid[r][c]?.toUpperCase();
        if (ch == null || ch != word[i]) {
          ok = false;
          break;
        }
      }
      if (ok) correct.add(word);
    }
    setState(() {
      _selectedWords
        ..clear()
        ..addAll(correct);
    });
    _submitAnswer();
  }

  Widget _buildRoundResultsPhase() {
    final currentRoundResult = _roundResults.last;
    // Check if user passed this round (got at least minimum correct)
    bool passedRound =
        currentRoundResult.correctSelections ==
        currentRoundResult.targetWords.length;
    // Fallback for correct answer display
    final String finalCorrectAnswer =
        (currentRoundResult.correctAnswer != null &&
            currentRoundResult.correctAnswer!.trim().isNotEmpty)
        ? currentRoundResult.correctAnswer!.trim()
        : (currentRoundResult.targetWords.isNotEmpty
              ? currentRoundResult.targetWords.first
              : '');

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 60.w,
          height: 60.h,
          decoration: BoxDecoration(
            color: currentRoundResult.scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            currentRoundResult.scoreIcon,
            size: 60.sp,
            color: currentRoundResult.scoreColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          passed
              ? 'Round ${currentRoundResult.round} Complete!'
              : 'Give it Another Go',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing8),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: currentRoundResult.scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: currentRoundResult.scoreColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${currentRoundResult.percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: currentRoundResult.scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                currentRoundResult.scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              // Show all correct answers as chips (for any mode)
              if (currentRoundResult.targetWords.isNotEmpty) ...[
                SizedBox(height: AppTheme.spacing12),
                Divider(color: currentRoundResult.scoreColor.withOpacity(0.3)),
                SizedBox(height: AppTheme.spacing8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check_circle, color: AppTheme.secondaryColor),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Correct Answers',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing8),
                Wrap(
                  spacing: AppTheme.spacing8,
                  runSpacing: AppTheme.spacing8,
                  alignment: WrapAlignment.center,
                  children: currentRoundResult.targetWords
                      .map(
                        (w) => Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacing12,
                            vertical: AppTheme.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.surfaceColor,
                            borderRadius: BorderRadius.circular(
                              AppTheme.radiusMedium,
                            ),
                            border: Border.all(color: AppTheme.dividerColor),
                          ),
                          child: ResponsiveText(
                            w,
                            style: AppTheme.labelMedium.copyWith(
                              color: AppTheme.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
              if (currentRoundResult.explanation != null &&
                  currentRoundResult.explanation!.trim().isNotEmpty) ...[
                SizedBox(height: AppTheme.spacing8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.lightbulb, color: AppTheme.secondaryColor),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Explanation',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing4),
                ResponsiveText(
                  currentRoundResult.explanation!,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              SizedBox(height: AppTheme.spacing16),
              // Show current reward for this level
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing16,
                  vertical: AppTheme.spacing12,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.stars, color: AppTheme.accentColor, size: 24.sp),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'You got $_rewardPoints reward  points',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppTheme.spacing12),
              Divider(color: currentRoundResult.scoreColor.withOpacity(0.3)),
              SizedBox(height: AppTheme.spacing12),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              //   children: [
              //     _buildScoreStat(
              //       icon: Icons.check_circle,
              //       label: 'Correct',
              //       value: '${currentRoundResult.correctSelections}',
              //       color: AppTheme.successColor,
              //     ),
              //     _buildScoreStat(
              //       icon: Icons.cancel,
              //       label: 'Incorrect',
              //       value: '${currentRoundResult.incorrectSelections}',
              //       color: AppTheme.errorColor,
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        if (!_isOddOneOut)
          _buildWordReview(
            currentRoundResult.targetWords,
            currentRoundResult.selectedWords,
          ),
        SizedBox(height: AppTheme.spacing16),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: passedRound
                    ? _continueToNextRound
                    : _retryCurrentRound,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  passedRound ? 'Continue to Next Round' : 'Try Again',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinalResultsPhase() {
    if (_calculatingFinalResult) {
      return const Center(child: CircularProgressIndicator());
    }

    print('passed+$passed');

    // Show target score when all levels are passed
    _scoreEarned = passed ? _targetScore : 0;
    int finalScore = _scoreEarned;

    int totalMissedWords = _roundResults
        .map((r) => r.missedWords)
        .reduce((a, b) => a + b);

    Color finalScoreColor;
    String finalScoreMessage;
    IconData finalScoreIcon;

    if (passed) {
      finalScoreColor = AppTheme.successColor;
      finalScoreMessage = 'Excellent memory!';
      finalScoreIcon = Icons.star;
    } else {
      finalScoreColor = AppTheme.warningColor;
      finalScoreMessage = 'Keep practicing!';
      finalScoreIcon = Icons.trending_up;
    }

    // Prepare last round's OOO answer/explanation if present (helps when only 1 level goes straight to Final Results)
    final RoundResult? last = _roundResults.isNotEmpty
        ? _roundResults.last
        : null;
    final String finalCorrectAnswerFR =
        (last?.correctAnswer != null && last!.correctAnswer!.trim().isNotEmpty)
        ? last.correctAnswer!.trim()
        : ((last?.targetWords.isNotEmpty ?? false)
              ? last!.targetWords.first
              : '');
    final String? finalExplanationFR =
        last?.explanation?.trim().isNotEmpty == true
        ? last!.explanation!.trim()
        : null;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        vertical: AppTheme.spacing16,
        horizontal: AppTheme.spacing16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            ),
            child: Icon(finalScoreIcon, size: 60.sp, color: finalScoreColor),
          ),
          SizedBox(height: AppTheme.spacing8),
          ResponsiveText(
            passed ? 'All Levels Complete!' : 'Give it Another Go!',
            style: AppTheme.displaySmall.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing8),
          Container(
            padding: EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              border: Border.all(
                color: finalScoreColor.withOpacity(0.3),
                width: 2.w,
              ),
            ),
            child: Column(
              children: [
                ResponsiveText(
                  'You got $_rewardPoints reward  points',
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.accentColor,
                  ),
                ),
                ResponsiveText(
                  passed ? 'Target Score: $_targetScore points' : '',
                  style: AppTheme.displayLarge.copyWith(
                    fontWeight: FontWeight.w800,
                    color: finalScoreColor,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  finalScoreMessage,
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing12),
                Divider(color: finalScoreColor.withOpacity(0.3)),
                SizedBox(height: AppTheme.spacing8),
                if ((last?.targetWords.isNotEmpty ?? false) ||
                    finalCorrectAnswerFR.isNotEmpty) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle, color: AppTheme.secondaryColor),
                      SizedBox(width: AppTheme.spacing8),
                      ResponsiveText(
                        'Correct Answers',
                        style: AppTheme.titleMedium.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  if ((last?.targetWords.isNotEmpty ?? false))
                    Wrap(
                      spacing: AppTheme.spacing8,
                      runSpacing: AppTheme.spacing8,
                      alignment: WrapAlignment.center,
                      children: last!.targetWords
                          .map(
                            (w) => Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppTheme.spacing12,
                                vertical: 6.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.surfaceColor,
                                borderRadius: BorderRadius.circular(
                                  AppTheme.radiusMedium,
                                ),
                                border: Border.all(
                                  color: AppTheme.dividerColor,
                                ),
                              ),
                              child: ResponsiveText(
                                w,
                                style: AppTheme.labelMedium.copyWith(
                                  color: AppTheme.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    )
                  else
                    ResponsiveText(
                      finalCorrectAnswerFR,
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  SizedBox(height: AppTheme.spacing8),
                ],
                if (finalExplanationFR != null &&
                    finalExplanationFR.trim().isNotEmpty) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.lightbulb, color: AppTheme.secondaryColor),
                      SizedBox(width: AppTheme.spacing8),
                      ResponsiveText(
                        'Explanation',
                        style: AppTheme.titleMedium.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppTheme.spacing4),
                  ResponsiveText(
                    finalExplanationFR,
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: AppTheme.spacing12),
                ],
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //   children: [
                //     _buildScoreStat(
                //       icon: Icons.check_circle,
                //       label: 'Total Correct',
                //       value: '$totalCorrectSelections',
                //       color: AppTheme.successColor,
                //     ),
                //     _buildScoreStat(
                //       icon: Icons.help_outline,
                //       label: 'Total Missed',
                //       value: '$totalMissedWords',
                //       color: AppTheme.warningColor,
                //     ),
                //   ],
                // ),
              ],
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          // _buildRoundBreakdown(),
          SizedBox(height: AppTheme.spacing8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Navigate back to exercises page
                    context.go(AppRouter.exercises);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                    side: BorderSide(color: AppTheme.textSecondary),
                  ),
                  child: ResponsiveText(
                    'Back to Exercises',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: ElevatedButton(
                  onPressed: passed ? _restartExercise : _retryCurrentRound,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  ),
                  child: ResponsiveText(
                    passed ? 'Restart' : 'Try Again',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textOnPrimary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.labelSmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildRoundBreakdown() {
    return SizedBox(
      height: 200,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText(
              'Round-by-Round Breakdown',
              style: AppTheme.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            SizedBox(height: AppTheme.spacing12),
            ..._roundResults.map(
              (result) => Container(
                margin: EdgeInsets.only(bottom: AppTheme.spacing8),
                padding: EdgeInsets.all(AppTheme.spacing12),
                decoration: BoxDecoration(
                  color: result.scoreColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: result.scoreColor.withOpacity(0.3),
                    width: 1.w,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32.w,
                      height: 32.h,
                      decoration: BoxDecoration(
                        color: result.scoreColor,
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusSmall,
                        ),
                      ),
                      child: Center(
                        child: ResponsiveText(
                          '${result.round}',
                          style: AppTheme.labelMedium.copyWith(
                            color: AppTheme.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: AppTheme.spacing12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveText(
                            'Round ${result.round}',
                            style: AppTheme.labelMedium.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          ResponsiveText(
                            '${result.percentage.toInt()}% • Score: ${result.score}/5',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      result.scoreIcon,
                      color: result.scoreColor,
                      size: 20.sp,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWordReview(List<String> targetWords, Set<String> selectedWords) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.dividerColor, width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Word Review',
            style: AppTheme.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: AppTheme.spacing12),
          Wrap(
            spacing: AppTheme.spacing8,
            runSpacing: AppTheme.spacing8,
            children: targetWords.map((word) {
              final wasSelected = selectedWords.contains(word);

              return Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing12,
                  vertical: 6.h,
                ),
                decoration: BoxDecoration(
                  color: wasSelected
                      ? AppTheme.successColor.withOpacity(0.1)
                      : AppTheme.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: wasSelected
                        ? AppTheme.successColor
                        : AppTheme.errorColor,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      wasSelected ? Icons.check : Icons.close,
                      color: wasSelected
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                      size: 16.sp,
                    ),
                    SizedBox(width: AppTheme.spacing4),
                    ResponsiveText(
                      word,
                      style: AppTheme.labelMedium.copyWith(
                        color: wasSelected
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

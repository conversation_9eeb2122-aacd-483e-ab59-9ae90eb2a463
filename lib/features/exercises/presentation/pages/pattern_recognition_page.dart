import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/constants/game_constants.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum PatternPhase { instructions, pattern, question, results }

enum PatternType {
  shapeSequence,
  sizeSequence,
  letterSequence,
  colorSequence,
  numberMemory,
}

class PatternRecognitionPage extends StatefulWidget {
  const PatternRecognitionPage({super.key, this.extra});
  final Object? extra;

  @override
  State<PatternRecognitionPage> createState() => _PatternRecognitionPageState();
}

class _PatternRecognitionPageState extends State<PatternRecognitionPage> {
  PatternPhase _currentPhase = PatternPhase.instructions;
  PatternType _currentPatternType = PatternType.letterSequence;
  final List<PatternElement> _patternSequence = [];
  List<PatternElement> _answerOptions = [];
  PatternElement? _correctAnswer;
  PatternElement? _selectedAnswer;
  final int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  int _totalTime = 0;
  String _instructions = '';
  int _currentRound = 1;
  int _totalRounds = 1;
  int _totalScore = 0;
  int _targetScore = 0;
  final int _scoreEarned = 0;
  int _rewardPoints = 0;
  String _gameId = "";
  String _levelId = '';
  final int _minCorrect = 0;
  String _patternType = 'letter';
  String? _sessionId;
  int? _selectedIndex;
  String? _patternExplanation;
  String _difficulty = 'easy';
  String? _numberToRemember;
  String? _lastSubmittedNumber; // Store the submitted number for results
  final TextEditingController _numberInputController = TextEditingController();
  final currentLevel = [];
  Map<String, dynamic>? _currentPatternData;
  bool _isPatternGenerated = false;
  int _currentLevelNo = 1;
  Map<String, dynamic>? _currentData;
  String? _origin;
  @override
  void initState() {
    super.initState();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) {
        _origin = from;
      }
    }
    _initializePatternSystem();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _numberInputController.dispose();
    super.dispose();
  }

  // Fixed: Separate initialization from pattern generation
  Future<void> _initializePatternSystem() async {
    final loadedGame = await HiveUserService().getAssignedGames();
    if (loadedGame == null) {
      return;
    }
    _currentLevelNo = loadedGame.gameStatus != 'completed'
        ? loadedGame.level
        : 1;
    _currentLevelNo;
    final currentLevel = loadedGame.levels.firstWhere(
      (level) =>
          level.levelNumber ==
          _currentLevelNo, // Use _currentRound for consistency
      orElse: () => throw Exception("Level not found"),
    );
    // final currentLevel = loadedGame.levels.firstWhere(
    //   (level) => level.levelId == loadedGame.currentLevelId,
    //   orElse: () => throw Exception("Level not found"),
    // );

    setState(() {
      _instructions = loadedGame.instructions;
      _timeRemaining = currentLevel.timer!;
      _gameId = loadedGame.id;
      _patternType = currentLevel.patternType ?? 'NUMBER_PATTERN';
      _targetScore = loadedGame.targetScore ?? 0;
      _totalRounds = loadedGame.levels.length;
      _currentRound = _currentLevelNo;
      _levelId = loadedGame.currentLevelId ?? '1';
      _totalTime = currentLevel.timer!;
      _difficulty = currentLevel.difficultyLevel ?? 'easy';
      _rewardPoints = currentLevel.rewards?.first.points ?? 0;
    });

    // Generate pattern only once, after state is fully updated
    if (!_isPatternGenerated) {
      _generateNewPattern(currentLevel.difficultyLevel ?? 'hard');
    }
  }

  // Fixed: New method to generate pattern only when needed
  void _generateNewPattern(difficulty) {
    print('diff+ $difficulty');
    setState(() {
      _difficulty = difficulty;
    });
    _patternSequence.clear();
    _answerOptions.clear();
    _selectedAnswer = null;
    _selectedIndex = null;
    _currentPatternData = null;
    _patternExplanation = null;

    // Randomly select pattern type - including numbers now
    final patternTypes = [
      PatternType.shapeSequence,
      PatternType.sizeSequence,
      PatternType.letterSequence,
    ];
    // Handle new pattern types: color pattern and number memory
    if (_patternType == 'NUMBER_PATTERN') {
      _currentPatternType = PatternType.sizeSequence;
      // Features: Number sequence recognition (e.g., 2, 4, 6, ?)
    } else if (_patternType == 'LETTER_PATTERN') {
      _currentPatternType = PatternType.letterSequence;
      // Features: Letter sequence recognition (e.g., A, C, E, ?)
    } else if (_patternType == 'COLOR_PATTERN') {
      // Features: Color pattern memory (e.g., Red, Blue, Green, ?)
      _currentPatternType = PatternType.colorSequence;
    } else if (_patternType == 'NUMBER_MEMORY') {
      // Features: Memorise a number game (show a number, then ask to recall)
      _currentPatternType = PatternType.numberMemory;
    } else {
      _currentPatternType = PatternType.shapeSequence;
      // Features: Shape sequence recognition
    }

    switch (_currentPatternType) {
      case PatternType.shapeSequence:
        _generateShapePattern();
        break;
      case PatternType.sizeSequence:
        _generateNumberPattern();
        break;
      case PatternType.letterSequence:
        _generateLetterPattern();
        break;
      case PatternType.colorSequence:
        _generateColorPattern();
        break;
      case PatternType.numberMemory:
        _generateNumberMemoryPattern();
        break;
    }
    _isPatternGenerated = true;
  }

  // NEW: Generate number patterns

  void _generateNumberPattern() {
    print(currentLevel);
    final patterns = GameConstants.numberPatternData[_difficulty];
    print('Loaded number patterns from constants: $patterns');

    // Select a random pattern
    final random = Random();
    final pattern = patterns?[random.nextInt(patterns.length)];

    final sequence = List<dynamic>.from(
      pattern?['sequence'] as List,
    ); // keep as dynamic for numbers
    final options = List<dynamic>.from(
      pattern?['options'] as List,
    ); // keep as dynamic for numbers
    final correctValue = pattern?['answer'];
    _patternExplanation = pattern?['explanation'] as String?;

    print('Generated Number Pattern: $pattern');
    print('Sequence: $sequence');
    print('Correct Answer: $correctValue');

    // Build pattern sequence (excluding the '?' at the end)
    _patternSequence.clear();
    for (var item in sequence) {
      if (item != '?') {
        _patternSequence.add(_createPatternElement(item.toString()));
      }
    }

    // Set correct answer
    _correctAnswer = _createPatternElement(correctValue.toString());

    // Generate answer options
    _answerOptions = options.map((value) {
      return _createPatternElement(value.toString());
    }).toList();

    _answerOptions.shuffle();
  }

  // Fixed: Updated shape pattern generation with proper persistence
  void _generateShapePattern() {
    // Always generate new pattern data for each round
    // Get patterns from constants
    final patterns = GameConstants.shapePatternData[_difficulty];
    print(patterns);
    // Select a random pattern
    final random = Random();
    final patternIndex = random.nextInt(patterns!.length);
    _currentPatternData = patterns[patternIndex];

    // Use the stored pattern data
    final sequence = List<String>.from(
      _currentPatternData!['sequence'] as List,
    );
    final options = List<String>.from(_currentPatternData!['options'] as List);
    final answer = _currentPatternData!['answer'] as String;
    _patternExplanation = _currentPatternData!['explanation'] as String?;

    _patternSequence.clear();
    for (var shape in sequence.take(sequence.length - 1)) {
      _patternSequence.add(
        PatternElement(
          color: AppTheme.primaryColor,
          shape: shape,
          size: 1.0,
          displayValue: '',
        ),
      );
    }

    _correctAnswer = PatternElement(
      color: AppTheme.primaryColor,
      shape: answer,
      size: 1.0,
      displayValue: '',
    );

    // Ensure unique options
    final uniqueOptions = <String>{};
    final answerOptions = <PatternElement>[];
    for (final shape in options) {
      if (!uniqueOptions.contains(shape)) {
        uniqueOptions.add(shape);
        answerOptions.add(
          PatternElement(
            color: AppTheme.primaryColor,
            shape: shape,
            size: 1.0,
            displayValue: '',
          ),
        );
      }
    }
    _answerOptions = answerOptions;
    _answerOptions.shuffle();
  }

  void _generateAnswerOptions(
    List<Map<String, dynamic>> problems,
    String correctValue,
  ) {
    switch (_currentPatternType) {
      case PatternType.sizeSequence:
        _generateNumberAnswerOptions(problems, correctValue);
        break;
      case PatternType.shapeSequence:
        _generateShapeAnswerOptions(problems);
        break;
      case PatternType.letterSequence:
        _generateLetterAnswerOptions(problems, correctValue);
        break;
      case PatternType.colorSequence:
        _generateColorAnswerOptions(problems, correctValue);
        break;
      case PatternType.numberMemory:
        // Number memory doesn't use answer options, so no action needed
        break;
    }
    _answerOptions.shuffle();
  }

  void _generateNumberAnswerOptions(
    List<Map<String, dynamic>> problems,
    String correctValue,
  ) {
    final allNumbers = <String>{};
    for (var problem in problems) {
      if (problem['question'] != '?') {
        allNumbers.add(problem['question'] as String);
      }
      allNumbers.add(problem['answer'] as String);
    }
    // If we don't have enough options, generate some distractors
    if (allNumbers.length < 4) {
      final correctNum = int.tryParse(correctValue) ?? 0;
      // Add some logical distractors
      allNumbers.add((correctNum + 1).toString());
      allNumbers.add((correctNum - 1).toString());
      allNumbers.add((correctNum + 3).toString());
      allNumbers.add((correctNum - 3).toString());
    }

    _answerOptions = allNumbers.map((value) {
      return _createPatternElement(value);
    }).toList();
  }

  void _generateShapeAnswerOptions(List<Map<String, dynamic>> problems) {
    final allShapes = <String>{};
    for (var problem in problems) {
      if (problem['question'] != '?') {
        allShapes.add(problem['question'] as String);
      }
      allShapes.add(problem['answer'] as String);
    }

    // If we don't have enough options, add common shapes
    if (allShapes.length < 4) {
      final commonShapes = [
        'circle',
        'square',
        'triangle',
        'rectangle',
        'diamond',
        'star',
        'pentagon',
        'hexagon',
        'heptagon',
        'octagon',
        'nonagon',
      ];
      allShapes.addAll(commonShapes);
    }
    _answerOptions = allShapes.map((value) {
      return _createPatternElement(value);
    }).toList();
  }

  void _generateLetterAnswerOptions(
    List<Map<String, dynamic>> problems,
    String correctValue,
  ) {
    final allLetters = <String>{};

    // Add all letters from the problems
    for (var problem in problems) {
      if (problem['question'] != '?') {
        allLetters.add(problem['question'] as String);
      }
      allLetters.add(problem['answer'] as String);
    }

    _answerOptions = allLetters.map((value) {
      return _createPatternElement(value);
    }).toList();
  }

  void _generateColorAnswerOptions(
    List<Map<String, dynamic>> problems,
    String correctValue,
  ) {
    final allColors = <String>{};

    // Add all colors from the problems
    for (var problem in problems) {
      if (problem['question'] != '?') {
        allColors.add(problem['question'] as String);
      }
      allColors.add(problem['answer'] as String);
    }

    // If we don't have enough options, add common colors from GameConstants
    if (allColors.length < 4) {
      final commonColors = GameConstants.circleColors;
      allColors.addAll(commonColors);
    }

    _answerOptions = allColors.map((value) {
      return _createPatternElement(value);
    }).toList();
  }

  void _generateNumberMemoryPattern() {
    final patterns = GameConstants.numberMemoryData[_difficulty];
    if (patterns == null || patterns.isEmpty) {
      print('No number memory patterns found for difficulty: $_difficulty');
      return;
    }

    final random = Random();
    final patternIndex = random.nextInt(patterns.length);
    final selectedPattern = patterns[patternIndex];

    // For number memory, we just need the number to show
    _numberToRemember = selectedPattern['numbers'].toString();
  }

  PatternElement _createPatternElement(String value) {
    switch (_currentPatternType) {
      case PatternType.sizeSequence:
        // For numbers, use the number as display value
        return PatternElement(
          color: AppTheme.primaryColor,
          shape: 'number',
          size: 1.0,
          displayValue: value,
        );
      case PatternType.shapeSequence:
        return PatternElement(
          color: AppTheme.primaryColor,
          shape: _getShapeFromName(value),
          size: 1.0,
          displayValue: value,
        );
      case PatternType.letterSequence:
        return PatternElement(
          color: AppTheme.primaryColor,
          shape: 'circle',
          size: 1.0,
          displayValue: value,
        );
      case PatternType.colorSequence:
        return PatternElement(
          color: _parseColor(value), // Parse hex string to Color
          shape: 'circle',
          size: 1.0,
          displayValue: value,
        );
      case PatternType.numberMemory:
        // For number memory, create a simple number element
        return PatternElement(
          color: AppTheme.primaryColor,
          shape: 'number',
          size: 1.0,
          displayValue: value,
        );
    }
  }

  String _getShapeFromName(String name) {
    switch (name.toLowerCase()) {
      case 'circle':
        return 'circle';
      case 'square':
        return 'square';
      case 'triangle':
        return 'triangle';
      case 'diamond':
        return 'diamond';
      case 'star':
        return 'star';
      case 'pentagon':
        return 'pentagon';
      case 'hexagon':
        return 'hexagon';
      case 'heptagon':
        return 'heptagon';
      case 'octagon':
        return 'octagon';
      case 'nonagon':
        return 'nonagon';
      default:
        return 'circle'; // Default shape
    }
  }

  Color _getColorFromName(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.yellow;
      case 'purple':
        return Colors.purple;
      case 'orange':
        return Colors.orange;
      default:
        return AppTheme.primaryColor;
    }
  }

  void _generateLetterPattern() {
    // Get patterns from constants
    final patterns = GameConstants.letterPatternData[_difficulty];

    // Select a random pattern
    final random = Random();
    final pattern = patterns?[random.nextInt(patterns.length)];

    final sequence = List<String>.from(pattern?['sequence'] as List);
    final options = List<String>.from(pattern?['options'] as List);
    final correctValue = pattern?['answer'] as String;
    _patternExplanation = pattern?['explanation'] as String?;

    _patternSequence.clear();
    for (var item in sequence) {
      if (item != '?') {
        _patternSequence.add(_createPatternElement(item));
      }
    }
    _correctAnswer = _createPatternElement(correctValue);

    _answerOptions = [
      _createPatternElement(options[0]),
      _createPatternElement(options[1]),
      _createPatternElement(options[2]),
      _createPatternElement(options[3]),
    ];
    _answerOptions.shuffle();
  }

  void _generateColorPattern() {
    // Get color patterns from constants
    final patterns = GameConstants.colorPatternData[_difficulty];

    // Select a random pattern
    final random = Random();
    final pattern = patterns?[random.nextInt(patterns.length)];

    final sequence = List<String>.from(pattern?['sequence'] as List);
    final options = List<String>.from(pattern?['options'] as List);
    final correctValue = pattern?['answer'] as String;
    _patternExplanation = pattern?['explanation'] as String?;

    _patternSequence.clear();
    for (var item in sequence) {
      if (item != '?') {
        _patternSequence.add(_createPatternElement(item));
      }
    }
    _correctAnswer = _createPatternElement(correctValue);

    _answerOptions = [
      _createPatternElement(options[0]),
      _createPatternElement(options[1]),
      _createPatternElement(options[2]),
      _createPatternElement(options[3]),
    ];
    _answerOptions.shuffle();
  }

  Future<void> _startPattern() async {
    final activeSessions = await AssignedGamesInfoService.getActiveSession(
      _gameId,
    );
    print(activeSessions);
    print(activeSessions?['details']);
    if (activeSessions?['details'] == "UNFINISHED_SESSION_FOUND") {
      final shouldContinue = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Resume Game?"),
          content: Text(
            "You have an unfinished session. Do you want to continue?",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text("No"),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text("Yes"),
            ),
          ],
        ),
      );

      if (shouldContinue == true) {
        setState(() {
          _sessionId = activeSessions?['session_ids'][0];
        });
      } else {
        final config = await AssignedGamesInfoService.startGame(
          gameId: _gameId,
          levelId: _levelId,
        );
        setState(() {
          _sessionId = config?['session_id'];
        });
      }
    } else {
      final config = await AssignedGamesInfoService.startGame(
        gameId: _gameId,
        levelId: _levelId,
      );
      setState(() {
        _sessionId = config?['session_id'];
      });
    }
    setState(() {
      _currentPhase = PatternPhase.pattern;
      // _timeRemaining = 5; // 5 seconds to view pattern
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _showQuestion();
      }
    });
  }

  void _showQuestion() {
    setState(() {
      _currentPhase = PatternPhase.question;
    });
  }

  void _selectAnswer(PatternElement answer) {
    setState(() {
      _selectedAnswer = answer;
      _selectedIndex = _answerOptions.indexOf(answer); // Update selected index
    });
  }

  Future<void> _submitAnswer() async {
    bool isCorrect;

    if (_currentPatternType == PatternType.numberMemory) {
      // For number memory, compare the input text with the remembered number
      final inputNumber = _numberInputController.text.trim();
      _lastSubmittedNumber = inputNumber; // Store for results phase

      isCorrect = inputNumber == _numberToRemember;

      // Clear the input field after submission
      _numberInputController.clear();
    } else {
      // For other pattern types, use the existing logic
      isCorrect = _selectedAnswer?.isEqual(_correctAnswer!) ?? false;
    }

    if (isCorrect) {
      _totalScore++;
    }

    // Always show results; allow user to continue via button if applicable
    setState(() {
      _currentPhase = PatternPhase.results;
    });
    final config = await AssignedGamesInfoService.endGame(
      gameId: _gameId,
      levelId: _levelId,
      sessionId: _sessionId!,
      score: isCorrect ? _targetScore.toString() : '0',
    );
    final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    await HiveUserService().saveAssignedGames(games!);
  }

  Future<void> _continueToNextRound() async {
    final updatedRound = _currentRound + 1;
    setState(() {
      _currentRound = updatedRound;
    });

    // Load next level details
    final loadedGame = await HiveUserService().getAssignedGames();
    final nextLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == updatedRound,
      orElse: () => throw Exception("Level not found"),
    );

    // Start next level session
    final config = await AssignedGamesInfoService.startGame(
      gameId: _gameId,
      levelId: nextLevel!.levelId,
    );
    setState(() {
      _sessionId = config?['session_id'];
    });
    // final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    // await HiveUserService().saveAssignedGames(games!);
    // final loadedGame = await HiveUserService().getAssignedGames();

    // Prepare next round pattern
    setState(() {
      _isPatternGenerated = false;
      _currentPhase = PatternPhase.pattern;
      _timeRemaining = nextLevel.timer!;
      _patternType =
          nextLevel.patternType!; // Reset to allow new pattern generation
      _rewardPoints = nextLevel.rewards?.first.points ?? 0;

      // Clear input field for next round
      _numberInputController.clear();
      _selectedAnswer = null;
      _selectedIndex = null;
    });
    _generateNewPattern(nextLevel.difficultyLevel);

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _showQuestion();
      }
    });
  }

  Future<void> _restartExercise() async {
    final loadedGame = await HiveUserService().getAssignedGames();
    final nextLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == 1,
      orElse: () => throw Exception("Level not found"),
    );

    setState(() {
      _currentPhase = PatternPhase.instructions;
      _currentRound = 1;
      _totalScore = 0;
      _isPatternGenerated = false; // Reset pattern generation flag
      _currentPatternData = null; // Clear stored pattern data
      _timeRemaining = nextLevel!.timer!;
      _patternType = nextLevel.patternType!;
      _rewardPoints = nextLevel.rewards?.first.points ?? 0;

      // Clear all input states for restart
      _numberInputController.clear();
      _selectedAnswer = null;
      _selectedIndex = null;
      _numberToRemember = null;
      _lastSubmittedNumber = null;
    });

    _generateNewPattern(nextLevel?.difficultyLevel); // Generate fresh pattern
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Pattern Recognition',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.accentColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'exercises') {
                context.go(AppRouter.exercises);
              } else {
                context.go(AppRouter.home);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildWebLayout(),
      ),
    );
  }

  Widget _buildWebLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 1000,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case PatternPhase.instructions:
        return _buildInstructionsPhase();
      case PatternPhase.pattern:
        return _buildPatternPhase();
      case PatternPhase.question:
        return _buildQuestionPhase();
      case PatternPhase.results:
        return _buildResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(Icons.pattern, size: 60.sp, color: AppTheme.accentColor),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Pattern Recognition',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                _instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startPattern,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPatternPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                _currentPatternType == PatternType.numberMemory
                    ? 'Memorize the number: $_timeRemaining seconds'
                    : 'Study the pattern: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Round $_currentRound of $_totalRounds',
          style: AppTheme.titleSmall.copyWith(color: AppTheme.textSecondary),
        ),
        SizedBox(height: AppTheme.spacing32),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _currentPatternType == PatternType.numberMemory
              ? Center(
                  child: Container(
                    padding: EdgeInsets.all(AppTheme.spacing32),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                      border: Border.all(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        width: 2.w,
                      ),
                    ),
                    child: ResponsiveText(
                      _numberToRemember ?? '',
                      style: AppTheme.displayLarge.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.primaryColor,
                        fontSize: 48.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
              : Wrap(
                  alignment: WrapAlignment.center,
                  spacing: AppTheme.spacing12,
                  runSpacing: AppTheme.spacing12,
                  children: _patternSequence.map((element) {
                    return _buildPatternElement(element);
                  }).toList(),
                ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            _currentPatternType == PatternType.numberMemory
                ? 'Remember ${_getPatternTypeDescription()}'
                : 'Look for the pattern in ${_getPatternTypeDescription()}',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.quiz, color: AppTheme.accentColor, size: 32.sp),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                _currentPatternType == PatternType.numberMemory
                    ? 'What number did you see?'
                    : 'What comes next?',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        // Show different UI based on pattern type
        if (_currentPatternType == PatternType.numberMemory) ...[
          // Number memory: Show input field for recall
          Container(
            padding: EdgeInsets.all(AppTheme.spacing20),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(color: AppTheme.dividerColor, width: 1.w),
            ),
            child: Column(
              children: [
                ResponsiveText(
                  'Enter the number you remember:',
                  style: AppTheme.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing16),
                TextField(
                  controller: _numberInputController,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  style: AppTheme.headlineMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter number...',
                    hintStyle: AppTheme.bodyLarge.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                      borderSide: BorderSide(color: AppTheme.dividerColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                      borderSide: BorderSide(
                        color: AppTheme.primaryColor,
                        width: 2.w,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing12,
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // Enable submit button when text is entered
                    });
                  },
                ),
              ],
            ),
          ),
        ] else ...[
          // Pattern sequence: Show pattern with question mark
          Container(
            padding: EdgeInsets.all(AppTheme.spacing20),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(color: AppTheme.dividerColor, width: 1.w),
            ),
            child: Wrap(
              alignment: WrapAlignment.center,
              spacing: AppTheme.spacing12,
              runSpacing: AppTheme.spacing12,
              children: [
                ..._patternSequence.map(
                  (element) => _buildPatternElement(element),
                ),
                Container(
                  width: 50.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: AppTheme.textTertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                    border: Border.all(
                      color: AppTheme.textTertiary,
                      width: 2.w,
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: Center(
                    child: ResponsiveText(
                      '?',
                      style: AppTheme.headlineMedium.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textTertiary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: AppTheme.spacing24),
          ResponsiveText(
            'Choose the correct answer:',
            style: AppTheme.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          // Answer options grid
          GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
              childAspectRatio: 1.0,
            ),
            itemCount: _answerOptions.length,
            itemBuilder: (context, index) {
              final option = _answerOptions[index];
              final isSelected = index == _selectedIndex;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedIndex = index;
                    _selectedAnswer = option;
                  });
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(AppTheme.spacing16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.accentColor.withOpacity(0.2)
                        : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.dividerColor,
                      width: isSelected ? 3.w : 1.w,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: AppTheme.accentColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Center(child: _buildPatternElement(option)),
                ),
              );
            },
          ),
        ],
        SizedBox(height: AppTheme.spacing16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _currentPatternType == PatternType.numberMemory
                ? (_numberInputController.text.isNotEmpty
                      ? _submitAnswer
                      : null)
                : (_selectedAnswer != null ? _submitAnswer : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _checkAnswer() {
    bool isCorrect =
        _selectedAnswer != null &&
        _selectedAnswer!.color == _correctAnswer!.color;

    setState(() {
      _currentPhase = PatternPhase.results;
    });

    // Show results for a few seconds then return to home or restart
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        context.go(AppRouter.home);
      }
    });
  }

  Future<void> _retryCurrentRound() async {
    // Preserve current round and restart session for this level only
    final loadedGame = await HiveUserService().getAssignedGames();
    final currentLevel = loadedGame?.levels.firstWhere(
      (level) => level.levelNumber == _currentRound,
      orElse: () => throw Exception("Level not found"),
    );

    // Start/restart the session for the current level
    final config = await AssignedGamesInfoService.startGame(
      gameId: _gameId,
      levelId: currentLevel!.levelId,
    );
    setState(() {
      _sessionId = config?['session_id'];
    });

    // Refresh game config locally
    // final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    // await HiveUserService().saveAssignedGames(games!);

    // Regenerate pattern without advancing the level
    setState(() {
      _isPatternGenerated = false;
      _currentPatternData = null;
      _timeRemaining = currentLevel.timer!;
      _currentPhase = PatternPhase.pattern;
      _patternType = currentLevel.patternType!;
      _rewardPoints = currentLevel.rewards?.first.points ?? 0;
    });
    _generateNewPattern(currentLevel.difficultyLevel);

    // Restart timer for showing pattern before question
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _showQuestion();
      }
    });
  }

  Widget _buildResultsPhase() {
    bool isCorrect;

    if (_currentPatternType == PatternType.numberMemory) {
      // For number memory, use the stored submitted number for comparison
      final inputNumber = _lastSubmittedNumber ?? '';

      // Debug logging for results phase
      print('DEBUG RESULTS: Input number: "$inputNumber"');
      print('DEBUG RESULTS: Remembered number: "$_numberToRemember"');
      print(
        'DEBUG RESULTS: Are they equal? ${inputNumber == _numberToRemember}',
      );

      isCorrect = inputNumber == _numberToRemember;
    } else {
      // For other pattern types, use the existing logic
      isCorrect = _selectedAnswer?.isEqual(_correctAnswer!) ?? false;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: isCorrect
                ? AppTheme.successColor.withOpacity(0.1)
                : AppTheme.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            isCorrect ? Icons.check_circle : Icons.cancel,
            size: 60.sp,
            color: isCorrect ? AppTheme.successColor : AppTheme.errorColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          isCorrect ? 'Correct!' : 'Incorrect',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: isCorrect ? AppTheme.successColor : AppTheme.errorColor,
          ),
          textAlign: TextAlign.center,
        ),
        isCorrect
            ? ResponsiveText(
                'You got $_rewardPoints  reward points',
                style: AppTheme.displaySmall.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.successColor,
                ),
                textAlign: TextAlign.center,
              )
            : SizedBox.shrink(), // returns nothing if not correct
        ResponsiveText(
          _currentRound == _totalRounds && isCorrect
              ? 'Score:  $_targetScore'
              : '',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.successColor,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),

        if (_patternExplanation != null) ...[
          SizedBox(height: AppTheme.spacing16),
          ResponsiveText(
            'Explanation: \n${_patternExplanation!}',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: (isCorrect && _currentRound < _totalRounds)
                    ? _continueToNextRound
                    : (!isCorrect ? _retryCurrentRound : _restartExercise),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  (isCorrect && _currentRound < _totalRounds)
                      ? 'Continue to Next Round'
                      : (!isCorrect ? 'Retry Round' : 'Restart'),
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // UPDATED: Enhanced pattern element builder with number support
  Widget _buildPatternElement(PatternElement element) {
    switch (_currentPatternType) {
      case PatternType.sizeSequence:
        // For numbers, show the actual number in a styled container
        return Container(
          width: 60.w,
          height: 60.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            border: Border.all(color: AppTheme.primaryColor, width: 2),
          ),
          child: Center(
            child: ResponsiveText(
              element.displayValue ?? '',
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );

      case PatternType.shapeSequence:
        return Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
          ),
          child: CustomPaint(
            painter: ShapeElementPainter(
              shape: element.shape,
              color: AppTheme.primaryColor,
            ),
          ),
        );

      case PatternType.letterSequence:
        return Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: ResponsiveText(
              element.displayValue ?? '',
              style: AppTheme.headlineMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );

      case PatternType.colorSequence:
        // For colors, show a colored circle
        return Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: element.color, // element.color is already a Color object
            shape: BoxShape.circle,
            border: Border.all(color: Colors.grey.shade300, width: 2),
          ),
        );

      case PatternType.numberMemory:
        // For number memory, this shouldn't be called as we use direct display
        return Container(
          width: 60.w,
          height: 60.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            border: Border.all(color: AppTheme.primaryColor, width: 2),
          ),
          child: Center(
            child: ResponsiveText(
              element.displayValue ?? '',
              style: AppTheme.headlineMedium.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
    }
  }

  Color _parseColor(String colorString) {
    // Parse hex color string to Color object
    if (colorString.startsWith('#')) {
      return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
    }
    // Fallback to primary color if parsing fails
    return AppTheme.primaryColor;
  }

  // UPDATED: Pattern type description
  String _getPatternTypeDescription() {
    switch (_currentPatternType) {
      case PatternType.shapeSequence:
        return 'shapes';
      case PatternType.sizeSequence:
        return 'numbers'; // Changed from 'sizes' to 'numbers'
      case PatternType.letterSequence:
        return 'letters';
      case PatternType.colorSequence:
        return 'colors';
      case PatternType.numberMemory:
        return 'the number shown';
    }
  }
}

class PatternElement {
  final Color color;
  final String shape;
  final double size;
  final String? displayValue;

  PatternElement({
    required this.color,
    required this.shape,
    required this.size,
    this.displayValue,
  });

  bool isEqual(PatternElement other) {
    return shape == other.shape &&
        displayValue == other.displayValue &&
        color.value == other.color.value; // Compare color by value
  }
}

class ShapeElementPainter extends CustomPainter {
  final String shape;
  final Color color;

  ShapeElementPainter({required this.shape, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 * 0.8;

    switch (shape) {
      case 'circle':
        canvas.drawCircle(center, radius, paint);
        break;
      case 'square':
      case 'rectangle':
        final rect = Rect.fromCenter(
          center: center,
          width: radius * 1.6,
          height: radius * 1.6,
        );
        canvas.drawRect(rect, paint);
        break;
      case 'triangle':
        final path = Path();
        path.moveTo(center.dx, center.dy - radius);
        path.lineTo(center.dx - radius * 0.866, center.dy + radius * 0.5);
        path.lineTo(center.dx + radius * 0.866, center.dy + radius * 0.5);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case 'diamond':
        final path = Path();
        path.moveTo(center.dx, center.dy - radius);
        path.lineTo(center.dx + radius, center.dy);
        path.lineTo(center.dx, center.dy + radius);
        path.lineTo(center.dx - radius, center.dy);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case 'star':
        final path = Path();
        final outerRadius = radius;
        final innerRadius = radius * 0.5;
        for (int i = 0; i < 10; i++) {
          final angle = (i * 36 - 90) * (3.14159 / 180);
          final r = i % 2 == 0 ? outerRadius : innerRadius;
          final x = center.dx + r * cos(angle);
          final y = center.dy + r * sin(angle);
          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }
        path.close();
        canvas.drawPath(path, paint);
        break;
      case 'pentagon':
      case 'hexagon':
      case 'heptagon':
      case 'octagon':
      case 'nonagon':
        final sides = _getSidesFromShape(shape);
        final path = Path();
        for (int i = 0; i < sides; i++) {
          final angle = (i * 360 / sides - 90) * (3.14159 / 180);
          final x = center.dx + radius * cos(angle);
          final y = center.dy + radius * sin(angle);
          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }
        path.close();
        canvas.drawPath(path, paint);
        break;
      default:
        canvas.drawCircle(center, radius, paint);
    }
  }

  int _getSidesFromShape(String shape) {
    switch (shape) {
      case 'pentagon':
        return 5;
      case 'hexagon':
        return 6;
      case 'heptagon':
        return 7;
      case 'octagon':
        return 8;
      case 'nonagon':
        return 9;
      default:
        return 3;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/constants/game_constants.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum VisualMemoryPhase {
  instructions,
  study,
  recall,
  roundResults,
  finalResults,
}

class VisualMemoryPage extends StatefulWidget {
  final String
  gameType; // e.g., 'visual_memory', 'object_memory', 'shape_memory'
  const VisualMemoryPage({
    super.key,
    this.gameType = 'face_memory',
    this.extra,
  });
  final Object? extra;

  @override
  State<VisualMemoryPage> createState() => _VisualMemoryPageState();
}

class _VisualMemoryPageState extends State<VisualMemoryPage>
    with TickerProviderStateMixin {
  // Game state
  VisualMemoryPhase _currentPhase = VisualMemoryPhase.instructions;
  int _currentRound = 1;
  int _score = 0;
  int _totalRounds = 1;
  String _difficulty = 'medium';
  String _gameType = 'face_memory';
  String _gameTitle = 'Visual Memory Exercise';
  String _patternType = 'FRUIT_PATTERN';
  // Timer
  Timer? _timer;
  int _timeRemaining = 0;
  int _studyTime = 8; // Default study time, will be overridden by API
  late String _sessionId;
  // Grid data
  List<List<bool>> _targetGrid = [];
  List<List<bool>> _playerGrid = [];
  List<List<String>> _targetColors = [];
  List<List<String>> _playerColors = [];
  List<List<String>> _targetItems = [];
  List<List<String>> _playerItems = [];
  int _gridSize = 3;
  int _numCircles = 3;

  // Game configuration
  String _instructions = '';
  String _gameId = '';
  int _targetScore = 0;
  int _rewardPoints = 0;
  String _levelId = '1';
  int _totalTime = 0;

  // Scoring flags
  bool _allRoundsPerfect = true;
  bool _lastRoundPerfect = false;
  int _lastCorrectPositions = 0;
  int _lastTotalTargets = 0;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  String? _origin;
  @override
  void initState() {
    super.initState();
    if (widget.extra is Map) {
      final map = widget.extra as Map;
      final from = map['from'];
      if (from is String) _origin = from;
    }
    _initializeAnimations();
    _initializeGame();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
  }

  Future<void> _initializeGame() async {
    // Pick selected game by type from constants
    _gameType = widget.gameType;
    Map<String, dynamic> selectedGame;

    // Try to find by type first (e.g., 'object_memory')
    try {
      selectedGame = GameConstants.visualMemoryGames.firstWhere(
        (g) => (g['type'] as String) == _gameType,
      );
    } catch (e) {
      // If not found by type, try to find by name (e.g., 'Object Memory')
      try {
        selectedGame = GameConstants.visualMemoryGames.firstWhere(
          (g) => (g['name'] as String) == _gameType,
        );
      } catch (e) {
        // Default fallback
        selectedGame = const {
          'name': 'Visual Memory Exercise',
          'difficulty': 'medium',
          'type': 'FLAG_PATTERN',
        };
      }
    }

    _gameTitle = 'Visual Memory Exercise';
    _difficulty = (selectedGame['difficulty'] as String?) ?? 'medium';

    final loadedGame = await HiveUserService().getAssignedGames();
    if (loadedGame != null) {
      // Determine total rounds and current round from assigned game
      final levels = loadedGame.levels;
      final currentLevelNumber = loadedGame.gameStatus != 'completed'
          ? loadedGame.level
          : 1;
      final currentLevel = levels.firstWhere(
        (l) => l.levelNumber == currentLevelNumber,
        orElse: () => levels.first,
      );

      setState(() {
        _instructions = loadedGame.instructions;
        _gameId = loadedGame.id;
        _targetScore = loadedGame.targetScore ?? 0;
        _patternType = currentLevel.patternType ?? _patternType;
        _rewardPoints = currentLevel.rewards?.first.points ?? 0;
        _totalRounds = levels.length;
        _currentRound = currentLevelNumber;
        _levelId = currentLevel.levelId;
        _studyTime = currentLevel.timer ?? _studyTime;
        _timeRemaining = _studyTime;
        _totalTime = _studyTime;

        // Configure grid based on difficulty of current level if available
        final difficulty = currentLevel.difficultyLevel ?? _difficulty;
        _difficulty = difficulty;
        final gameConfig = GameConstants.visualMemoryData[difficulty];
        if (gameConfig != null) {
          _gridSize = gameConfig['gridSize'] as int;
          _numCircles = gameConfig['circles'] as int;
        }
      });
    }

    _initializeGrids();
    _fadeController.forward();
  }

  void _initializeGrids() {
    _targetGrid = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => false),
    );
    _playerGrid = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => false),
    );
    _targetColors = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => ''),
    );
    _playerColors = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => ''),
    );
    _targetItems = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => ''),
    );
    _playerItems = List.generate(
      _gridSize,
      (i) => List.generate(_gridSize, (j) => ''),
    );
  }

  void _startGame() {
    setState(() {
      _score = 0;
    });
    _startRound();
  }

  Future<void> _startRound() async {
    // Ensure current level context (_levelId, timers, grid) is synced for this round
    final loadedGame = await HiveUserService().getAssignedGames();
    if (loadedGame != null) {
      final levels = loadedGame.levels;
      final currentLevel = levels.firstWhere(
        (l) => l.levelNumber == _currentRound,
        orElse: () => levels.first,
      );
      setState(() {
        _levelId = currentLevel.levelId;
        _studyTime = currentLevel.timer ?? _studyTime;
        _patternType = currentLevel.patternType ?? _patternType;
        _timeRemaining = _studyTime;
        final difficulty = currentLevel.difficultyLevel ?? _difficulty;
        _difficulty = difficulty;
        final gameConfig = GameConstants.visualMemoryData[difficulty];
        if (gameConfig != null) {
          _gridSize = gameConfig['gridSize'] as int;
          _numCircles = gameConfig['circles'] as int;
          // Clamp circles to grid capacity
          final maxCells = _gridSize * _gridSize;
          if (_numCircles > maxCells) {
            _numCircles = maxCells;
          }
          // Fallback to peekTime if no level timer provided
          if (currentLevel.timer == null) {
            final peek = gameConfig['peekTime'] as int?;
            if (peek != null) {
              _studyTime = peek;
              _timeRemaining = _studyTime;
            }
          }
        }
      });
    }

    final activeSessions = await AssignedGamesInfoService.getActiveSession(
      _gameId,
    );

    if (activeSessions != null &&
        activeSessions['details'] == "UNFINISHED_SESSION_FOUND") {
      final shouldContinue = await _showContinueDialog();
      if (shouldContinue == true) {
        setState(() {
          _sessionId = activeSessions['session_ids'][0];
        });
      } else {
        final config = await AssignedGamesInfoService.startGame(
          gameId: _gameId,
          levelId: _levelId,
        );
        setState(() {
          _sessionId = config?['session_id'];
        });
      }
    } else {
      final config = await AssignedGamesInfoService.startGame(
        gameId: _gameId,
        levelId: _levelId,
      );
      setState(() {
        _sessionId = config?['session_id'];
      });
    }
    _generateTargetPattern();
    _startStudyPhase();
  }

  Future<bool> _showContinueDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Continue Previous Session?'),
              content: const Text(
                'You have an unfinished session. Would you like to continue?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text("No"),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text("Yes"),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _generateTargetPattern() {
    final random = Random();
    _initializeGrids();

    // Get the appropriate data based on game type
    List<String> itemPool = _getItemPoolForGameType();

    // Place items randomly
    int itemsPlaced = 0;
    while (itemsPlaced < _numCircles) {
      int row = random.nextInt(_gridSize);
      int col = random.nextInt(_gridSize);

      if (!_targetGrid[row][col]) {
        _targetGrid[row][col] = true;

        if (_gameType == 'visual_memory' ||
            _gameType == 'Basic Visual Memory') {
          // Use colors for basic visual memory
          _targetColors[row][col] = GameConstants
              .circleColors[random.nextInt(GameConstants.circleColors.length)];
        } else {
          // Use items for other game types
          _targetItems[row][col] = itemPool[random.nextInt(itemPool.length)];
        }
        itemsPlaced++;
      }
    }
  }

  List<String> _getItemPoolForGameType() {
    switch (_patternType) {
      case 'SHAPE_PATTERN':
        return GameConstants.shapeTypes;
      case 'FACE_PATTERN':
        return GameConstants.faceTypes;
      case 'FLAG_PATTERN':
        return GameConstants.flagTypes;
      case 'FRUIT_PATTERN':
        return GameConstants.fruitTypes;
      default:
        return GameConstants.circleColors; // Default to colors
    }
  }

  void _startStudyPhase() {
    setState(() {
      _currentPhase = VisualMemoryPhase.study;
      _timeRemaining = _studyTime;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });

      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecallPhase();
      }
    });
  }

  void _startRecallPhase() {
    setState(() {
      _currentPhase = VisualMemoryPhase.recall;
    });
  }

  void _onGridTap(int row, int col) {
    if (_currentPhase != VisualMemoryPhase.recall) return;

    // Enforce selection cap: cannot select more than the number of targets
    if (!_playerGrid[row][col]) {
      int currentSelected = 0;
      for (int i = 0; i < _gridSize; i++) {
        for (int j = 0; j < _gridSize; j++) {
          if (_playerGrid[i][j]) currentSelected++;
        }
      }
      if (currentSelected >= _numCircles) {
        return; // ignore extra selections
      }
    }

    setState(() {
      _playerGrid[row][col] = !_playerGrid[row][col];
      if (_playerGrid[row][col]) {
        if (_gameType == 'visual_memory' ||
            _gameType == 'Basic Visual Memory') {
          // Use colors for basic visual memory
          _playerColors[row][col] =
              GameConstants.circleColors[Random().nextInt(
                GameConstants.circleColors.length,
              )];
        } else {
          // Use items for other game types
          List<String> itemPool = _getItemPoolForGameType();
          _playerItems[row][col] = itemPool[Random().nextInt(itemPool.length)];
        }
      } else {
        if (_gameType == 'visual_memory' ||
            _gameType == 'Basic Visual Memory') {
          _playerColors[row][col] = '';
        } else {
          _playerItems[row][col] = '';
        }
      }
    });
  }

  Future<void> _submitAnswer() async {
    int correctPositions = 0;
    int totalTargets = 0;
    bool isExactMatch = true;
    int playerSelectedCount = 0;

    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        if (_targetGrid[i][j]) {
          totalTargets++;
          if (_playerGrid[i][j]) {
            // Check both presence and content match
            bool contentMatches;
            if (_gameType == 'visual_memory' ||
                _gameType == 'Basic Visual Memory') {
              contentMatches = _playerColors[i][j] == _targetColors[i][j];
            } else {
              contentMatches = _playerItems[i][j] == _targetItems[i][j];
            }
            if (contentMatches) {
              correctPositions++;
            } else {
              isExactMatch = false;
            }
          } else {
            isExactMatch = false;
          }
        } else {
          // Player should NOT select cells that were not targets
          if (_playerGrid[i][j]) {
            isExactMatch = false;
          }
        }
      }
    }

    setState(() {
      _lastCorrectPositions = correctPositions;
      _lastTotalTargets = totalTargets;
      _lastRoundPerfect = isExactMatch;
      if (_currentRound == _totalRounds && isExactMatch) {
        print('roundCompleted');
      }
      if (isExactMatch) {
        // advance only on pass
        _currentRound++;
      }
    });

    // End this level with score
    final _ = await AssignedGamesInfoService.endGame(
      gameId: _gameId,
      levelId: _levelId,
      sessionId: _sessionId,
      score: isExactMatch ? _targetScore.toString() : '0',
    );
    // Refresh assigned games config in local storage
    final games = await AssignedGamesInfoService.loadGameConfig(_gameId);
    if (games != null) {
      await HiveUserService().saveAssignedGames(games);
    }

    // If more rounds remain or failed current round, show round results; else show final
    if (_currentRound <= _totalRounds) {
      setState(() {
        _currentPhase = VisualMemoryPhase.roundResults;
      });
    } else {
      setState(() {
        _currentPhase = VisualMemoryPhase.finalResults;
      });
    }
  }

  void _showRoundResult(int roundScore, int correct, int total) {
    setState(() {
      _currentPhase = VisualMemoryPhase.finalResults;
    });
  }

  void _completeGame() {
    setState(() {
      _currentPhase = VisualMemoryPhase.finalResults;
    });
    // Compute final score view only; per-level results are already saved in _submitAnswer
    _score = _allRoundsPerfect ? _targetScore : 0;
  }

  Future<void> _saveGameResults() async {}

  void _restartGame() {
    setState(() {
      _currentPhase = VisualMemoryPhase.instructions;
      _currentRound = 1;
      _score = 0;
      _allRoundsPerfect = true;
      _lastRoundPerfect = false;
      _lastCorrectPositions = 0;
      _lastTotalTargets = 0;
    });
    _initializeGrids();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          _gameTitle,
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.secondaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              if (_origin == 'home') {
                context.go(AppRouter.home);
              } else {
                context.go(AppRouter.exercises);
              }
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.psychology,
            size: 60.sp,
            color: AppTheme.secondaryColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          _gameTitle,
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                _instructions.trim().isEmpty
                    ? '1. Complete $_totalRounds rounds of visual memory\n'
                          '2. Each round: study the pattern for $_studyTime seconds\n'
                          '3. Then recreate the pattern by tapping circles\n'
                          '4. Try to get all positions correct!'
                    : _instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startGame,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInstructionStep(
    String number,
    String text,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 32.w,
          height: 32.h,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: Center(
            child: ResponsiveText(
              number,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
              ),
            ),
          ),
        ),
        SizedBox(width: AppTheme.spacing12),
        Icon(icon, color: color, size: 20.sp),
        SizedBox(width: AppTheme.spacing8),
        Expanded(
          child: ResponsiveText(
            text,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGrid() {
    // Constrain grid width so it doesn't grow too large on wide screens
    return Align(
      alignment: Alignment.center,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 360, // cap grid width (similar footprint across layouts)
        ),
        child: GridView.builder(
          padding: EdgeInsets.all(12.w),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _gridSize,
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.h,
          ),
          itemCount: _gridSize * _gridSize,
          itemBuilder: (context, index) {
            int row = index ~/ _gridSize;
            int col = index % _gridSize;
            bool showItem = _currentPhase == VisualMemoryPhase.study
                ? _targetGrid[row][col]
                : _playerGrid[row][col];
            String itemContent = _currentPhase == VisualMemoryPhase.study
                ? (_gameType == 'visual_memory' ||
                          _gameType == 'Basic Visual Memory'
                      ? _targetColors[row][col]
                      : _targetItems[row][col])
                : (_gameType == 'visual_memory' ||
                          _gameType == 'Basic Visual Memory'
                      ? _playerColors[row][col]
                      : _playerItems[row][col]);

            bool isInteractive = _currentPhase == VisualMemoryPhase.recall;
            bool isSelected = _playerGrid[row][col];

            return GestureDetector(
              onTap: isInteractive ? () => _onGridTap(row, col) : null,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                decoration: BoxDecoration(
                  color: showItem && itemContent.isNotEmpty
                      ? (_gameType == 'visual_memory' ||
                                _gameType == 'Basic Visual Memory'
                            ? _hexToColor(itemContent)
                            : AppTheme.primaryColor.withOpacity(0.8))
                      : (isInteractive && isSelected
                            ? AppTheme.primaryColor.withOpacity(0.28)
                            : AppTheme.dividerColor),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  border: Border.all(
                    color: isInteractive && isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.dividerColor,
                    width: isInteractive && isSelected ? 2.w : 1.w,
                  ),
                  boxShadow: isInteractive && isSelected
                      ? [
                          BoxShadow(
                            color: AppTheme.primaryColor.withOpacity(0.25),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: showItem && itemContent.isNotEmpty
                    ? (_gameType == 'visual_memory' ||
                              _gameType == 'Basic Visual Memory'
                          ? Icon(Icons.circle, color: Colors.white, size: 20.sp)
                          : Center(
                              child: Text(
                                itemContent,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ))
                    : (isInteractive && isSelected
                          ? Icon(
                              Icons.check,
                              color: AppTheme.primaryColor,
                              size: 18.sp,
                            )
                          : null),
              ),
            );
          },
        ),
      ),
    );
  }

  Color _hexToColor(String hexColor) {
    hexColor = hexColor.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 1000,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case VisualMemoryPhase.instructions:
        return _buildInstructionsPhase();
      case VisualMemoryPhase.study:
        return _buildStudyPhase();
      case VisualMemoryPhase.recall:
        return _buildRecallPhase();
      case VisualMemoryPhase.roundResults:
        return _buildRoundResultsPhase();
      case VisualMemoryPhase.finalResults:
        return _buildFinalResultsPhase();
    }
  }

  Widget _buildStudyPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds - Study the pattern: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                  fontSize: 10.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _buildGrid(),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Focus and try to remember the pattern!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.quiz, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds - Recreate the pattern',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                  fontSize: 10.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _buildGrid(),
        ),
        SizedBox(height: AppTheme.spacing24),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.successColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(
              color: AppTheme.successColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: AppTheme.successColor,
                size: 18.sp,
              ),
              SizedBox(width: AppTheme.spacing8),
              Expanded(
                child: ResponsiveText(
                  'Selected: ${_getSelectedCount()}/$_numCircles circles',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        Center(
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _submitAnswer,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing32,
                  vertical: AppTheme.spacing20,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                ),
                elevation: 4,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.send, color: Colors.white, size: 24.sp),
                  SizedBox(width: AppTheme.spacing8),
                  ResponsiveText(
                    'Submit Answer',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  int _getSelectedCount() {
    int count = 0;
    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        if (_playerGrid[i][j]) count++;
      }
    }
    return count;
  }

  Widget _buildRoundResultsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: _lastRoundPerfect
                ? AppTheme.successColor.withOpacity(0.1)
                : AppTheme.warningColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            border: Border.all(
              color: _lastRoundPerfect
                  ? AppTheme.successColor.withOpacity(0.3)
                  : AppTheme.warningColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Icon(
            _lastRoundPerfect ? Icons.check_circle : Icons.info_outline,
            size: 60.sp,
            color: _lastRoundPerfect
                ? AppTheme.successColor
                : AppTheme.warningColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          _lastRoundPerfect ? 'Perfect Round!' : 'Try Again!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.surfaceColor,
                AppTheme.surfaceColor.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: _lastRoundPerfect
                  ? AppTheme.successColor.withOpacity(0.3)
                  : AppTheme.warningColor.withOpacity(0.3),
              width: 2.w,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _lastRoundPerfect ? Icons.star : Icons.trending_up,
                    color: _lastRoundPerfect
                        ? AppTheme.successColor
                        : AppTheme.warningColor,
                    size: 24.sp,
                  ),
                  SizedBox(width: AppTheme.spacing8),
                  ResponsiveText(
                    _lastRoundPerfect ? 'Excellent!' : 'Good effort!',
                    style: AppTheme.headlineMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildScoreStat(
                    'Correct',
                    '$_lastCorrectPositions',
                    Icons.check_circle,
                    AppTheme.successColor,
                  ),
                  Container(
                    width: 1.w,
                    height: 40.h,
                    color: AppTheme.dividerColor,
                  ),
                  _buildScoreStat(
                    'Total',
                    '$_lastTotalTargets',
                    Icons.circle,
                    AppTheme.textSecondary,
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing16),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing16,
                  vertical: AppTheme.spacing8,
                ),
                decoration: BoxDecoration(
                  color: _lastRoundPerfect
                      ? AppTheme.successColor.withOpacity(0.1)
                      : AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),

                child: ResponsiveText(
                  _lastRoundPerfect ? 'You earned $_rewardPoints points!' : '',
                  style: AppTheme.bodyMedium.copyWith(
                    color: _lastRoundPerfect
                        ? AppTheme.successColor
                        : AppTheme.warningColor,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        if (_currentRound <= _totalRounds)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _currentPhase = VisualMemoryPhase.study;
                });
                _startRound();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing32,
                  vertical: AppTheme.spacing20,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                ),
                elevation: 4,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _lastRoundPerfect ? Icons.arrow_forward : Icons.refresh,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                  SizedBox(width: AppTheme.spacing8),
                  ResponsiveText(
                    _lastRoundPerfect ? 'Next Round' : 'Retry Round',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _currentPhase = VisualMemoryPhase.finalResults;
                });
                _completeGame();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentColor,
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing32,
                  vertical: AppTheme.spacing20,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                ),
                elevation: 4,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.flag, color: Colors.white, size: 24.sp),
                  SizedBox(width: AppTheme.spacing8),
                  ResponsiveText(
                    'Finish Exercise',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildScoreStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildFinalResultsPhase() {
    // Use the last round outcome for final message
    final bool achievedPerfect = _lastRoundPerfect;
    final int finalScore = achievedPerfect ? _targetScore : 0;
    final int rewardPoints = achievedPerfect ? _rewardPoints : 0;

    final double accuracyPct = _lastTotalTargets > 0
        ? (_lastCorrectPositions / _lastTotalTargets) * 100
        : 0;

    Color finalScoreColor;
    String finalScoreMessage;
    IconData finalScoreIcon;

    if (achievedPerfect) {
      finalScoreColor = AppTheme.successColor;
      finalScoreMessage = 'All Round Completed';
      finalScoreIcon = Icons.check;
    } else {
      finalScoreColor = AppTheme.errorColor;
      finalScoreMessage = 'Incorrect';
      finalScoreIcon = Icons.close;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Central Error/Success Indicator
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: finalScoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(finalScoreIcon, size: 60.sp, color: finalScoreColor),
        ),
        SizedBox(height: AppTheme.spacing32),

        // Main Message
        ResponsiveText(
          finalScoreMessage,
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: finalScoreColor,
          ),
          textAlign: TextAlign.center,
        ),

        // Explanation Section
        SizedBox(height: AppTheme.spacing24),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(color: AppTheme.dividerColor, width: 1.w),
          ),
          child: Column(
            children: [
              ResponsiveText(
                'Explanation:',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Pattern: $_lastCorrectPositions/$_lastTotalTargets positions correct',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                achievedPerfect ? 'Score: $_targetScore' : '',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),

              ResponsiveText(
                'You earned: $rewardPoints reward points',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // Action Buttons
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => context.go(AppRouter.exercises),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing24,
                    vertical: AppTheme.spacing16,
                  ),
                  side: BorderSide(color: AppTheme.primaryColor, width: 2.w),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                  ),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _restartGame,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.successColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing24,
                    vertical: AppTheme.spacing16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                  ),
                  elevation: 2,
                ),
                child: ResponsiveText(
                  'Try Again',
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinalScoreStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }
}

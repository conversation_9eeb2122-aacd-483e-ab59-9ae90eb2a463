class GameConfig {
  final String id;
  final String title;
  final String category;
  final int level;
  // final int? timer;
  final String instructions;
  final GameAssets? assets;
  final GameLogic? logic;
  final List<GameLevel> levels;
  final int? targetScore;
  final String? levelId;
  final String? patternType;
  final String? sessionStatus;
  final String? currentLevelId;
  final String? gameLevelStatus;
  final String? gameStatus;

  GameConfig({
    required this.id,
    required this.title,
    required this.category,
    required this.level,
    // this.timer,
    required this.instructions,
    required this.assets,
    required this.logic,
    required this.levels,
    this.levelId,
    this.targetScore,
    this.patternType,
    this.sessionStatus,
    this.currentLevelId,
    this.gameLevelStatus,
    this.gameStatus,
  });

  factory GameConfig.fromJson(Map<String, dynamic> json) {
    return GameConfig(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      category: json['category'] ?? '',
      level: json['current_level'] ?? 0, // default 0 if null
      // timer: json['timer'], // nullable int stays nullable
      instructions: json['instructions'] ?? '',
      assets: json['assets'] != null
          ? GameAssets.fromJson(json['assets'])
          : null,
      logic: json['logic'] != null ? GameLogic.fromJson(json['logic']) : null,
      levels: (json['levels'] as List? ?? [])
          .map((e) => GameLevel.fromJson(e))
          .toList(),
      targetScore: json['target_score'],
      currentLevelId: json['current_level_id'] ?? '',
      // patternType: json['pattern_type'],
      sessionStatus: json['session_status'],
      gameLevelStatus: json['game_level_status'],
      gameStatus: json['game_status'],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'category': category,
    'current_level': level,
    // 'timer': timer,
    'instructions': instructions,
    'assets': assets?.toJson(),
    'logic': logic?.toJson(),
    'levels': levels.map((e) => e.toJson()).toList(),
    'target_score': targetScore,
    'level_id': levelId,
    // 'pattern_type': patternType,
    'session_status': sessionStatus,
    'current_level_id': currentLevelId,
    'game_level_status': gameLevelStatus,
    'game_status': gameStatus,
  };
}

class GameLevel {
  final String status;
  final List<LevelReward>? rewards; // nullable now
  final String levelId;
  final int levelNumber;
  final String? difficultyLevel;
  final String? patternType;
  final int? timer;
  GameLevel({
    required this.status,
    this.rewards,
    required this.levelId,
    required this.levelNumber,
    this.difficultyLevel,
    this.patternType,
    this.timer,
  });

  factory GameLevel.fromJson(Map<String, dynamic> json) {
    return GameLevel(
      status: json['status'],
      rewards: json['rewards'] != null
          ? (json['rewards'] as List)
                .map((e) => LevelReward.fromJson(e))
                .toList()
          : null,
      levelId: json['level_id'],
      timer: json['timer'],
      levelNumber: json['level_number'],
      difficultyLevel: json['difficulty_level'] ?? '',
      patternType: json['pattern_type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'status': status,
    'rewards': rewards?.map((e) => e.toJson()).toList(),
    'level_id': levelId,
    'level_number': levelNumber,
    'difficulty_level': difficultyLevel,
    'pattern_type': patternType,
    'timer': timer,
  };
}

class LevelReward {
  final List<RewardBadge> badges;
  final int? points; // nullable int
  final String rewardId;
  final String rewardName;
  final String? patternType; // nullable String
  final String? description;

  LevelReward({
    required this.badges,
    this.points,
    required this.rewardId,
    required this.rewardName,
    this.patternType,
    this.description,
  });

  factory LevelReward.fromJson(Map<String, dynamic> json) {
    return LevelReward(
      badges: (json['badges'] as List? ?? [])
          .map((e) => RewardBadge.fromJson(e))
          .toList(),
      points: json['points'], // will be null if not present
      rewardId: json['reward_id'] ?? '',
      rewardName: json['reward_name'] ?? '',
      patternType: json['pattern_type'], // can be null
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'badges': badges.map((e) => e.toJson()).toList(),
    'points': points,
    'reward_id': rewardId,
    'reward_name': rewardName,
    'pattern_type': patternType,
    'description': description,
  };
}

class RewardBadge {
  final String badgeId;
  final String badgeName;

  RewardBadge({required this.badgeId, required this.badgeName});

  factory RewardBadge.fromJson(Map<String, dynamic> json) {
    return RewardBadge(
      badgeId: json['badge_id'],
      badgeName: json['badge_name'],
    );
  }

  Map<String, dynamic> toJson() => {
    'badge_id': badgeId,
    'badge_name': badgeName,
  };
}

// Existing GameAssets & GameLogic stay unchanged
class GameAssets {
  final List<String>? words;
  final List<Map<String, dynamic>>? problems;
  final List<Map<String, dynamic>>? patterns;
  final List<Map<String, dynamic>>? targetShapes;
  final List<Map<String, dynamic>>? distractorShapes;
  final String? gridSize;
  final List<String>? shapes;
  final List<String>? colors;
  final List<List<int>>? positions;
  final int? sequenceLength;
  final List<int>? sequence;
  final int? memorizationTime;

  GameAssets({
    this.words,
    this.problems,
    this.patterns,
    this.targetShapes,
    this.distractorShapes,
    this.gridSize,
    this.shapes,
    this.colors,
    this.positions,
    this.sequenceLength,
    this.sequence,
    this.memorizationTime,
  });

  factory GameAssets.fromJson(Map<String, dynamic>? json) {
    if (json == null) return GameAssets();
    return GameAssets(
      words: (json['words'] as List?)?.cast<String>(),
      problems: (json['problems'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      patterns: (json['patterns'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      targetShapes: (json['target_shapes'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      distractorShapes: (json['distractor_shapes'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      gridSize: json['grid_size'],
      shapes: (json['shapes'] as List?)?.cast<String>(),
      colors: (json['colors'] as List?)?.cast<String>(),
      positions: (json['positions'] as List?)
          ?.map((pos) => List<int>.from(pos))
          .toList(),
      sequenceLength: json['sequence_length'],
      sequence: (json['sequence'] as List?)?.cast<int>(),
      memorizationTime: json['memorization_time'],
    );
  }

  Map<String, dynamic> toJson() => {
    'words': words,
    'problems': problems,
    'patterns': patterns,
    'target_shapes': targetShapes,
    'distractor_shapes': distractorShapes,
    'grid_size': gridSize,
    'shapes': shapes,
    'colors': colors,
    'positions': positions,
    'sequence_length': sequenceLength,
    'sequence': sequence,
    'memorization_time': memorizationTime,
  };
}

class GameLogic {
  final int minCorrect;
  final dynamic nextGameUnlocked;

  GameLogic({required this.minCorrect, required this.nextGameUnlocked});

  factory GameLogic.fromJson(Map<String, dynamic>? json) {
    if (json == null) return GameLogic(minCorrect: 0, nextGameUnlocked: false);
    return GameLogic(
      minCorrect: json['min_correct'] ?? 0,
      nextGameUnlocked: json['next_game_unlocked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'min_correct': minCorrect,
    'next_game_unlocked': nextGameUnlocked,
  };
}

class AssignedGameInfo {
  final String? gameId;
  final String? gameName;
  final String? categoryName;
  final String? gameType;
  final String? assignedType;
  final String? assignedStatus;
  final String? thumbnailUrl;
  final String? description;
  final String? difficultyLevel;
  final bool? isActive;
  final int? noOfLevels;
  final int? estimatedDuration;
  final String? type;
  final String? iconPath;
  final bool? isCompleted;
  final int? lastScore;
  final DateTime? lastPlayedAt;
  AssignedGameInfo({
    this.gameId,
    this.gameName,
    this.categoryName,
    this.gameType,
    this.assignedType,
    this.assignedStatus,
    this.thumbnailUrl,
    this.description,
    this.difficultyLevel,
    this.isActive,
    this.noOfLevels,
    this.estimatedDuration,
    this.type,
    this.iconPath,
    this.isCompleted,
    this.lastScore,
    this.lastPlayedAt,
  });

  factory AssignedGameInfo.fromJson(Map<String, dynamic> json) {
    return AssignedGameInfo(
      gameId: json['game_id'],
      gameName: json['game_name'],
      categoryName: json['category_name'],
      gameType: json['game_type'],
      assignedType: json['assigned_type'],
      assignedStatus: json['assigned_status'],
      thumbnailUrl: json['thumbnail_url'],
      description: json['description'],
      difficultyLevel: json['difficulty_level'],
      isActive: json['is_active'],
      noOfLevels: json['no_of_levels'],
      estimatedDuration: json['estimated_duration_sec'],
      type: json['type'],
      iconPath: json['icon_path'],
      isCompleted: json['is_completed'],
      lastScore: json['last_score'],
      lastPlayedAt: json['last_played_at'] != null
          ? DateTime.tryParse(json['last_played_at'])
          : null,
    );
  }
}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'exercise.freezed.dart';

@freezed
class Exercise with _$Exercise {
  const factory Exercise({
    required String id,
    required String title,
    required String description,
    required String exerciseType,
    String? categoryName,
    String? assignedType,
    String? assignedStatus,
    required int difficulty,
    required int estimatedDuration,
    required String iconPath,
    @Default(false) bool isCompleted,
    int? lastScore,
    DateTime? lastPlayedAt,
  }) = _Exercise;
}

@freezed
class ExerciseResult with _$ExerciseResult {
  const factory ExerciseResult({
    required String exerciseId,
    required int score,
    required int timeSpent,
    required DateTime completedAt,
    required Map<String, dynamic> details,
  }) = _ExerciseResult;
}

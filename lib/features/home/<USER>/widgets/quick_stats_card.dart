import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/router/app_router.dart';
import 'package:recallloop/features/home/<USER>/models/game_summary_stat_model.dart';
import 'package:recallloop/features/home/<USER>/bloc/home/<USER>/home_bloc_bloc.dart';
import 'package:recallloop/features/mood/presentation/bloc/mood/mood_bloc.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

class QuickStatsCard extends StatelessWidget {
  const QuickStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBlocBloc, HomeBlocState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withValues(alpha: 0.95),
                Colors.white.withValues(alpha: 0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing20),
                state.when(
                  initial: () => _buildLoadingState(),
                  loading: () => _buildLoadingState(),
                  loaded: (stats) => _buildGameStatsGrid(context, stats),
                  error: (_) => _buildErrorState(),
                ),
                SizedBox(height: AppTheme.spacing20),
                state.when(
                  initial: () => _buildMoodCheckIn(context, null),
                  loading: () => _buildMoodCheckIn(context, null),
                  loaded: (stats) => _buildMoodCheckIn(context, stats),
                  error: (_) => _buildMoodCheckIn(context, null),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.analytics, color: AppTheme.primaryColor, size: 20.sp),
        SizedBox(width: AppTheme.spacing8),
        ResponsiveText(
          'Quick Stats',
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Unable to load stats',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameStatsGrid(BuildContext context, GameSummaryStats stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.games,
                label: 'My Excercises',
                value: stats.assignedGames,
                color: AppTheme.primaryColor,
                onTap: () {
                  context.go(AppRouter.exercises);
                },
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.play_arrow,
                label: 'Played Games',
                value: stats.playedGames,
                color: AppTheme.secondaryColor,
                onTap: () {
                  print("Played Games clicked!");
                },
              ),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing12),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.score,
                label: 'Average Score',
                value: stats.avgScore,
                color: AppTheme.accentColor,
                onTap: () {
                  print("Average Score clicked!");
                },
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.stars,
                label: 'Rewards',
                value: stats.rewardPoints,
                color: AppTheme.warningColor,
                onTap: () {
                  print("Rewards clicked!");
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isLarge = true,
    VoidCallback? onTap,
  }) {
    final bool showPointer = label == 'Assigned Games';

    return MouseRegion(
      cursor: showPointer ? SystemMouseCursors.click : SystemMouseCursors.basic,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(
            isLarge ? AppTheme.spacing16 : AppTheme.spacing12,
          ),
          decoration: BoxDecoration(
            gradient: label == 'Played Games'
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color.fromARGB(255, 4, 223, 52).withOpacity(0.05),
                      const Color.fromARGB(255, 2, 255, 107).withOpacity(0.05),
                    ],
                  )
                : null,
            color: label != 'Played Games' ? color.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(
              color: label == 'Played Games'
                  ? const Color.fromARGB(255, 2, 255, 107).withOpacity(0.15)
                  : color.withOpacity(0.2),
              width: 1.w,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isLarge ? 8.w : 6.w),
                    decoration: BoxDecoration(
                      color: label == 'Played Games'
                          ? const Color.fromARGB(
                              255,
                              2,
                              255,
                              107,
                            ).withOpacity(0.2)
                          : color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: isLarge ? 20.sp : 16.sp,
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: isLarge ? AppTheme.spacing12 : AppTheme.spacing8,
              ),
              ResponsiveText(
                value,
                style:
                    (isLarge ? AppTheme.headlineMedium : AppTheme.titleMedium)
                        .copyWith(fontWeight: FontWeight.w700, color: color),
              ),
              SizedBox(height: 4.h),
              ResponsiveText(
                label,
                style: (isLarge ? AppTheme.bodySmall : AppTheme.labelSmall)
                    .copyWith(color: AppTheme.textSecondary),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMoodCheckIn(BuildContext context, GameSummaryStats? stats) {
    List<String> badgeImages = [];
    if (stats?.badgesReceived != null) {
      badgeImages = stats!.badgesReceived!
          .map((badge) => badge['icon_url'] as String? ?? '')
          .where((url) => url.isNotEmpty)
          .take(5)
          .toList();
    }
    // Always show badges section (even if empty)
    return _buildBadgesSection(badgeImages);
  }

  Widget _buildBadgesSection(List<String> badgeImages) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color.fromARGB(255, 4, 223, 52).withOpacity(0.05),
            const Color.fromARGB(255, 2, 255, 107).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: AppTheme.accentColor.withOpacity(0.15),
          width: 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.emoji_events,
                  color: AppTheme.accentColor,
                  size: 16.sp,
                ),
              ),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Badges Received',
                style: AppTheme.labelMedium.copyWith(
                  color: const Color.fromARGB(255, 24, 15, 1),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          badgeImages.isEmpty
              ? ResponsiveText(
                  "No badges yet",
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                )
              : _buildLeftAlignedBadgeImages(badgeImages),
        ],
      ),
    );
  }

  Widget _buildLeftAlignedBadgeImages(List<String> badgeImages) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive badge size based on available width
        final screenWidth = MediaQuery.of(context).size.width;
        final isVerySmallScreen = screenWidth < 360;
        final badgeSize = isVerySmallScreen ? 40.0 : 48.0;
        final spacing = isVerySmallScreen ? 6.0 : AppTheme.spacing8;

        return Wrap(
          alignment: WrapAlignment.start,
          spacing: spacing,
          runSpacing: spacing,
          children: badgeImages.map((imageUrl) {
            return Container(
              width: badgeSize,
              height: badgeSize,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                border: Border.all(
                  color: AppTheme.accentColor.withOpacity(0.2),
                  width: 1.5.w,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.accentColor.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  width: badgeSize,
                  height: badgeSize,
                  placeholder: (context, url) => Container(
                    color: AppTheme.accentColor.withOpacity(0.1),
                    child: Icon(
                      Icons.emoji_events,
                      color: AppTheme.accentColor,
                      size: (badgeSize * 0.5).sp,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: AppTheme.accentColor.withOpacity(0.1),
                    child: Icon(
                      Icons.emoji_events,
                      color: AppTheme.accentColor,
                      size: (badgeSize * 0.5).sp,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  List<Widget> _buildMoodEmojis(BuildContext context) {
    return List.generate(5, (index) {
      final moodLevel = index + 1;
      return GestureDetector(
        onTap: () {
          context.read<MoodBloc>().add(
            MoodEvent.entryAdded(moodLevel: moodLevel),
          );
        },
        child: Container(
          width: 44.w,
          height: 44.h,
          decoration: BoxDecoration(
            color: _getMoodColor(moodLevel).withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(
              color: _getMoodColor(moodLevel).withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Center(
            child: ResponsiveText(
              _getMoodEmoji(moodLevel),
              style: TextStyle(fontSize: 20.sp),
            ),
          ),
        ),
      );
    });
  }

  Color _getMoodColor(int moodLevel) {
    switch (moodLevel) {
      case 1:
        return Colors.red;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.yellow;
      case 4:
        return Colors.lightGreen;
      case 5:
        return Colors.green;
      default:
        return AppTheme.textTertiary;
    }
  }

  String _getMoodEmoji(int moodLevel) {
    switch (moodLevel) {
      case 1:
        return '😢';
      case 2:
        return '😔';
      case 3:
        return '😐';
      case 4:
        return '😊';
      case 5:
        return '😄';
      default:
        return '😐';
    }
  }
}

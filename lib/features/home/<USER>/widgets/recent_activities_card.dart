import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../data/models/game_summary_stat_model.dart';
import '../bloc/home/<USER>/home_bloc_bloc.dart';

class RecentActivitiesCard extends StatefulWidget {
  const RecentActivitiesCard({super.key});

  @override
  State<RecentActivitiesCard> createState() => _RecentActivitiesCardState();
}

class _RecentActivitiesCardState extends State<RecentActivitiesCard> {
  static const int _initialDisplayLimit = 3;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBlocBloc, HomeBlocState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withValues(alpha: 0.95),
                Colors.white.withValues(alpha: 0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing20),
                state.when(
                  initial: () => _buildEmptyState(),
                  loading: () => _buildLoadingState(),
                  loaded: (stats) =>
                      _buildActivitiesList(stats.recentActivities),
                  error: (message) => _buildErrorState(message),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.history, color: AppTheme.secondaryColor, size: 20.sp),
        SizedBox(width: AppTheme.spacing8),
        ResponsiveText(
          'History',
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 150.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.secondaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildErrorState(String? errorMessage) {
    return SizedBox(
      height: 150.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              errorMessage ?? 'Unable to load activities',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: 150.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history_outlined,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'No recent activities',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
            SizedBox(height: AppTheme.spacing4),
            ResponsiveText(
              'Start playing games to see your activity here',
              style: AppTheme.labelSmall.copyWith(color: AppTheme.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivitiesList(List<RecentActivity> activities) {
    if (activities.isEmpty) {
      return _buildEmptyState();
    }

    final displayActivities = activities.take(_initialDisplayLimit).toList();
    final hasMoreActivities = activities.length > _initialDisplayLimit;

    return Column(
      children: [
        ...displayActivities.asMap().entries.map((entry) {
          final index = entry.key;
          final activity = entry.value;
          return Padding(
            padding: EdgeInsets.only(
              bottom: index == displayActivities.length - 1
                  ? 0
                  : AppTheme.spacing12,
            ),
            child: _buildActivityItem(activity),
          );
        }),

        if (hasMoreActivities) ...[
          SizedBox(height: AppTheme.spacing12),
          _buildViewMoreButton(activities.length),
        ],
      ],
    );
  }

  Widget _buildViewMoreButton(int totalCount) {
    return Center(
      child: TextButton.icon(
        onPressed: () {
          context.go(AppRouter.recentactivities);
        },
        style: TextButton.styleFrom(
          backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
          foregroundColor: AppTheme.primaryColor,
          padding: EdgeInsets.symmetric(
            vertical: AppTheme.spacing16,
            horizontal: AppTheme.spacing16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            side: BorderSide(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
        ),
        icon: Icon(Icons.arrow_forward, size: 20.sp),
        label: ResponsiveText(
          'View More',
          style: AppTheme.labelMedium.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(RecentActivity activity) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.1),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          _buildActivityIcon(activity),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  activity.gameName,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (activity.description != null &&
                    activity.description!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  ResponsiveText(
                    activity.description!,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                if (activity.levelName != null &&
                    activity.levelName!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  ResponsiveText(
                    activity.levelName!,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                if (activity.sessionStatus != null) ...[
                  SizedBox(height: 6.h),
                  _buildSessionStatusChip(activity.sessionStatus!),
                ],
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              ResponsiveText(
                activity.timeAgo,
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.textTertiary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSessionStatusChip(String status) {
    Color statusColor;
    IconData statusIcon;
    String displayStatus;

    switch (status.toLowerCase()) {
      case 'completed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle_outline;
        displayStatus = 'Completed';
        break;
      case 'in_progress':
        statusColor = Colors.orange;
        statusIcon = Icons.play_circle_outline;
        displayStatus = 'In Progress';
        break;
      case 'paused':
        statusColor = Colors.blue;
        statusIcon = Icons.pause_circle_outline;
        displayStatus = 'Paused';
        break;
      default:
        statusColor = AppTheme.textTertiary;
        statusIcon = Icons.info_outline;
        displayStatus = status;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing8,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: statusColor.withOpacity(0.3), width: 0.5.w),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 12.sp, color: statusColor),
          SizedBox(width: 4.w),
          ResponsiveText(
            displayStatus,
            style: AppTheme.labelSmall.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityIcon(RecentActivity activity) {
    if (activity.thumbnail != null && activity.thumbnail!.isNotEmpty) {
      return Container(
        width: 48.w,
        height: 48.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          color: AppTheme.primaryColor.withOpacity(0.1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          child: CachedNetworkImage(
            imageUrl: activity.thumbnail!,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: AppTheme.primaryColor.withOpacity(0.2),
              child: Icon(
                Icons.psychology,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: AppTheme.primaryColor.withOpacity(0.2),
              child: Icon(
                Icons.psychology,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
            ),
          ),
        ),
      );
    }

    return Container(
      width: 48.w,
      height: 48.h,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      ),
      child: Icon(Icons.psychology, color: AppTheme.primaryColor, size: 24.sp),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/database/hive_db_helper.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../profile/data/services/profile_services.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';

class HomeHeader extends StatefulWidget {
  const HomeHeader({super.key});

  @override
  State<HomeHeader> createState() => _HomeHeaderState();
}

class _HomeHeaderState extends State<HomeHeader> {
  String? _signedAvatarUrl;
  String? _lastAvatarUrl;
  bool _isFetchingSignedUrl = false;

  Future<void> _fetchSignedAvatarUrl(String? avatarUrl) async {
    if (avatarUrl == null || avatarUrl.isEmpty || !mounted) return;

    if (_isFetchingSignedUrl || _lastAvatarUrl == avatarUrl) return;

    _isFetchingSignedUrl = true;
    _lastAvatarUrl = avatarUrl;

    try {
      final loginModel = await HiveUserService().getCurrentUser();
      final userId = loginModel?.id;
      final accessToken = loginModel?.accessToken ?? '';
      if (userId != null) {
        final fileName = avatarUrl.split('/').last;
        for (int i = 0; i < 3; i++) {
          final signedUrl = await ProfileDetailsService.getSignedAvatarUrl(
            userId: userId,
            accessToken: accessToken,
            fileName: fileName,
          );
          if (signedUrl != null && mounted) {
            setState(() {
              _signedAvatarUrl = signedUrl;
            });
            break;
          }
          await Future.delayed(const Duration(seconds: 1));
        }
        if (_signedAvatarUrl == null) {
          debugPrint('Failed to fetch signed URL after retries');
        }
      }
    } catch (e) {
      debugPrint('Error fetching signed URL: $e');
    } finally {
      _isFetchingSignedUrl = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        state.maybeWhen(
          loaded: (profile) {
            if (profile.avatarUrl != null &&
                profile.avatarUrl != _lastAvatarUrl &&
                !_isFetchingSignedUrl) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _fetchSignedAvatarUrl(profile.avatarUrl);
              });
            }
          },
          orElse: () {},
        );

        return ResponsiveLayout(
          mobile: _buildMobileHeader(context, state),
          tablet: _buildTabletHeader(context, state),
          desktop: _buildDesktopHeader(context, state),
        );
      },
    );
  }

  Widget _buildMobileHeader(BuildContext context, ProfileState state) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      _getGreeting(),
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.secondaryColor,
                        ],
                      ).createShader(bounds),
                      child: ResponsiveText(
                        _getUserName(state),
                        style: AppTheme.headlineMedium.copyWith(
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                          letterSpacing: -0.5,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _buildProfileAvatar(context, state),
            ],
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildStreakInfo(state),
        ],
      ),
    );
  }

  Widget _buildTabletHeader(BuildContext context, ProfileState state) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                _getGreeting(),
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: 8.h),
              ResponsiveText(
                _getUserName(state),
                style: AppTheme.displaySmall.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing12),
              _buildStreakInfo(state),
            ],
          ),
        ),
        SizedBox(width: AppTheme.spacing24),

        _buildProfileAvatar(context, state, size: 64.w),
      ],
    );
  }

  Widget _buildDesktopHeader(BuildContext context, ProfileState state) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.95),
            Colors.white.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  _getGreeting(),
                  style: AppTheme.bodyLarge.copyWith(
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8.h),
                // Enhanced user name with gradient text
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                  ).createShader(bounds),
                  child: ResponsiveText(
                    _getUserName(state),
                    style: AppTheme.displayMedium.copyWith(
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                  ),
                ),
                SizedBox(height: AppTheme.spacing20),
                Row(
                  children: [
                    _buildStreakInfo(state),
                    SizedBox(width: AppTheme.spacing32),
                    _buildMotivationalQuote(),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: AppTheme.spacing32),
          _buildProfileAvatar(context, state, size: 80.w),
        ],
      ),
    );
  }

  Widget _buildProfileAvatar(
    BuildContext context,
    ProfileState state, {
    double? size,
  }) {
    final avatarSize = size ?? 48.w;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          context.go('/profile');
        },
        child: Container(
          width: avatarSize,
          height: avatarSize,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.primaryVariant],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(avatarSize / 2),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: state.maybeWhen(
            loaded: (profile) => _buildAvatarImage(profile, avatarSize),
            orElse: () => _buildDefaultAvatar(avatarSize),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarImage(dynamic profile, double avatarSize) {
    final imageUrl = _signedAvatarUrl ?? profile.avatarUrl;

    if (imageUrl != null && imageUrl.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(avatarSize / 2),
        child: Image.network(
          imageUrl,
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: SizedBox(
                width: avatarSize * 0.5,
                height: avatarSize * 0.5,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.textOnPrimary,
                  ),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Avatar loading error: $error');
            return _buildDefaultAvatar(avatarSize);
          },
        ),
      );
    } else {
      return _buildDefaultAvatar(avatarSize);
    }
  }

  Widget _buildDefaultAvatar(double size) {
    return Icon(Icons.person, color: AppTheme.textOnPrimary, size: size * 0.6);
  }

  Widget _buildStreakInfo(ProfileState state) {
    int streak = state.maybeWhen(
      // loaded: (profile) => profile.exerciseStats.streak,
      loaded: (profile) => profile.levelsCompleted,
      orElse: () => 0,
    );

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing12,
        vertical: AppTheme.spacing8,
      ),
      decoration: BoxDecoration(
        color: AppTheme.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(
          color: AppTheme.successColor.withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.local_fire_department,
            color: AppTheme.successColor,
            size: 16.sp,
          ),
          SizedBox(width: AppTheme.spacing4),
          ResponsiveText(
            '$streak day streak',
            style: AppTheme.labelSmall.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotivationalQuote() {
    final quotes = [
      "Your brain is a muscle. Exercise it daily!",
      "Small steps lead to big improvements.",
      "Consistency is the key to cognitive growth.",
      "Challenge your mind, expand your potential.",
      "Every exercise makes you stronger mentally.",
    ];

    final quote = quotes[DateTime.now().day % quotes.length];

    return Expanded(
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing12),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.3),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.format_quote, color: AppTheme.accentColor, size: 16.sp),
            SizedBox(width: AppTheme.spacing8),
            Expanded(
              child: ResponsiveText(
                quote,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  String _getUserName(ProfileState state) {
    return state.when(
      initial: () => 'Welcome back',
      loading: () => 'Loading...',
      // loaded: (profile) => profile.name,
      loaded: (profile) => '${profile.firstName} ${profile.lastName}',
      uploadingImage: () => 'Uploading...',
      error: (message) => 'Welcome back',
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../home/<USER>/bloc/home/<USER>/home_bloc_bloc.dart';
import '../../data/services/game_access_service.dart';

class RecommendedExercisesCard extends StatefulWidget {
  const RecommendedExercisesCard({super.key});

  @override
  State<RecommendedExercisesCard> createState() =>
      _RecommendedExercisesCardState();
}

class _RecommendedExercisesCardState extends State<RecommendedExercisesCard> {
  bool _showAll = false;
  bool _isRequestingAccess = false;

  final Map<String, String> _localExerciseStatus = {};

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBlocBloc, HomeBlocState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.psychology,
                      color: AppTheme.primaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Exercises You Might Like',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        // context.go(AppRouter.exercises);
                      },
                      child: ResponsiveText(
                        'View All',
                        style: AppTheme.labelMedium.copyWith(
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildLoadingState(),
                  loading: () => _buildLoadingState(),
                  loaded: (stats) =>
                      _buildExercisesList(context, stats.recommendedExercises),
                  error: (message) => _buildErrorState(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Unable to load exercises',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercisesList(BuildContext context, List<dynamic> exercises) {
    debugPrint('--- Recommended Exercises ---');
    for (var e in exercises) {
      debugPrint(e.toString());
    }
    if (exercises.isEmpty) {
      return SizedBox(
        height: 120.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.psychology_outlined,
                color: AppTheme.textTertiary,
                size: 32.sp,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'No recommended exercises',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final availableExercises = exercises;
    final shouldShowViewMore = availableExercises.length > 3;
    final exercisesToShow = _showAll
        ? availableExercises
        : availableExercises.take(3).toList();

    return Column(
      children: [
        ...exercisesToShow.map((exercise) {
          return Padding(
            padding: EdgeInsets.only(bottom: AppTheme.spacing12),
            child: _buildExerciseItem(context, exercise),
          );
        }),
        if (shouldShowViewMore) ...[
          SizedBox(height: AppTheme.spacing8),
          _buildViewMoreButton(availableExercises.length),
        ],
      ],
    );
  }

  Widget _buildViewMoreButton(int totalCount) {
    return Center(
      child: TextButton.icon(
        onPressed: () {
          setState(() {
            _showAll = !_showAll;
          });
        },
        style: TextButton.styleFrom(
          backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
          foregroundColor: AppTheme.primaryColor,
          padding: EdgeInsets.symmetric(
            vertical: AppTheme.spacing16,
            horizontal: AppTheme.spacing16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            side: BorderSide(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
        ),
        icon: Icon(
          _showAll ? Icons.expand_less : Icons.expand_more,
          size: 20.sp,
        ),
        label: ResponsiveText(
          _showAll ? 'View Less' : 'View More',
          style: AppTheme.labelMedium.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildExerciseItem(
    BuildContext context,
    Map<String, dynamic> exercise,
  ) {
    final gameName = exercise['game_name'] ?? 'Unknown Game';
    final description = exercise['description'] ?? '';
    final categoryName = exercise['category_name'] ?? '';
    final thumbnailUrl = exercise['thumbnail_url'];
    final gameId = exercise['game_id'] ?? '';
    final estimatedDurationSec = exercise['estimated_duration_sec'];
    final difficultyLevel = exercise['difficulty_level'];

    final originalAssignedStatus =
        exercise['assigned_status'] ?? 'Not Assigned';
    final assignedStatus =
        _localExerciseStatus[gameId] ?? originalAssignedStatus;

    final estimatedDuration = estimatedDurationSec != null
        ? (estimatedDurationSec / 60).round()
        : null;

    return GestureDetector(
      onTap: () {
        // _startExercise(context, exercise);
      },
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: _getExerciseColor(categoryName).withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          border: Border.all(
            color: _getExerciseColor(categoryName).withOpacity(0.2),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: _getExerciseColor(categoryName).withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: thumbnailUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                      child: Image.network(
                        thumbnailUrl,
                        width: 48.w,
                        height: 48.h,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            _getExerciseIcon(categoryName),
                            color: _getExerciseColor(categoryName),
                            size: 24.sp,
                          );
                        },
                      ),
                    )
                  : Icon(
                      _getExerciseIcon(categoryName),
                      color: _getExerciseColor(categoryName),
                      size: 24.sp,
                    ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    gameName,
                    style: AppTheme.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  if (description.isNotEmpty) ...[
                    SizedBox(height: 4.h),
                    ResponsiveText(
                      description,
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    runSpacing: 4.h,
                    children: [
                      if (categoryName.isNotEmpty)
                        _buildExerciseTag(
                          icon: Icons.category,
                          text: categoryName,
                          color: AppTheme.textTertiary,
                        ),
                      if (estimatedDuration != null)
                        _buildExerciseTag(
                          icon: Icons.timer,
                          text: '${estimatedDuration}min',
                          color: AppTheme.textTertiary,
                        ),
                      if (difficultyLevel != null)
                        _buildExerciseTag(
                          icon: Icons.star,
                          text: _getDifficultyText(difficultyLevel),
                          color: _getDifficultyColor(difficultyLevel),
                        ),
                      if (assignedStatus.toLowerCase() == 'requested')
                        _buildExerciseTag(
                          icon: Icons.stars,
                          text: 'Requested',
                          color: _getAssignedStatusColor(assignedStatus),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                if (assignedStatus.toLowerCase() != 'requested') {
                  _showConfirmationDialog(
                    context,
                    categoryName,
                    gameId,
                    exercise,
                  );
                }
              },
              child: Icon(
                Icons.play_circle_filled,
                color: _getExerciseColor(categoryName),
                size: 32.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12.sp),
          SizedBox(width: 2.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getAssignedStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppTheme.warningColor;
      case 'assigned':
        return AppTheme.successColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'requested':
        return AppTheme.accentColor;
      default:
        return AppTheme.textTertiary;
    }
  }

  void _startExercise(
    BuildContext context,
    String categoryName,
    String gameId,
  ) {
    switch (categoryName.toLowerCase()) {
      case 'word recall':
        context.go(AppRouter.wordRecall);
        break;
      case 'number sequence':
        context.go(AppRouter.numberSequence);
        break;
      case 'shape matching':
        context.go(AppRouter.shapeMatching);
        break;
      case 'pattern recognition':
        context.go(AppRouter.patternRecognition);
        break;
      case 'object memory':
        context.go(AppRouter.objectMemory);
        break;
      default:
        context.go(AppRouter.exercises);
        break;
    }
  }

  IconData _getExerciseIcon(String categoryName) {
    final category = categoryName.toLowerCase();
    if (category.contains('word') || category.contains('recall')) {
      return Icons.record_voice_over;
    } else if (category.contains('number') || category.contains('sequence')) {
      return Icons.calculate;
    } else if (category.contains('pattern')) {
      return Icons.pattern;
    } else if (category.contains('memory') || category.contains('object')) {
      return Icons.memory;
    } else if (category.contains('spatial')) {
      return Icons.rotate_right;
    }
    return Icons.psychology;
  }

  Color _getExerciseColor(String categoryName) {
    final category = categoryName.toLowerCase();
    if (category.contains('word') || category.contains('recall')) {
      return AppTheme.secondaryColor;
    } else if (category.contains('number') || category.contains('sequence')) {
      return AppTheme.warningColor;
    } else if (category.contains('pattern')) {
      return AppTheme.accentColor;
    } else if (category.contains('shape') || category.contains('matching')) {
      return AppTheme.successColor;
    } else if (category.contains('memory') || category.contains('object')) {
      return AppTheme.primaryColor;
    } else if (category.contains('spatial')) {
      return AppTheme.successColor;
    }
    return AppTheme.primaryColor;
  }

  String _getDifficultyText(dynamic difficulty) {
    if (difficulty == null) return 'Medium';

    int level = 3;
    if (difficulty is int) {
      level = difficulty;
    } else if (difficulty is String) {
      level = int.tryParse(difficulty) ?? 3;
    }

    switch (level) {
      case 1:
        return 'Easy';
      case 2:
        return 'Easy';
      case 3:
        return 'Medium';
      case 4:
        return 'Hard';
      case 5:
        return 'Expert';
      default:
        return 'Medium';
    }
  }

  Color _getDifficultyColor(dynamic difficulty) {
    if (difficulty == null) return AppTheme.accentColor;

    int level = 3;
    if (difficulty is int) {
      level = difficulty;
    } else if (difficulty is String) {
      level = int.tryParse(difficulty) ?? 3;
    }

    switch (level) {
      case 1:
      case 2:
        return AppTheme.successColor;
      case 3:
        return AppTheme.accentColor;
      case 4:
      case 5:
        return AppTheme.errorColor;
      default:
        return AppTheme.accentColor;
    }
  }

  void _showConfirmationDialog(
    BuildContext context,
    String categoryName,
    String gameId,
    Map<String, dynamic> exercise,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Confirmation'),
              content: _isRequestingAccess
                  ? const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Requesting game access...'),
                      ],
                    )
                  : const Text(
                      'Do you want to submit a request to access this game?',
                    ),
              actions: _isRequestingAccess
                  ? []
                  : [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('No'),
                      ),
                      TextButton(
                        onPressed: () async {
                          setState(() {
                            _isRequestingAccess = true;
                          });

                          try {
                            final result =
                                await GameAccessService.requestGameAccess(
                                  gameId: gameId,
                                );

                            if (mounted) {
                              Navigator.of(context).pop();

                              if (result != null &&
                                  result['status'] == 'success') {
                                this.setState(() {
                                  _localExerciseStatus[gameId] = 'Requested';
                                });

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      result['message'] ??
                                          'Game access requested successfully',
                                    ),
                                    backgroundColor: AppTheme.successColor,
                                    duration: const Duration(seconds: 3),
                                  ),
                                );

                                Future.delayed(const Duration(seconds: 2), () {
                                  if (mounted) {
                                    context.read<HomeBlocBloc>().add(
                                      const HomeBlocEvent.fetchGameSummaryStats(
                                        tenantCode: '',
                                        userId: '',
                                        accessToken: '',
                                      ),
                                    );
                                  }
                                });
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Failed to request game access. Please try again.',
                                    ),
                                    backgroundColor: Colors.red,
                                    duration: Duration(seconds: 3),
                                  ),
                                );
                              }
                            }
                          } catch (e) {
                            if (mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'An error occurred. Please try again.',
                                  ),
                                  backgroundColor: Colors.red,
                                  duration: Duration(seconds: 3),
                                ),
                              );
                            }
                          } finally {
                            if (mounted) {
                              setState(() {
                                _isRequestingAccess = false;
                              });
                            }
                          }
                        },
                        child: const Text('Yes'),
                      ),
                    ],
            );
          },
        );
      },
    );
  }
}

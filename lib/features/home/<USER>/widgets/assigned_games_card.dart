import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/core/router/app_router.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../exercises/data/models/assigned_game_model.dart';

class AssignedGamesCard extends StatelessWidget {
  final List<AssignedGameInfo> assignedGames;
  final bool isLoading;

  const AssignedGamesCard({
    super.key,
    required this.assignedGames,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.games, color: AppTheme.accentColor, size: 20.sp),
                SizedBox(width: AppTheme.spacing8),
                ResponsiveText(
                  'My Excercise',
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const Spacer(),
                if (assignedGames.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      context.go(AppRouter.exercises);
                    },
                    child: ResponsiveText(
                      'View All',
                      style: AppTheme.labelMedium.copyWith(
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: AppTheme.spacing16),
            if (isLoading)
              _buildLoadingState()
            else if (assignedGames.isEmpty)
              _buildEmptyState()
            else
              _buildGamesList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.games_outlined,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'No assigned games',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGamesList(BuildContext context) {
    return Column(
      children: assignedGames.take(10).map((game) {
        return Padding(
          padding: EdgeInsets.only(bottom: AppTheme.spacing12),
          child: _buildGameItem(context, game),
        );
      }).toList(),
    );
  }

  Widget _buildGameItem(BuildContext context, AssignedGameInfo game) {
    return GestureDetector(
      onTap: () async {
        // Show loading indicator dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) => Center(
            child: CircularProgressIndicator(color: AppTheme.primaryColor),
          ),
        );

        try {
          await _navigateToExercise(context, game);
        } catch (e) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Failed to load game config')));
        } finally {
          Navigator.of(context).pop(); // Close loading indicator
        }
      },
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.2),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                child:
                    game.thumbnailUrl != null && game.thumbnailUrl!.isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: game.thumbnailUrl!,
                        fit: BoxFit.cover,
                        width: 48.w,
                        height: 48.h,
                        placeholder: (context, url) => Container(
                          width: 48.w,
                          height: 48.h,
                          color: AppTheme.accentColor.withOpacity(0.2),
                          child: Icon(
                            Icons.games,
                            color: AppTheme.accentColor,
                            size: 24.sp,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          width: 48.w,
                          height: 48.h,
                          color: AppTheme.accentColor.withOpacity(0.2),
                          child: Icon(
                            Icons.games,
                            color: AppTheme.accentColor,
                            size: 24.sp,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.games,
                        color: AppTheme.accentColor,
                        size: 24.sp,
                      ),
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    game.gameName ?? 'Unknown Game',
                    style: AppTheme.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: AppTheme.spacing4),
                  ResponsiveText(
                    game.categoryName ?? 'Unknown Category',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    children: [
                      _buildGameTag(
                        icon: Icons.layers,
                        text: '${game.noOfLevels ?? 0} levels',
                        color: AppTheme.textTertiary,
                      ),
                      if (game.isActive == true)
                        _buildGameTag(
                          icon: Icons.check_circle,
                          text: 'Active',
                          color: AppTheme.successColor,
                        ),
                      if (game.assignedStatus != null &&
                          game.assignedStatus!.isNotEmpty)
                        _buildAssignedStatusTag(game.assignedStatus!),
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.play_circle_filled,
              color: AppTheme.accentColor,
              size: 32.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing8,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12.sp, color: color),
          SizedBox(width: 4.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignedStatusTag(String status) {
    Color statusColor;
    IconData statusIcon;

    switch (status.toLowerCase()) {
      case 'approved':
        statusColor = AppTheme.successColor;
        statusIcon = Icons.check_circle_outline;
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.pending_outlined;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel_outlined;
        break;
      case 'assigned':
        statusColor = AppTheme.primaryColor;
        statusIcon = Icons.assignment_outlined;
        break;
      default:
        statusColor = AppTheme.textTertiary;
        statusIcon = Icons.info_outline;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing8,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: statusColor.withOpacity(0.3), width: 0.5.w),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 12.sp, color: statusColor),
          SizedBox(width: 4.w),
          ResponsiveText(
            status,
            style: AppTheme.labelSmall.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToExercise(
    BuildContext context,
    AssignedGameInfo game,
  ) async {
    final games = await AssignedGamesInfoService.loadGameConfig(game.gameId!);
    if (games != null) {
      await HiveUserService().saveAssignedGames(games);
    }
    switch (game.categoryName) {
      case 'Word Recall':
        context.go(AppRouter.wordRecall, extra: {'from': 'home'});
        break;
      case 'number_sequence':
        context.go(AppRouter.numberSequence, extra: {'from': 'home'});
        break;
      case 'shape_matching':
        context.go(AppRouter.shapeMatching, extra: {'from': 'home'});
        break;
      case 'Pattern Recognition':
        context.go(AppRouter.patternRecognition, extra: {'from': 'home'});
        break;
      case 'object_memory':
        context.go(AppRouter.objectMemory, extra: {'from': 'home'});
        break;
      case 'Sequence Memory':
        context.go(AppRouter.sequenceMemory, extra: {'from': 'home'});
        break;
      case 'Visual Memory':
        context.go(AppRouter.visualMemory, extra: {'from': 'home'});
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${game.gameName ?? "This game"} coming soon!'),
            backgroundColor: AppTheme.accentColor,
          ),
        );
        break;
    }
  }
}

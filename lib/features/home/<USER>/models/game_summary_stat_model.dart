class GameSummaryStats {
  final String assignedGames;
  final String playedGames;
  final String avgScore;
  final String rewardPoints;
  final List<RecentActivity> recentActivities;
  final List<dynamic> recommendedExercises;
  final List<Map<String, dynamic>>? badgesReceived;

  GameSummaryStats({
    required this.assignedGames,
    required this.playedGames,
    required this.avgScore,
    required this.rewardPoints,
    required this.recentActivities,
    required this.recommendedExercises,
    this.badgesReceived,
  });

  factory GameSummaryStats.fromApiResponse(Map<String, dynamic> json) {
    final List<dynamic> data = json['data'] ?? [];

    List<RecentActivity> activities = [];
    List<dynamic> recommendedExercises = [];
    List<Map<String, dynamic>>? badgesReceived;

    for (int i = 0; i < data.length; i++) {
      if (data[i] == "recent_activities" && i + 1 < data.length) {
        final activityData = data[i + 1];
        if (activityData is List) {
          activities = activityData
              .map((activity) {
                if (activity is Map<String, dynamic>) {
                  return RecentActivity.fromJson(activity);
                }
                return null;
              })
              .where((activity) => activity != null)
              .cast<RecentActivity>()
              .toList();
        }
      } else if (data[i] == "recommended_exercises" && i + 1 < data.length) {
        final exercisesData = data[i + 1];
        if (exercisesData is List) {
          recommendedExercises = exercisesData;
        }
      } else if (data[i] == "badges_received" && i + 1 < data.length) {
        final badgesData = data[i + 1];
        if (badgesData is List) {
          badgesReceived = badgesData
              .map((badge) {
                if (badge is Map<String, dynamic>) {
                  return badge;
                }
                return null;
              })
              .where((badge) => badge != null)
              .cast<Map<String, dynamic>>()
              .toList();
        }
      }
    }

    return GameSummaryStats(
      assignedGames: data.isNotEmpty && data[0] is Map<String, dynamic>
          ? (data[0]['title'] ?? '0')
          : '0',
      playedGames: data.length > 1 && data[1] is Map<String, dynamic>
          ? (data[1]['title'] ?? '0')
          : '0',
      avgScore: data.length > 2 && data[2] is Map<String, dynamic>
          ? (data[2]['title'] ?? '0')
          : '0',
      rewardPoints: data.length > 3 && data[3] is Map<String, dynamic>
          ? (data[3]['title'] ?? '0')
          : '0',
      recentActivities: activities,
      recommendedExercises: recommendedExercises,
      badgesReceived: badgesReceived,
    );
  }
}

class RecentActivity {
  final String gameId;
  final String gameName;
  final DateTime playedOn;
  final String? thumbnail;
  final String? description;
  final String? sessionStatus;
  final String? levelName;

  RecentActivity({
    required this.gameId,
    required this.gameName,
    required this.playedOn,
    this.thumbnail,
    this.description,
    this.sessionStatus,
    this.levelName,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      gameId: json['id'] ?? '',
      gameName: json['name'] ?? 'Unknown Game',
      playedOn: json['ended_at'] != null
          ? DateTime.tryParse(json['ended_at']) ?? DateTime.now()
          : DateTime.now(),
      thumbnail: json['thumbnail_url'],
      description: json['description'],
      sessionStatus: json['session_status'],
      levelName: json['level_name'],
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(playedOn);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/home/<USER>/models/game_summary_stat_model.dart';
import 'package:recallloop/features/home/<USER>/services/quick_summary_stat_service.dart';

part 'home_bloc_bloc.freezed.dart';
part 'home_bloc_event.dart';
part 'home_bloc_state.dart';

class HomeBlocBloc extends Bloc<HomeBlocEvent, HomeBlocState> {
  HomeBlocBloc() : super(const _Initial()) {
    on<_FetchGameSummaryStats>((event, emit) async {
      try {
        emit(const _Loading());

        final loginModel = await HiveUserService().getCurrentUser();

        GameSummaryStats gameSummaryStats = GameSummaryStats(
          assignedGames: '0',
          playedGames: '0',
          avgScore: '0%',
          rewardPoints: '0',
          recentActivities: <RecentActivity>[],
          recommendedExercises: <dynamic>[],
        );

        if (loginModel != null) {
          final statsResponse =
              await GameSummaryStatsService.getGameSummaryStats(
                tenantCode: loginModel.tenantCode ?? '',
                userId: loginModel.id ?? '',
                accessToken: loginModel.accessToken ?? '',
              );

          if (statsResponse != null) {
            gameSummaryStats = statsResponse;
          }
        }

        emit(_Loaded(gameSummaryStats));
      } catch (e) {
        emit(const _Error('Failed to load game summary stats.'));
      }
    });
  }
}

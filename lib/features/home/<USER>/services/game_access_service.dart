import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

import '../../../../core/database/hive_db_helper.dart';
import '../../../../core/urls/api_config.dart';

class GameAccessService {
  static Future<Map<String, dynamic>?> requestGameAccess({
    required String gameId,
  }) async {
    try {
      debugPrint(
        'GameAccessService: Requesting game access for gameId: $gameId',
      );

      final loginModel = await HiveUserService().getCurrentUser();
      final partyId = loginModel?.id;
      final accessToken = loginModel?.accessToken ?? '';
      final tenantCode = loginModel?.tenantCode ?? '';

      if (partyId == null) {
        debugPrint('GameAccessService: User ID not found');
        return null;
      }

      debugPrint('GameAccessService: partyId: $partyId');
      debugPrint('GameAccessService: tenantCode: $tenantCode');

      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.requestGameAccess}',
      );

      final requestBody = {
        'party_id': partyId,
        'game_id': gameId,
        'tenant_code': tenantCode,
      };

      debugPrint('GameAccessService: Request body: ${jsonEncode(requestBody)}');

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Authorization': 'Bearer $accessToken',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(const Duration(seconds: 15));

      debugPrint('GameAccessService: Response status: ${response.statusCode}');
      debugPrint('GameAccessService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);

        if (decoded is Map<String, dynamic>) {
          if (decoded.containsKey('status') && decoded['status'] == 'success') {
            debugPrint('GameAccessService: Game access requested successfully');
            return decoded;
          } else if (decoded.containsKey('message')) {
            return {
              'status': 'success',
              'message':
                  decoded['message'] ?? 'Game access requested successfully',
              'request_id': decoded['request_id'],
            };
          }
        }

        debugPrint('GameAccessService: Unexpected response format: $decoded');
        return null;
      } else {
        debugPrint(
          'GameAccessService: API failed with status: ${response.statusCode}',
        );
        debugPrint('GameAccessService: Error response: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('GameAccessService: Exception occurred: $e');
      return null;
    }
  }
}

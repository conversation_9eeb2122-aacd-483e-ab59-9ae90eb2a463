import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/services/google_signin_manager.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class GoogleAuthService {
  static const List<String> scopes = ['email', 'profile', 'openid'];

  GoogleSignIn get _googleSignIn {
    if (!GoogleSignInManager.instance.isInitialized) {
      throw StateError(
        'Google Sign-In is not initialized. Please check the initialization logs.',
      );
    }
    return GoogleSignInManager.instance.googleSignIn;
  }

  GoogleAuthService() {
    // Simple constructor - no complex web-specific setup to avoid compilation issues
  }

  Future<void> _ensureInitialized() async {
    if (!GoogleSignInManager.instance.isInitialized) {
      await GoogleSignInManager.instance.initialize();
    }
  }

  Future<AuthResponse?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        // For web, use Supabase's native Google OAuth instead of google_sign_in plugin
        // This avoids the ID token issues completely
        print('Using Supabase native Google OAuth for web...');

        // // Ensure any existing session is cleared so the provider prompt shows
        // await Supabase.instance.client.auth.signOut();

        final response = await Supabase.instance.client.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo: kIsWeb
              ? 'https://devlqc.citrusdev.com/recallloop-web/index.html#/login'
              : null,
          queryParams: {'prompt': 'select_account'},
        );

        if (response) {
          print('Supabase Google OAuth initiated successfully');
          // The authentication will complete via redirect
          // You'll need to handle the redirect in your app
          return null; // Return null as auth completes via redirect
        } else {
          throw 'Failed to initiate Google OAuth with Supabase';
        }
      } else {
        // Mobile-specific sign-in approach using google_sign_in plugin
        await _ensureInitialized();

        // Ensure previous Google account is signed out to force account chooser
        try {
          await _googleSignIn.signOut();
        } catch (_) {}

        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

        if (googleUser == null) {
          print('Google Sign-In was cancelled by user');
          return null;
        }

        print("Google user: ${googleUser.displayName} (${googleUser.email})");

        // Get authentication details
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;

        final accessToken = googleAuth.accessToken;
        final idToken = googleAuth.idToken;

        print('Access token: ${accessToken != null ? "Present" : "NULL"}');
        print('ID token: ${idToken != null ? "Present" : "NULL"}');

        if (idToken == null) {
          throw 'ID token is null on mobile. Please check your Google Services configuration files.';
        }

        if (accessToken == null) {
          throw 'Failed to get access token from Google';
        }

        print('Google Sign-In successful for: ${googleUser.email}');
        _currentUser = googleUser;

        return await _signInToSupabase(accessToken, idToken);
      }
    } catch (error) {
      print('Google Sign-In Error: $error');
      rethrow;
    }
  }

  Future<AuthResponse> _signInToSupabase(
    String? accessToken,
    String idToken,
  ) async {
    // Sign in to Supabase with the Google credentials
    // Access token is required for Google sign-in
    if (accessToken == null) {
      throw 'Access token is required for Google sign-in with Supabase';
    }

    final response = await Supabase.instance.client.auth.signInWithIdToken(
      provider: OAuthProvider.google,
      idToken: idToken,
      accessToken: accessToken,
    );

    return response;
  }

  Future<void> signOut() async {
    try {
      await _ensureInitialized();

      // Sign out from Google
      await _googleSignIn.signOut();

      // Clear manual state
      _currentUser = null;

      // Sign out from Supabase
      await Supabase.instance.client.auth.signOut();
    } catch (error) {
      print('Sign-Out Error: $error');
      rethrow;
    }
  }

  // Manual state management for consistency
  GoogleSignInAccount? _currentUser;
  GoogleSignInAccount? get currentUser => _currentUser;

  Future<bool> isSignedIn() async {
    try {
      await _ensureInitialized();

      // Check if user is signed in using v6.x API
      final isSignedIn = await _googleSignIn.isSignedIn();

      if (isSignedIn) {
        _currentUser = _googleSignIn.currentUser;
      } else {
        _currentUser = null;
      }

      return isSignedIn;
    } catch (error) {
      print('Error checking sign-in status: $error');
      _currentUser = null;
      return false;
    }
  }

  // Helper method to get user info from Google's userinfo API using access token
  Future<Map<String, dynamic>> _getUserInfoFromAccessToken(
    String accessToken,
  ) async {
    final response = await http.get(
      Uri.parse('https://www.googleapis.com/oauth2/v2/userinfo'),
      headers: {
        'Authorization': 'Bearer $accessToken',
        'Accept': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      throw 'Failed to get user info from Google API. Status: ${response.statusCode}';
    }
  }

  // Alternative Supabase authentication method for web when ID token is not available
  Future<AuthResponse> _signInToSupabaseWithAccessToken(
    String accessToken,
    Map<String, dynamic> userInfo,
  ) async {
    try {
      // Try using Supabase's signInWithIdToken with empty idToken but valid accessToken
      // Supabase might be able to validate the access token
      final response = await Supabase.instance.client.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: '', // Empty but required parameter
        accessToken: accessToken,
      );

      if (response.user != null) {
        print('Successfully authenticated with Supabase using access token');
        return response;
      } else {
        throw 'Supabase authentication failed with access token';
      }
    } catch (error) {
      print('Supabase access token authentication failed: $error');

      // Fallback: Try with a minimal fake ID token structure
      // This is a workaround - in production you'd want proper server-side token exchange
      try {
        print('Trying fallback authentication method...');

        // Create a minimal JWT-like structure (this is a workaround)
        final header = base64Url.encode(
          utf8.encode(json.encode({'alg': 'none', 'typ': 'JWT'})),
        );
        final payload = base64Url.encode(
          utf8.encode(
            json.encode({
              'iss': 'https://accounts.google.com',
              'sub': userInfo['id'],
              'email': userInfo['email'],
              'email_verified': true,
              'name': userInfo['name'],
              'picture': userInfo['picture'],
              'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'exp':
                  DateTime.now()
                      .add(Duration(hours: 1))
                      .millisecondsSinceEpoch ~/
                  1000,
            }),
          ),
        );
        final fakeIdToken = '$header.$payload.';

        final fallbackResponse = await Supabase.instance.client.auth
            .signInWithIdToken(
              provider: OAuthProvider.google,
              idToken: fakeIdToken,
              accessToken: accessToken,
            );

        if (fallbackResponse.user != null) {
          print('Fallback authentication successful');
          return fallbackResponse;
        }
      } catch (fallbackError) {
        print('Fallback authentication also failed: $fallbackError');
      }

      throw 'Web authentication failed. Please implement server-side token exchange for production use.';
    }
  }
}

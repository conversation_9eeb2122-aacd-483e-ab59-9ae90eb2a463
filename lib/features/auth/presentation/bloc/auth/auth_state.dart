part of 'auth_bloc.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated({required LoginModel loginUser}) =
      _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message) = _Error;
  const factory AuthState.signUpSuccess({SignupModel? signupUser}) =
      _SignUpSuccess;
  const factory AuthState.signUpError(String message) = _SignUpError;
  const factory AuthState.showUserModal({required LoginModel loginUser}) =
      _ShowUserModal;
}

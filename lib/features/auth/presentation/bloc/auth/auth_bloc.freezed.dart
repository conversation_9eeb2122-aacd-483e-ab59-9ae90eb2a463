// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$AuthEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CheckAuthStatusImplCopyWith<$Res> {
  factory _$$CheckAuthStatusImplCopyWith(
    _$CheckAuthStatusImpl value,
    $Res Function(_$CheckAuthStatusImpl) then,
  ) = __$$CheckAuthStatusImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckAuthStatusImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$CheckAuthStatusImpl>
    implements _$$CheckAuthStatusImplCopyWith<$Res> {
  __$$CheckAuthStatusImplCopyWithImpl(
    _$CheckAuthStatusImpl _value,
    $Res Function(_$CheckAuthStatusImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckAuthStatusImpl
    with DiagnosticableTreeMixin
    implements _CheckAuthStatus {
  const _$CheckAuthStatusImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.checkAuthStatus()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthEvent.checkAuthStatus'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckAuthStatusImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return checkAuthStatus();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return checkAuthStatus?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (checkAuthStatus != null) {
      return checkAuthStatus();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return checkAuthStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return checkAuthStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (checkAuthStatus != null) {
      return checkAuthStatus(this);
    }
    return orElse();
  }
}

abstract class _CheckAuthStatus implements AuthEvent {
  const factory _CheckAuthStatus() = _$CheckAuthStatusImpl;
}

/// @nodoc
abstract class _$$LoginRequestedImplCopyWith<$Res> {
  factory _$$LoginRequestedImplCopyWith(
    _$LoginRequestedImpl value,
    $Res Function(_$LoginRequestedImpl) then,
  ) = __$$LoginRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class __$$LoginRequestedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$LoginRequestedImpl>
    implements _$$LoginRequestedImplCopyWith<$Res> {
  __$$LoginRequestedImplCopyWithImpl(
    _$LoginRequestedImpl _value,
    $Res Function(_$LoginRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? email = null, Object? password = null}) {
    return _then(
      _$LoginRequestedImpl(
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        password: null == password
            ? _value.password
            : password // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$LoginRequestedImpl
    with DiagnosticableTreeMixin
    implements _LoginRequested {
  const _$LoginRequestedImpl({required this.email, required this.password});

  @override
  final String email;
  @override
  final String password;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.loginRequested(email: $email, password: $password)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthEvent.loginRequested'))
      ..add(DiagnosticsProperty('email', email))
      ..add(DiagnosticsProperty('password', password));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginRequestedImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginRequestedImplCopyWith<_$LoginRequestedImpl> get copyWith =>
      __$$LoginRequestedImplCopyWithImpl<_$LoginRequestedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return loginRequested(email, password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return loginRequested?.call(email, password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (loginRequested != null) {
      return loginRequested(email, password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return loginRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return loginRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (loginRequested != null) {
      return loginRequested(this);
    }
    return orElse();
  }
}

abstract class _LoginRequested implements AuthEvent {
  const factory _LoginRequested({
    required final String email,
    required final String password,
  }) = _$LoginRequestedImpl;

  String get email;
  String get password;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginRequestedImplCopyWith<_$LoginRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignUpRequestedImplCopyWith<$Res> {
  factory _$$SignUpRequestedImplCopyWith(
    _$SignUpRequestedImpl value,
    $Res Function(_$SignUpRequestedImpl) then,
  ) = __$$SignUpRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    String email,
    String password,
    String firstName,
    String lastName,
    String phone,
    String address,
    String tenantCode,
  });
}

/// @nodoc
class __$$SignUpRequestedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignUpRequestedImpl>
    implements _$$SignUpRequestedImplCopyWith<$Res> {
  __$$SignUpRequestedImplCopyWithImpl(
    _$SignUpRequestedImpl _value,
    $Res Function(_$SignUpRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? phone = null,
    Object? address = null,
    Object? tenantCode = null,
  }) {
    return _then(
      _$SignUpRequestedImpl(
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        password: null == password
            ? _value.password
            : password // ignore: cast_nullable_to_non_nullable
                  as String,
        firstName: null == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String,
        lastName: null == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        tenantCode: null == tenantCode
            ? _value.tenantCode
            : tenantCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$SignUpRequestedImpl
    with DiagnosticableTreeMixin
    implements _SignUpRequested {
  const _$SignUpRequestedImpl({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.address,
    required this.tenantCode,
  });

  @override
  final String email;
  @override
  final String password;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String phone;
  @override
  final String address;
  @override
  final String tenantCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.signUpRequested(email: $email, password: $password, firstName: $firstName, lastName: $lastName, phone: $phone, address: $address, tenantCode: $tenantCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthEvent.signUpRequested'))
      ..add(DiagnosticsProperty('email', email))
      ..add(DiagnosticsProperty('password', password))
      ..add(DiagnosticsProperty('firstName', firstName))
      ..add(DiagnosticsProperty('lastName', lastName))
      ..add(DiagnosticsProperty('phone', phone))
      ..add(DiagnosticsProperty('address', address))
      ..add(DiagnosticsProperty('tenantCode', tenantCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignUpRequestedImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.tenantCode, tenantCode) ||
                other.tenantCode == tenantCode));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    email,
    password,
    firstName,
    lastName,
    phone,
    address,
    tenantCode,
  );

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignUpRequestedImplCopyWith<_$SignUpRequestedImpl> get copyWith =>
      __$$SignUpRequestedImplCopyWithImpl<_$SignUpRequestedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return signUpRequested(
      email,
      password,
      firstName,
      lastName,
      phone,
      address,
      tenantCode,
    );
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return signUpRequested?.call(
      email,
      password,
      firstName,
      lastName,
      phone,
      address,
      tenantCode,
    );
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (signUpRequested != null) {
      return signUpRequested(
        email,
        password,
        firstName,
        lastName,
        phone,
        address,
        tenantCode,
      );
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return signUpRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return signUpRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (signUpRequested != null) {
      return signUpRequested(this);
    }
    return orElse();
  }
}

abstract class _SignUpRequested implements AuthEvent {
  const factory _SignUpRequested({
    required final String email,
    required final String password,
    required final String firstName,
    required final String lastName,
    required final String phone,
    required final String address,
    required final String tenantCode,
  }) = _$SignUpRequestedImpl;

  String get email;
  String get password;
  String get firstName;
  String get lastName;
  String get phone;
  String get address;
  String get tenantCode;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignUpRequestedImplCopyWith<_$SignUpRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LogoutRequestedImplCopyWith<$Res> {
  factory _$$LogoutRequestedImplCopyWith(
    _$LogoutRequestedImpl value,
    $Res Function(_$LogoutRequestedImpl) then,
  ) = __$$LogoutRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LogoutRequestedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$LogoutRequestedImpl>
    implements _$$LogoutRequestedImplCopyWith<$Res> {
  __$$LogoutRequestedImplCopyWithImpl(
    _$LogoutRequestedImpl _value,
    $Res Function(_$LogoutRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LogoutRequestedImpl
    with DiagnosticableTreeMixin
    implements _LogoutRequested {
  const _$LogoutRequestedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.logoutRequested()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthEvent.logoutRequested'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LogoutRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return logoutRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return logoutRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (logoutRequested != null) {
      return logoutRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return logoutRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return logoutRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (logoutRequested != null) {
      return logoutRequested(this);
    }
    return orElse();
  }
}

abstract class _LogoutRequested implements AuthEvent {
  const factory _LogoutRequested() = _$LogoutRequestedImpl;
}

/// @nodoc
abstract class _$$GoogleSignInRequestedImplCopyWith<$Res> {
  factory _$$GoogleSignInRequestedImplCopyWith(
    _$GoogleSignInRequestedImpl value,
    $Res Function(_$GoogleSignInRequestedImpl) then,
  ) = __$$GoogleSignInRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GoogleSignInRequestedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$GoogleSignInRequestedImpl>
    implements _$$GoogleSignInRequestedImplCopyWith<$Res> {
  __$$GoogleSignInRequestedImplCopyWithImpl(
    _$GoogleSignInRequestedImpl _value,
    $Res Function(_$GoogleSignInRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GoogleSignInRequestedImpl
    with DiagnosticableTreeMixin
    implements _GoogleSignInRequested {
  const _$GoogleSignInRequestedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.googleSignInRequested()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthEvent.googleSignInRequested'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoogleSignInRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return googleSignInRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return googleSignInRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (googleSignInRequested != null) {
      return googleSignInRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return googleSignInRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return googleSignInRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (googleSignInRequested != null) {
      return googleSignInRequested(this);
    }
    return orElse();
  }
}

abstract class _GoogleSignInRequested implements AuthEvent {
  const factory _GoogleSignInRequested() = _$GoogleSignInRequestedImpl;
}

/// @nodoc
abstract class _$$OAuthSignInCompletedImplCopyWith<$Res> {
  factory _$$OAuthSignInCompletedImplCopyWith(
    _$OAuthSignInCompletedImpl value,
    $Res Function(_$OAuthSignInCompletedImpl) then,
  ) = __$$OAuthSignInCompletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Session session});
}

/// @nodoc
class __$$OAuthSignInCompletedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$OAuthSignInCompletedImpl>
    implements _$$OAuthSignInCompletedImplCopyWith<$Res> {
  __$$OAuthSignInCompletedImplCopyWithImpl(
    _$OAuthSignInCompletedImpl _value,
    $Res Function(_$OAuthSignInCompletedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? session = null}) {
    return _then(
      _$OAuthSignInCompletedImpl(
        null == session
            ? _value.session
            : session // ignore: cast_nullable_to_non_nullable
                  as Session,
      ),
    );
  }
}

/// @nodoc

class _$OAuthSignInCompletedImpl
    with DiagnosticableTreeMixin
    implements _OAuthSignInCompleted {
  const _$OAuthSignInCompletedImpl(this.session);

  @override
  final Session session;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.oauthSignInCompleted(session: $session)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthEvent.oauthSignInCompleted'))
      ..add(DiagnosticsProperty('session', session));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OAuthSignInCompletedImpl &&
            (identical(other.session, session) || other.session == session));
  }

  @override
  int get hashCode => Object.hash(runtimeType, session);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OAuthSignInCompletedImplCopyWith<_$OAuthSignInCompletedImpl>
  get copyWith =>
      __$$OAuthSignInCompletedImplCopyWithImpl<_$OAuthSignInCompletedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return oauthSignInCompleted(session);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return oauthSignInCompleted?.call(session);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (oauthSignInCompleted != null) {
      return oauthSignInCompleted(session);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return oauthSignInCompleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return oauthSignInCompleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (oauthSignInCompleted != null) {
      return oauthSignInCompleted(this);
    }
    return orElse();
  }
}

abstract class _OAuthSignInCompleted implements AuthEvent {
  const factory _OAuthSignInCompleted(final Session session) =
      _$OAuthSignInCompletedImpl;

  Session get session;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OAuthSignInCompletedImplCopyWith<_$OAuthSignInCompletedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateUserMetadataImplCopyWith<$Res> {
  factory _$$UpdateUserMetadataImplCopyWith(
    _$UpdateUserMetadataImpl value,
    $Res Function(_$UpdateUserMetadataImpl) then,
  ) = __$$UpdateUserMetadataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    String firstName,
    String lastName,
    String phone,
    String tenantCode,
  });
}

/// @nodoc
class __$$UpdateUserMetadataImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$UpdateUserMetadataImpl>
    implements _$$UpdateUserMetadataImplCopyWith<$Res> {
  __$$UpdateUserMetadataImplCopyWithImpl(
    _$UpdateUserMetadataImpl _value,
    $Res Function(_$UpdateUserMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? phone = null,
    Object? tenantCode = null,
  }) {
    return _then(
      _$UpdateUserMetadataImpl(
        firstName: null == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String,
        lastName: null == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        tenantCode: null == tenantCode
            ? _value.tenantCode
            : tenantCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$UpdateUserMetadataImpl
    with DiagnosticableTreeMixin
    implements _UpdateUserMetadata {
  const _$UpdateUserMetadataImpl({
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.tenantCode,
  });

  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String phone;
  @override
  final String tenantCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthEvent.updateUserMetadata(firstName: $firstName, lastName: $lastName, phone: $phone, tenantCode: $tenantCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthEvent.updateUserMetadata'))
      ..add(DiagnosticsProperty('firstName', firstName))
      ..add(DiagnosticsProperty('lastName', lastName))
      ..add(DiagnosticsProperty('phone', phone))
      ..add(DiagnosticsProperty('tenantCode', tenantCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateUserMetadataImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.tenantCode, tenantCode) ||
                other.tenantCode == tenantCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, firstName, lastName, phone, tenantCode);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateUserMetadataImplCopyWith<_$UpdateUserMetadataImpl> get copyWith =>
      __$$UpdateUserMetadataImplCopyWithImpl<_$UpdateUserMetadataImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(String email, String password) loginRequested,
    required TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )
    signUpRequested,
    required TResult Function() logoutRequested,
    required TResult Function() googleSignInRequested,
    required TResult Function(Session session) oauthSignInCompleted,
    required TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )
    updateUserMetadata,
  }) {
    return updateUserMetadata(firstName, lastName, phone, tenantCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(String email, String password)? loginRequested,
    TResult? Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult? Function()? logoutRequested,
    TResult? Function()? googleSignInRequested,
    TResult? Function(Session session)? oauthSignInCompleted,
    TResult? Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
  }) {
    return updateUserMetadata?.call(firstName, lastName, phone, tenantCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(String email, String password)? loginRequested,
    TResult Function(
      String email,
      String password,
      String firstName,
      String lastName,
      String phone,
      String address,
      String tenantCode,
    )?
    signUpRequested,
    TResult Function()? logoutRequested,
    TResult Function()? googleSignInRequested,
    TResult Function(Session session)? oauthSignInCompleted,
    TResult Function(
      String firstName,
      String lastName,
      String phone,
      String tenantCode,
    )?
    updateUserMetadata,
    required TResult orElse(),
  }) {
    if (updateUserMetadata != null) {
      return updateUserMetadata(firstName, lastName, phone, tenantCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckAuthStatus value) checkAuthStatus,
    required TResult Function(_LoginRequested value) loginRequested,
    required TResult Function(_SignUpRequested value) signUpRequested,
    required TResult Function(_LogoutRequested value) logoutRequested,
    required TResult Function(_GoogleSignInRequested value)
    googleSignInRequested,
    required TResult Function(_OAuthSignInCompleted value) oauthSignInCompleted,
    required TResult Function(_UpdateUserMetadata value) updateUserMetadata,
  }) {
    return updateUserMetadata(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(_LoginRequested value)? loginRequested,
    TResult? Function(_SignUpRequested value)? signUpRequested,
    TResult? Function(_LogoutRequested value)? logoutRequested,
    TResult? Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult? Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult? Function(_UpdateUserMetadata value)? updateUserMetadata,
  }) {
    return updateUserMetadata?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckAuthStatus value)? checkAuthStatus,
    TResult Function(_LoginRequested value)? loginRequested,
    TResult Function(_SignUpRequested value)? signUpRequested,
    TResult Function(_LogoutRequested value)? logoutRequested,
    TResult Function(_GoogleSignInRequested value)? googleSignInRequested,
    TResult Function(_OAuthSignInCompleted value)? oauthSignInCompleted,
    TResult Function(_UpdateUserMetadata value)? updateUserMetadata,
    required TResult orElse(),
  }) {
    if (updateUserMetadata != null) {
      return updateUserMetadata(this);
    }
    return orElse();
  }
}

abstract class _UpdateUserMetadata implements AuthEvent {
  const factory _UpdateUserMetadata({
    required final String firstName,
    required final String lastName,
    required final String phone,
    required final String tenantCode,
  }) = _$UpdateUserMetadataImpl;

  String get firstName;
  String get lastName;
  String get phone;
  String get tenantCode;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateUserMetadataImplCopyWith<_$UpdateUserMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AuthState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl with DiagnosticableTreeMixin implements _Initial {
  const _$InitialImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.initial()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthState.initial'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AuthState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl with DiagnosticableTreeMixin implements _Loading {
  const _$LoadingImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.loading()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthState.loading'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements AuthState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$AuthenticatedImplCopyWith<$Res> {
  factory _$$AuthenticatedImplCopyWith(
    _$AuthenticatedImpl value,
    $Res Function(_$AuthenticatedImpl) then,
  ) = __$$AuthenticatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LoginModel loginUser});

  $LoginModelCopyWith<$Res> get loginUser;
}

/// @nodoc
class __$$AuthenticatedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthenticatedImpl>
    implements _$$AuthenticatedImplCopyWith<$Res> {
  __$$AuthenticatedImplCopyWithImpl(
    _$AuthenticatedImpl _value,
    $Res Function(_$AuthenticatedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? loginUser = null}) {
    return _then(
      _$AuthenticatedImpl(
        loginUser: null == loginUser
            ? _value.loginUser
            : loginUser // ignore: cast_nullable_to_non_nullable
                  as LoginModel,
      ),
    );
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoginModelCopyWith<$Res> get loginUser {
    return $LoginModelCopyWith<$Res>(_value.loginUser, (value) {
      return _then(_value.copyWith(loginUser: value));
    });
  }
}

/// @nodoc

class _$AuthenticatedImpl
    with DiagnosticableTreeMixin
    implements _Authenticated {
  const _$AuthenticatedImpl({required this.loginUser});

  @override
  final LoginModel loginUser;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.authenticated(loginUser: $loginUser)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.authenticated'))
      ..add(DiagnosticsProperty('loginUser', loginUser));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticatedImpl &&
            (identical(other.loginUser, loginUser) ||
                other.loginUser == loginUser));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loginUser);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticatedImplCopyWith<_$AuthenticatedImpl> get copyWith =>
      __$$AuthenticatedImplCopyWithImpl<_$AuthenticatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return authenticated(loginUser);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return authenticated?.call(loginUser);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (authenticated != null) {
      return authenticated(loginUser);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return authenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return authenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (authenticated != null) {
      return authenticated(this);
    }
    return orElse();
  }
}

abstract class _Authenticated implements AuthState {
  const factory _Authenticated({required final LoginModel loginUser}) =
      _$AuthenticatedImpl;

  LoginModel get loginUser;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticatedImplCopyWith<_$AuthenticatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnauthenticatedImplCopyWith<$Res> {
  factory _$$UnauthenticatedImplCopyWith(
    _$UnauthenticatedImpl value,
    $Res Function(_$UnauthenticatedImpl) then,
  ) = __$$UnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnauthenticatedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$UnauthenticatedImpl>
    implements _$$UnauthenticatedImplCopyWith<$Res> {
  __$$UnauthenticatedImplCopyWithImpl(
    _$UnauthenticatedImpl _value,
    $Res Function(_$UnauthenticatedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnauthenticatedImpl
    with DiagnosticableTreeMixin
    implements _Unauthenticated {
  const _$UnauthenticatedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.unauthenticated()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthState.unauthenticated'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class _Unauthenticated implements AuthState {
  const factory _Unauthenticated() = _$UnauthenticatedImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ErrorImpl with DiagnosticableTreeMixin implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.error(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.error'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements AuthState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignUpSuccessImplCopyWith<$Res> {
  factory _$$SignUpSuccessImplCopyWith(
    _$SignUpSuccessImpl value,
    $Res Function(_$SignUpSuccessImpl) then,
  ) = __$$SignUpSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({signup.SignupModel? signupUser});

  $SignupModelCopyWith<$Res>? get signupUser;
}

/// @nodoc
class __$$SignUpSuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$SignUpSuccessImpl>
    implements _$$SignUpSuccessImplCopyWith<$Res> {
  __$$SignUpSuccessImplCopyWithImpl(
    _$SignUpSuccessImpl _value,
    $Res Function(_$SignUpSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? signupUser = freezed}) {
    return _then(
      _$SignUpSuccessImpl(
        signupUser: freezed == signupUser
            ? _value.signupUser
            : signupUser // ignore: cast_nullable_to_non_nullable
                  as signup.SignupModel?,
      ),
    );
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SignupModelCopyWith<$Res>? get signupUser {
    if (_value.signupUser == null) {
      return null;
    }

    return $SignupModelCopyWith<$Res>(_value.signupUser!, (value) {
      return _then(_value.copyWith(signupUser: value));
    });
  }
}

/// @nodoc

class _$SignUpSuccessImpl
    with DiagnosticableTreeMixin
    implements _SignUpSuccess {
  const _$SignUpSuccessImpl({this.signupUser});

  @override
  final signup.SignupModel? signupUser;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.signUpSuccess(signupUser: $signupUser)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.signUpSuccess'))
      ..add(DiagnosticsProperty('signupUser', signupUser));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignUpSuccessImpl &&
            (identical(other.signupUser, signupUser) ||
                other.signupUser == signupUser));
  }

  @override
  int get hashCode => Object.hash(runtimeType, signupUser);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignUpSuccessImplCopyWith<_$SignUpSuccessImpl> get copyWith =>
      __$$SignUpSuccessImplCopyWithImpl<_$SignUpSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return signUpSuccess(signupUser);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return signUpSuccess?.call(signupUser);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (signUpSuccess != null) {
      return signUpSuccess(signupUser);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return signUpSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return signUpSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (signUpSuccess != null) {
      return signUpSuccess(this);
    }
    return orElse();
  }
}

abstract class _SignUpSuccess implements AuthState {
  const factory _SignUpSuccess({final signup.SignupModel? signupUser}) =
      _$SignUpSuccessImpl;

  signup.SignupModel? get signupUser;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignUpSuccessImplCopyWith<_$SignUpSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignUpErrorImplCopyWith<$Res> {
  factory _$$SignUpErrorImplCopyWith(
    _$SignUpErrorImpl value,
    $Res Function(_$SignUpErrorImpl) then,
  ) = __$$SignUpErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$SignUpErrorImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$SignUpErrorImpl>
    implements _$$SignUpErrorImplCopyWith<$Res> {
  __$$SignUpErrorImplCopyWithImpl(
    _$SignUpErrorImpl _value,
    $Res Function(_$SignUpErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$SignUpErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$SignUpErrorImpl with DiagnosticableTreeMixin implements _SignUpError {
  const _$SignUpErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.signUpError(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.signUpError'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignUpErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignUpErrorImplCopyWith<_$SignUpErrorImpl> get copyWith =>
      __$$SignUpErrorImplCopyWithImpl<_$SignUpErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return signUpError(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return signUpError?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (signUpError != null) {
      return signUpError(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return signUpError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return signUpError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (signUpError != null) {
      return signUpError(this);
    }
    return orElse();
  }
}

abstract class _SignUpError implements AuthState {
  const factory _SignUpError(final String message) = _$SignUpErrorImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignUpErrorImplCopyWith<_$SignUpErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ShowUserModalImplCopyWith<$Res> {
  factory _$$ShowUserModalImplCopyWith(
    _$ShowUserModalImpl value,
    $Res Function(_$ShowUserModalImpl) then,
  ) = __$$ShowUserModalImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LoginModel loginUser});

  $LoginModelCopyWith<$Res> get loginUser;
}

/// @nodoc
class __$$ShowUserModalImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$ShowUserModalImpl>
    implements _$$ShowUserModalImplCopyWith<$Res> {
  __$$ShowUserModalImplCopyWithImpl(
    _$ShowUserModalImpl _value,
    $Res Function(_$ShowUserModalImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? loginUser = null}) {
    return _then(
      _$ShowUserModalImpl(
        loginUser: null == loginUser
            ? _value.loginUser
            : loginUser // ignore: cast_nullable_to_non_nullable
                  as LoginModel,
      ),
    );
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoginModelCopyWith<$Res> get loginUser {
    return $LoginModelCopyWith<$Res>(_value.loginUser, (value) {
      return _then(_value.copyWith(loginUser: value));
    });
  }
}

/// @nodoc

class _$ShowUserModalImpl
    with DiagnosticableTreeMixin
    implements _ShowUserModal {
  const _$ShowUserModalImpl({required this.loginUser});

  @override
  final LoginModel loginUser;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.showUserModal(loginUser: $loginUser)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.showUserModal'))
      ..add(DiagnosticsProperty('loginUser', loginUser));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShowUserModalImpl &&
            (identical(other.loginUser, loginUser) ||
                other.loginUser == loginUser));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loginUser);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShowUserModalImplCopyWith<_$ShowUserModalImpl> get copyWith =>
      __$$ShowUserModalImplCopyWithImpl<_$ShowUserModalImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(LoginModel loginUser) authenticated,
    required TResult Function() unauthenticated,
    required TResult Function(String message) error,
    required TResult Function(signup.SignupModel? signupUser) signUpSuccess,
    required TResult Function(String message) signUpError,
    required TResult Function(LoginModel loginUser) showUserModal,
  }) {
    return showUserModal(loginUser);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(LoginModel loginUser)? authenticated,
    TResult? Function()? unauthenticated,
    TResult? Function(String message)? error,
    TResult? Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult? Function(String message)? signUpError,
    TResult? Function(LoginModel loginUser)? showUserModal,
  }) {
    return showUserModal?.call(loginUser);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(LoginModel loginUser)? authenticated,
    TResult Function()? unauthenticated,
    TResult Function(String message)? error,
    TResult Function(signup.SignupModel? signupUser)? signUpSuccess,
    TResult Function(String message)? signUpError,
    TResult Function(LoginModel loginUser)? showUserModal,
    required TResult orElse(),
  }) {
    if (showUserModal != null) {
      return showUserModal(loginUser);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Authenticated value) authenticated,
    required TResult Function(_Unauthenticated value) unauthenticated,
    required TResult Function(_Error value) error,
    required TResult Function(_SignUpSuccess value) signUpSuccess,
    required TResult Function(_SignUpError value) signUpError,
    required TResult Function(_ShowUserModal value) showUserModal,
  }) {
    return showUserModal(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Authenticated value)? authenticated,
    TResult? Function(_Unauthenticated value)? unauthenticated,
    TResult? Function(_Error value)? error,
    TResult? Function(_SignUpSuccess value)? signUpSuccess,
    TResult? Function(_SignUpError value)? signUpError,
    TResult? Function(_ShowUserModal value)? showUserModal,
  }) {
    return showUserModal?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Authenticated value)? authenticated,
    TResult Function(_Unauthenticated value)? unauthenticated,
    TResult Function(_Error value)? error,
    TResult Function(_SignUpSuccess value)? signUpSuccess,
    TResult Function(_SignUpError value)? signUpError,
    TResult Function(_ShowUserModal value)? showUserModal,
    required TResult orElse(),
  }) {
    if (showUserModal != null) {
      return showUserModal(this);
    }
    return orElse();
  }
}

abstract class _ShowUserModal implements AuthState {
  const factory _ShowUserModal({required final LoginModel loginUser}) =
      _$ShowUserModalImpl;

  LoginModel get loginUser;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShowUserModalImplCopyWith<_$ShowUserModalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

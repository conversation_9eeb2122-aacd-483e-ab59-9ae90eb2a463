import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart'
    as signup;
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart'
    hide User;
import 'package:recallloop/features/auth/data/services/google_auth_service.dart';
import 'package:recallloop/features/auth/data/services/login_service.dart';
import 'package:recallloop/features/auth/data/services/party_info_service.dart';
import 'package:recallloop/features/auth/data/services/user_info_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final HiveUserService _hiveUserService = HiveUserService();

  AuthBloc() : super(const _Initial()) {
    // Listen for Supabase auth state changes (for web OAuth)
    if (kIsWeb) {
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        final AuthChangeEvent event = data.event;
        final Session? session = data.session;

        print('event: $event, session: $session');

        if (event == AuthChangeEvent.signedIn && session != null) {
          print('OAuth authentication detected, processing user data...');
          // Trigger the OAuth success handling
          add(AuthEvent.oauthSignInCompleted(session));
        }
      });
    }

    on<AuthEvent>((event, emit) async {
      try {
        if (event is _LoginRequested) {
          emit(const _Loading());

          final loginService = LoginService();
          final loginModel = await loginService.login(
            event.email,
            event.password,
          );

          if (loginModel != null) {
            // Get additional user info after successful login
            final userInfo = await UserInfoService.getUserInfo(
              userId: loginModel.user?.id ?? '',
              accessToken: loginModel.accessToken ?? '',
            );

            if (userInfo != null) {
              // Update the login model with additional user info from fn_get_user_info
              final updatedLoginModel = loginModel.copyWith(
                user: loginModel.user?.copyWith(
                  userMetadata: loginModel.user?.userMetadata?.copyWith(
                    tenantCode: userInfo['tenant_code'],
                    tenantId:
                        userInfo['tenant_id'], // Use parent_party_id as tenantId
                    partyTypeKey: userInfo['party_type_key'],
                    partyId: userInfo['id'], // Use id as partyId
                  ),
                  updatedAt: DateTime.now().toIso8601String(),
                  isAnonymous: loginModel.user?.isAnonymous ?? false,
                ),
              );

              // Save updated user data to local database
              await _hiveUserService.saveUserAfterLogin(updatedLoginModel);

              // Store tenant code in SharedPreferences for future login prefill
              await _storeTenantCode(userInfo['tenant_code']);

              emit(_Authenticated(loginUser: updatedLoginModel));
            } else {
              // Save original user data if user info call fails
              await _hiveUserService.saveUserAfterLogin(loginModel);

              // Store tenant code if available in login model
              final tenantCode = loginModel.user?.userMetadata?.tenantCode;
              if (tenantCode != null && tenantCode.isNotEmpty) {
                await _storeTenantCode(tenantCode);
              }

              emit(_Authenticated(loginUser: loginModel));
            }
          } else {
            emit(_Error('Invalid email or password'));
          }
        }

        if (event is _SignUpRequested) {
          emit(const _Loading());

          final email = event.email;
          final password = event.password;
          final firstName = event.firstName;
          final lastName = event.lastName;
          final phone = event.phone;
          final address = event.address;
          final tenantCode = event.tenantCode ?? 'TN-0001';

          final signUpResponse = await Supabase.instance.client.auth.signUp(
            email: email,
            password: password,
          );

          if (signUpResponse.user == null) {
            emit(_SignUpError('Sign up failed. Please try again.'));
            return;
          }

          // Get the unique user ID from the response
          final userId = signUpResponse.user!.id;

          // Generate username from first and last name
          final username = '${firstName.toLowerCase()}${lastName.toLowerCase()}'
              .replaceAll(' ', '');

          // Update party info in Supabase
          final partyInfoUpdateSuccess = await PartyInfoService.updatePartyInfo(
            firstName: firstName,
            lastName: lastName,
            email: email,
            address: address,
            phone: phone,
            username: username,
            tenantCode: tenantCode,
            rolename: 'PLAYER',
            avatarUrl: '',
            partyTypeKey: 'PLAYER',
            partyId: "",
            userId: userId,
            accessToken: signUpResponse.session?.accessToken ?? '',
          );

          if (partyInfoUpdateSuccess) {
            // Store tenant code for future logins
            await _storeTenantCode(tenantCode);

            emit(
              _SignUpSuccess(
                signupUser: signup.SignupModel(
                  accessToken: signUpResponse.session?.accessToken ?? '',
                  user: signup.User(
                    id: userId,
                    email: email,
                    userMetadata: signup.Data(
                      firstName: firstName,
                      lastName: lastName,
                      phone: phone,
                      address: address,
                      tenantCode: tenantCode,
                    ),
                  ),
                ),
              ),
            );
          } else {
            emit(
              _SignUpError(
                'Sign up completed but failed to update party info. Please contact support.',
              ),
            );
          }
        }

        if (event is _LogoutRequested) {
          emit(const _Loading());

          // Clear user data from local database (this also clears user info)
          await _hiveUserService.logout();

          // Note: We keep tenant_code in SharedPreferences for future login prefill
          // Only clear it if explicitly requested by user or during app uninstall

          emit(const _Unauthenticated());
        }

        if (event is _CheckAuthStatus) {
          emit(const _Loading());

          // Check if user is logged in with valid token
          final isLoggedIn = await _hiveUserService.isLoggedIn();
          if (isLoggedIn) {
            final currentUser = await _hiveUserService.getCurrentUser();
            if (currentUser != null) {
              // User is authenticated with valid token
              // For now, emit unauthenticated since we don't have a proper conversion
              // In a real implementation, you'd convert LocalUserEntity to LoginModel
              emit(const _Unauthenticated());
              // TODO: Create proper authenticated state from local user data
            } else {
              emit(const _Unauthenticated());
            }
          } else {
            emit(const _Unauthenticated());
          }
        }

        if (event is _GoogleSignInRequested) {
          print("onGoogleSigninCompleted event fired");
          emit(const _Loading());
          final googleAuthService = GoogleAuthService();
          final supabaseResponse = await googleAuthService.signInWithGoogle();

          if (supabaseResponse == null) {
            return;
          }

          if (supabaseResponse.user != null) {
            final result = await _handleGoogleSignInSuccess(supabaseResponse);
            emit(result);
          } else {
            emit(_Error('Supabase authentication failed'));
          }
        }

        if (event is _UpdateUserMetadata) {
          emit(const _Loading());

          try {
            final currentUser = await _hiveUserService.getCurrentUser();
            if (currentUser == null) {
              emit(_Error('No authenticated user found'));
              return;
            }

            // Update user metadata in Supabase
            final metadataSuccess = await UserInfoService.updateUserMetadata(
              userId: currentUser.id,
              accessToken: currentUser.accessToken,
              firstName: event.firstName,
              lastName: event.lastName,
              phoneNumber: event.phone,
              tenantCode: event.tenantCode,
            );

            // Also update party info to ensure consistency across tables
            final partyInfoSuccess = await PartyInfoService.updatePartyInfo(
              firstName: event.firstName,
              lastName: event.lastName,
              email: currentUser.email,
              address: '', // Keep existing or empty
              phone: event.phone,
              username:
                  '${event.firstName.toLowerCase()}${event.lastName.toLowerCase()}'
                      .replaceAll(' ', ''),
              tenantCode: event.tenantCode,
              rolename: 'PLAYER',
              avatarUrl: currentUser.avatarUrl,
              partyTypeKey: 'PLAYER',
              partyId: currentUser.partyId ?? '',
              userId: currentUser.id,
              accessToken: currentUser.accessToken,
            );

            if (metadataSuccess && partyInfoSuccess) {
              final updatedUser = currentUser.copyWith(
                firstName: event.firstName,
                lastName: event.lastName,
                phone: event.phone,
                tenantCode: event.tenantCode,
              );

              await _hiveUserService.updateUser(updatedUser);

              final loginModel = LoginModel(
                accessToken: currentUser.accessToken,
                user: User(
                  id: currentUser.id,
                  userMetadata: UserMetadata(
                    firstName: event.firstName,
                    lastName: event.lastName,
                    phone: event.phone,
                    tenantCode: event.tenantCode,
                    email: currentUser.email,
                    avatarUrl: currentUser.avatarUrl,
                    partyTypeKey: currentUser.partyTypeKey,
                    partyId: currentUser.partyId,
                  ),
                ),
              );

              // Mark user setup as completed
              await _markUserSetupCompleted(currentUser.email);

              // Store updated tenant code
              await _storeTenantCode(event.tenantCode);

              emit(_Authenticated(loginUser: loginModel));
            } else {
              emit(_Error('Failed to update user profile. Please try again.'));
            }
          } catch (e) {
            emit(_Error('Error updating profile: ${e.toString()}'));
          }
        }

        if (event is _OAuthSignInCompleted) {
          print("onAuthCompleted event fired");
          emit(const _Loading());

          try {
            final result = await _handleOAuthSignInSuccess(event.session);
            emit(result);
          } catch (e) {
            print('Error processing OAuth sign-in: $e');
            emit(_Error('Failed to process OAuth authentication: $e'));
          }
        }
      } catch (e) {
        emit(_Error('An unexpected error occurred: ${e.toString()}'));
      }
    });
  }

  // Helper method to store tenant code in SharedPreferences
  Future<void> _storeTenantCode(String? tenantCode) async {
    if (tenantCode != null && tenantCode.isNotEmpty) {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('tenant_code', tenantCode);
        print('Tenant code stored: $tenantCode');
      } catch (e) {
        print('Error storing tenant code: $e');
      }
    }
  }

  // Helper method to handle Google Sign-In success (mobile)
  Future<AuthState> _handleGoogleSignInSuccess(dynamic supabaseResponse) async {
    final user = supabaseResponse.user!;
    final session = supabaseResponse.session!;

    // Extract user information from Google metadata
    final displayName =
        user.userMetadata?['full_name'] ?? user.userMetadata?['name'] ?? '';
    final nameParts = displayName.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';
    final email = user.email ?? '';
    final avatarUrl = user.userMetadata?['avatar_url'] ?? '';

    // Create initial login model
    final loginModel = await _createLoginModelFromUser(
      user: user,
      session: session,
      firstName: firstName,
      lastName: lastName,
      email: email,
      avatarUrl: avatarUrl,
    );

    // Check if user already exists and has complete profile
    final existingUserInfo = await UserInfoService.getUserInfo(
      userId: user.id,
      accessToken: session.accessToken,
    );

    // Only update party info for new users or users without complete profile
    if (existingUserInfo == null ||
        existingUserInfo['tenant_code'] == null ||
        existingUserInfo['tenant_code'] == 'TN-0001') {
      // Update party info in Supabase for new users only
      final partyInfoUpdateSuccess = await PartyInfoService.updatePartyInfo(
        firstName: firstName,
        lastName: lastName,
        email: email,
        address: '',
        phone: '',
        username: '${firstName.toLowerCase()}${lastName.toLowerCase()}'
            .replaceAll(' ', ''),
        tenantCode: 'TN-0001',
        rolename: 'PLAYER',
        avatarUrl: avatarUrl,
        partyTypeKey: 'PLAYER',
        partyId: "",
        userId: user.id,
        accessToken: session.accessToken,
      );

      if (!partyInfoUpdateSuccess) {
        return _Error(
          'Google sign-in completed but failed to update party info. Please contact support.',
        );
      }
    }

    // Get additional user info and determine if modal should be shown
    return await _processUserInfoAndGetState(loginModel);
  }

  // Helper method to handle OAuth Sign-In success (web)
  Future<AuthState> _handleOAuthSignInSuccess(Session session) async {
    final user = session.user;

    // Extract user information from OAuth metadata
    final firstName = user.userMetadata?['given_name'] ?? '';
    final lastName = user.userMetadata?['family_name'] ?? '';
    final email = user.email ?? '';
    final avatarUrl = user.userMetadata?['avatar_url'] ?? '';

    // Create initial login model
    final loginModel = await _createLoginModelFromUser(
      user: user,
      session: session,
      firstName: firstName,
      lastName: lastName,
      email: email,
      avatarUrl: avatarUrl,
    );

    // Check if user already exists and has complete profile
    final existingUserInfo = await UserInfoService.getUserInfo(
      userId: user.id,
      accessToken: session.accessToken,
    );

    // Only update party info for new users or users without complete profile
    if (session.user.appMetadata['provider'] == 'google' &&
        (existingUserInfo == null ||
            existingUserInfo['tenant_code'] == null ||
            existingUserInfo['tenant_code'] == 'TN-0001')) {
      // Update party info in Supabase for new users only
      final partyInfoUpdateSuccess = await PartyInfoService.updatePartyInfo(
        firstName: user.userMetadata?['name'] ?? firstName,
        lastName: lastName,
        email: email,
        address: '',
        phone: '',
        username: email,
        tenantCode: 'TN-0001',
        rolename: 'PLAYER',
        avatarUrl: avatarUrl,
        partyTypeKey: 'PLAYER',
        partyId: "",
        userId: user.id,
        accessToken: session.accessToken,
      );

      if (!partyInfoUpdateSuccess) {
        return _Error(
          'OAuth sign-in completed but failed to update party info. Please contact support.',
        );
      }
    }

    // Get additional user info and determine if modal should be shown
    return await _processUserInfoAndGetState(loginModel);
  }

  // Helper method to create LoginModel from user and session data
  Future<LoginModel> _createLoginModelFromUser({
    required dynamic user,
    required dynamic session,
    required String firstName,
    required String lastName,
    required String email,
    required String avatarUrl,
  }) async {
    return LoginModel(
      accessToken: session.accessToken,
      tokenType: session.tokenType ?? 'Bearer',
      expiresIn: session.expiresIn ?? 3600,
      expiresAt:
          session.expiresAt ?? DateTime.now().millisecondsSinceEpoch ~/ 1000,
      refreshToken: session.refreshToken,
      user: User(
        id: user.id,
        aud: user.aud,
        role: user.role,
        email: email,
        emailConfirmedAt: user.emailConfirmedAt,
        phone: user.phone,
        confirmedAt: user.confirmedAt,
        lastSignInAt: user.lastSignInAt,
        appMetadata: null,
        userMetadata: UserMetadata(
          avatarUrl: avatarUrl,
          email: email,
          emailVerified: user.userMetadata?['email_verified'] ?? true,
          firstName: firstName,
          lastName: lastName,
          phone: user.phone,
          phoneVerified: user.userMetadata?['phone_verified'] ?? false,
          sub: user.userMetadata?['sub'] ?? user.id,
          tenantCode: 'TN-0001', // Default tenant code
          tenantId: '', // Will be updated after user info call
          partyTypeKey: 'PLAYER', // Default party type
          partyId: '', // Will be updated after user info call
          address: '', // Default empty address
        ),
        createdAt: user.createdAt ?? DateTime.now().toIso8601String(),
        updatedAt: user.updatedAt ?? DateTime.now().toIso8601String(),
        isAnonymous: false,
      ),
    );
  }

  // Helper method to process user info and return appropriate state
  Future<AuthState> _processUserInfoAndGetState(LoginModel loginModel) async {
    try {
      // Get additional user info from Supabase
      final userInfo = await UserInfoService.getUserInfo(
        userId: loginModel.user?.id ?? '',
        accessToken: loginModel.accessToken,
      );

      print("userInfo: $userInfo");

      if (userInfo != null) {
        // Update the login model with additional user info
        final updatedLoginModel = loginModel.copyWith(
          user: loginModel.user?.copyWith(
            userMetadata: loginModel.user?.userMetadata?.copyWith(
              tenantCode: userInfo['tenant_code'],
              tenantId: userInfo['tenant_id'],
              partyTypeKey: userInfo['party_type_key'],
              partyId: userInfo['id'],
            ),
            updatedAt: DateTime.now().toIso8601String(),
            isAnonymous: false,
          ),
        );

        // Save updated user data to local DB
        await _hiveUserService.saveUserAfterLogin(updatedLoginModel);

        // Store tenant code for future login prefill
        await _storeTenantCode(userInfo['tenant_code']);

        // Check if user should see modal or go directly to authenticated state
        if (await _shouldShowUserModal(updatedLoginModel)) {
          return _ShowUserModal(loginUser: updatedLoginModel);
        } else {
          return _Authenticated(loginUser: updatedLoginModel);
        }
      } else {
        // Save original user data if user info call fails
        await _hiveUserService.saveUserAfterLogin(loginModel);

        // Always show modal for new users without complete profile
        return _ShowUserModal(loginUser: loginModel);
      }
    } catch (e) {
      print('Error processing user info: $e');
      // Save original user data and show modal on error
      await _hiveUserService.saveUserAfterLogin(loginModel);
      return _ShowUserModal(loginUser: loginModel);
    }
  }

  // Helper method to determine if user modal should be shown
  Future<bool> _shouldShowUserModal(LoginModel loginModel) async {
    try {
      // First check if user logged in via Google/Gmail
      final isGoogleLogin = _isGoogleLogin(loginModel);

      // Only show modal for Google login users
      if (!isGoogleLogin) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final email =
          loginModel.user?.userMetadata?.email ?? loginModel.user?.email ?? '';

      // Check if this user has completed setup before
      final completedKey = 'user_setup_completed_$email';
      final hasCompletedSetup = prefs.getBool(completedKey) ?? false;

      // Check if user has complete profile data
      final userMetadata = loginModel.user?.userMetadata;
      final hasCompleteProfile =
          userMetadata?.tenantCode != null &&
          userMetadata!.tenantCode!.isNotEmpty &&
          userMetadata.tenantCode != 'TN-0001' &&
          userMetadata.firstName != null &&
          userMetadata.firstName!.isNotEmpty;

      // Show modal if user hasn't completed setup AND doesn't have complete profile
      return !hasCompletedSetup || !hasCompleteProfile;
    } catch (e) {
      print('Error checking user modal status: $e');
      // Default to not showing modal on error for non-Google users
      return _isGoogleLogin(loginModel);
    }
  }

  // Helper method to check if user logged in via Google
  bool _isGoogleLogin(LoginModel loginModel) {
    try {
      // Check app metadata provider
      final appProvider = loginModel.user?.appMetadata?.provider;
      if (appProvider == 'google') {
        return true;
      }

      // Check app metadata providers list
      final appProviders = loginModel.user?.appMetadata?.providers;
      if (appProviders != null && appProviders.contains('google')) {
        return true;
      }

      // Check identities for Google provider
      final identities = loginModel.user?.identities;
      if (identities != null) {
        for (final identity in identities) {
          if (identity.provider == 'google') {
            return true;
          }
        }
      }

      // Check current Supabase user for provider info (fallback)
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null) {
        final appMetadata = currentUser.appMetadata;
        final provider = appMetadata['provider'];
        final providers = appMetadata['providers'];

        if (provider == 'google' ||
            (providers is List && providers.contains('google'))) {
          return true;
        }
      }

      return false;
    } catch (e) {
      print('Error checking Google login status: $e');
      return false;
    }
  }

  // Helper method to mark user setup as completed
  Future<void> _markUserSetupCompleted(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final completedKey = 'user_setup_completed_$email';
      await prefs.setBool(completedKey, true);
    } catch (e) {
      print('Error marking user setup as completed: $e');
    }
  }
}

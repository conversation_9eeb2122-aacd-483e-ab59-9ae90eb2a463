import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:recallloop/core/services/google_signin_manager.dart';

import '../bloc/auth/auth_bloc.dart';

class GoogleSignInButton extends StatelessWidget {
  const GoogleSignInButton({super.key});

  @override
  Widget build(BuildContext context) {
    // Check if Google Sign-In is initialized
    if (!GoogleSignInManager.instance.isInitialized) {
      String message = 'Google Sign-In not available';
      if (GoogleSignInManager.instance.initializationFailed) {
        message = 'Google Sign-In setup error';
        print(
          'Google Sign-In initialization failed: ${GoogleSignInManager.instance.initializationError}',
        );
      }

      return Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(message, style: const TextStyle(color: Colors.grey)),
        ),
      );
    }

    // Modern Google Sign In button
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        icon: Image.asset('assets/images/google.png', height: 24, width: 24),
        label: Text(
          kIsWeb ? 'Sign in with Google ' : 'Sign in with Google',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        onPressed: () {
          context.read<AuthBloc>().add(const AuthEvent.googleSignInRequested());
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.black87,
          elevation: 0,
          shadowColor: Colors.transparent,
          minimumSize: const Size(double.infinity, 56),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
      ),
    );
  }
}

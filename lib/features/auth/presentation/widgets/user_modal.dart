import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../bloc/auth/auth_bloc.dart';

class UserModal extends StatefulWidget {
  final LoginModel loginUser;
  final VoidCallback? onContinue;

  const UserModal({super.key, required this.loginUser, this.onContinue});

  @override
  State<UserModal> createState() => _UserModalState();
}

class _UserModalState extends State<UserModal> {
  final HiveUserService _hiveUserService = HiveUserService();
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _phoneController;
  late TextEditingController _tenantCodeController;

  Future<void> _saveGoogleModalCompletion({
    required String email,
    required String tenantCode,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (email.isNotEmpty) {
        await prefs.setString('google_login_email', email);
      }
      if (tenantCode.isNotEmpty) {
        await prefs.setString('google_login_tenant_code', tenantCode);
      }
      await prefs.setBool('google_login_completed', true);
      if (mounted) {}
    } catch (e) {}
  }

  @override
  void initState() {
    super.initState();
    final metadata = widget.loginUser.user?.userMetadata;

    // Extract names with fallback to raw OAuth data
    final firstName = _extractNameFromMetadata('firstName', metadata);
    final lastName = _extractNameFromMetadata('lastName', metadata);
    final phone = metadata?.phone ?? '';

    _firstNameController = TextEditingController(text: firstName);
    _lastNameController = TextEditingController(text: lastName);
    _phoneController = TextEditingController(text: phone);
    // Tenant Code is always empty by default here
    _tenantCodeController = TextEditingController(text: '');
  }

  String _extractNameFromMetadata(String field, dynamic metadata) {
    // First try processed metadata
    if (field == 'firstName' && metadata?.firstName?.isNotEmpty == true) {
      return metadata.firstName;
    }
    if (field == 'lastName' && metadata?.lastName?.isNotEmpty == true) {
      return metadata.lastName;
    }

    // Fallback to raw OAuth data from Supabase
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null && currentUser.userMetadata != null) {
        final rawMetadata = currentUser.userMetadata!;

        if (field == 'firstName') {
          return rawMetadata['first_name'] ??
              rawMetadata['given_name'] ??
              _extractFirstNameFromFull(rawMetadata) ??
              '';
        } else if (field == 'lastName') {
          return rawMetadata['last_name'] ??
              rawMetadata['family_name'] ??
              _extractLastNameFromFull(rawMetadata) ??
              '';
        }
      }
    } catch (e) {
      print('Error extracting name from raw metadata: $e');
    }

    return '';
  }

  String? _extractFirstNameFromFull(Map<String, dynamic> rawMetadata) {
    final fullName = rawMetadata['full_name'] ?? rawMetadata['name'];
    if (fullName != null && fullName.toString().isNotEmpty) {
      return fullName.toString().split(' ').first;
    }
    return null;
  }

  String? _extractLastNameFromFull(Map<String, dynamic> rawMetadata) {
    final fullName = rawMetadata['full_name'] ?? rawMetadata['name'];
    if (fullName != null && fullName.toString().isNotEmpty) {
      final parts = fullName.toString().split(' ');
      if (parts.length > 1) {
        return parts.sublist(1).join(' ');
      }
    }
    return null;
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _tenantCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final metadata = widget.loginUser.user?.userMetadata;
    final email = metadata?.email ?? widget.loginUser.user?.email ?? '';

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(maxWidth: 400.w, maxHeight: 900.h),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing24),
                _buildUserInfo(email),
                SizedBox(height: AppTheme.spacing32),
                _buildActionButtons(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          ),
          child: Icon(Icons.edit, size: 40.sp, color: AppTheme.primaryColor),
        ),
        SizedBox(height: AppTheme.spacing16),
        ResponsiveText(
          'Update Profile',
          style: AppTheme.headlineMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          'Edit your profile information and save changes',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserInfo(String email) {
    return Form(
      key: _formKey,
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing20),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // Removed avatar here
            _buildEditableField('First Name', _firstNameController),
            _buildEditableField('Last Name', _lastNameController),
            _buildEditableField('Phone Number', _phoneController),

            // Tenant Code with empty default
            _buildEditableField('Tenant Code', _tenantCodeController),

            _buildInfoRow('Email', email),

            // Removed Role and Provider info rows
          ],
        ),
      ),
    );
  }

  Widget _buildEditableField(String label, TextEditingController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            label,
            style: AppTheme.labelMedium.copyWith(
              color: AppTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: AppTheme.spacing4),
          TextFormField(
            controller: controller,
            keyboardType: label == 'Phone Number'
                ? TextInputType.number
                : TextInputType.text,
            inputFormatters: label == 'Phone Number'
                ? [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                  ]
                : null,
            validator: (value) {
              if (label.contains('Name') && (value?.trim().isEmpty ?? true)) {
                return '$label is required';
              }
              if (label == 'Phone Number') {
                if (value == null || value.trim().isEmpty) {
                  return 'Phone number is required';
                }
                if (value.trim().length != 10) {
                  return 'Phone number must be exactly 10 digits';
                }
              }
              return null;
            },
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
            decoration: InputDecoration(
              filled: true,
              fillColor: AppTheme.surfaceColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                borderSide: BorderSide(
                  color: AppTheme.primaryColor.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppTheme.spacing12,
                vertical: AppTheme.spacing12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: ResponsiveText(
              '$label:',
              style: AppTheme.labelMedium.copyWith(
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: AppTheme.spacing8),
          Expanded(
            child: ResponsiveText(
              value,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState?.validate() ?? false) {
                // Update user metadata via Bloc
                context.read<AuthBloc>().add(
                  AuthEvent.updateUserMetadata(
                    firstName: _firstNameController.text.trim(),
                    lastName: _lastNameController.text.trim(),
                    phone: _phoneController.text.trim(),
                    tenantCode: _tenantCodeController.text.trim(),
                  ),
                );

                // Update local Hive user model
                final updatedLoginModel = widget.loginUser.copyWith(
                  user: widget.loginUser.user?.copyWith(
                    userMetadata: widget.loginUser.user?.userMetadata?.copyWith(
                      firstName: _firstNameController.text.trim(),
                      lastName: _lastNameController.text.trim(),
                      phone: _phoneController.text.trim(),
                      tenantCode: _tenantCodeController.text.trim(),
                    ),
                  ),
                );
                await _hiveUserService.saveUserAfterLogin(updatedLoginModel);

                // Save Google modal completion
                await _saveGoogleModalCompletion(
                  email:
                      (widget.loginUser.user?.userMetadata?.email ??
                      widget.loginUser.user?.email ??
                      ''),
                  tenantCode: _tenantCodeController.text.trim(),
                );
                Navigator.of(context).pop();
                if (widget.onContinue != null) {
                  widget.onContinue!();
                } else {
                  context.go(AppRouter.home);
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
              foregroundColor: AppTheme.primaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              elevation: 0,
            ),
            child: ResponsiveText(
              'Save Changes',
              style: AppTheme.labelLarge.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
        ),

        SizedBox(height: AppTheme.spacing12),

        // SizedBox(
        //   width: double.infinity,
        //   child: ElevatedButton(
        //     onPressed: () {
        //       _saveGoogleModalCompletion(
        //         email:
        //             (widget.loginUser.user?.userMetadata?.email ??
        //             widget.loginUser.user?.email ??
        //             ''),
        //         tenantCode: _tenantCodeController.text.trim(),
        //       );
        //       Navigator.of(context).pop();
        //       if (widget.onContinue != null) {
        //         widget.onContinue!();
        //       } else {
        //         context.go(AppRouter.home);
        //       }
        //     },
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: AppTheme.primaryColor,
        //       foregroundColor: AppTheme.textOnPrimary,
        //       padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        //       ),
        //       elevation: 0,
        //     ),
        //     child: ResponsiveText(
        //       'Continue to RecallLoop',
        //       style: AppTheme.labelLarge.copyWith(fontWeight: FontWeight.w600),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}

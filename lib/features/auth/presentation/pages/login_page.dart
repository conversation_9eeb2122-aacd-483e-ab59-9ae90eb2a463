import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../bloc/auth/auth_bloc.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_field.dart';
import '../widgets/google_sign_in_button.dart';
import '../widgets/user_modal.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _tenantController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadStoredData();
  }

  // Load stored data from SharedPreferences (tenant code functionality disabled)
  Future<void> _loadStoredData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // final storedTenantCode = prefs.getString('tenant_code');
      // final storedEmail = prefs.getString('email');

      if (mounted) {
        setState(() {
          // Tenant code functionality disabled
          // if (storedTenantCode != null && storedTenantCode.isNotEmpty) {
          //   _tenantController.text = storedTenantCode;
          // }
          // Optionally prefill email as well
          // if (storedEmail != null && storedEmail.isNotEmpty) {
          //   _emailController.text = storedEmail;
          //   _rememberMe = true; // Set remember me if email was stored
          // }
        });
      }
    } catch (e) {
      print('Error loading stored data: $e');
    }
  }

  // Store tenant code (functionality disabled)
  Future<void> _storeDataIfNeeded() async {
    try {
      // final prefs = await SharedPreferences.getInstance();

      // Tenant code functionality disabled
      // if (_tenantController.text.trim().isNotEmpty) {
      //   await prefs.setString('tenant_code', _tenantController.text.trim());
      // }
    } catch (e) {
      print('Error storing data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildBackgroundGradient(),
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            state.whenOrNull(
              authenticated: (user) async {
                print(user);
                // Store data before navigating
                await _storeDataIfNeeded();
                context.go(AppRouter.home);
              },
              showUserModal: (user) async {
                print('Showing user modal for Google OAuth user: $user');

                await _storeDataIfNeeded();

                try {
                  final prefs = await SharedPreferences.getInstance();
                  final completed =
                      prefs.getBool('google_login_completed') ?? false;
                  final savedEmail =
                      (prefs.getString('google_login_email') ?? '').trim();
                  final currentEmail =
                      (user.user?.userMetadata?.email ?? user.user?.email ?? '')
                          .trim();

                  if (completed &&
                      savedEmail.isNotEmpty &&
                      currentEmail.isNotEmpty &&
                      savedEmail.toLowerCase() == currentEmail.toLowerCase()) {
                    context.go(AppRouter.home);
                    return;
                  }
                } catch (_) {}

                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => UserModal(
                    loginUser: user,
                    onContinue: () {
                      context.go(AppRouter.home);
                    },
                  ),
                );
              },
              error: (message) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              },
              unauthenticated: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Invalid credentials'),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              },
            );
          },
          child: ResponsiveLayout(
            mobile: _buildMobileLayout(),
            tablet: _buildTabletLayout(),
            desktop: _buildDesktopLayout(),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFF8FAFC), // Light gray-blue
          Color(0xFFE2E8F0), // Slightly darker gray-blue
          Color(0xFFF1F5F9), // Very light blue
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(height: AppTheme.spacing24),
            _buildHeader(),
            SizedBox(height: AppTheme.spacing48),
            // Modern card container for form
            Container(
              padding: EdgeInsets.all(AppTheme.spacing32),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  _buildLoginForm(),
                  SizedBox(height: AppTheme.spacing24),
                  GoogleSignInButton(),
                ],
              ),
            ),
            SizedBox(height: AppTheme.spacing32),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: SingleChildScrollView(
          child: ResponsiveContainer(
            maxWidth: 500.w,
            padding: EdgeInsets.all(AppTheme.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing48),
                // Modern card container for form
                Container(
                  padding: EdgeInsets.all(AppTheme.spacing40),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.95),
                    borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
                    border: Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 25,
                        offset: const Offset(0, 15),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      _buildLoginForm(),
                      SizedBox(height: AppTheme.spacing24),
                      GoogleSignInButton(),
                    ],
                  ),
                ),
                SizedBox(height: AppTheme.spacing32),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Row(
        children: [
          // Left side - Branding
          Expanded(
            child: Container(
              color: AppTheme.primaryColor,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      width: 100.w,
                      height: 100.h,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: AppTheme.spacing24),
                    ResponsiveText(
                      'RecallLoop',
                      style: AppTheme.displayLarge.copyWith(
                        color: AppTheme.textOnPrimary,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    SizedBox(height: AppTheme.spacing16),
                    ResponsiveText(
                      'Enhance your cognitive abilities',
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textOnPrimary.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Login form
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: ResponsiveContainer(
                  maxWidth: 400.w,
                  padding: EdgeInsets.all(AppTheme.spacing40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(showLogo: false),
                      SizedBox(height: AppTheme.spacing48),
                      _buildLoginForm(),
                      SizedBox(height: AppTheme.spacing24),
                      GoogleSignInButton(),
                      SizedBox(height: AppTheme.spacing24),
                      _buildFooter(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader({bool showLogo = true}) {
    return Column(
      children: [
        if (showLogo) ...[
          // Modern glassmorphism logo container
          Container(
            width: 100.w,
            height: 100.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.15),
                  AppTheme.secondaryColor.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Container(
              margin: EdgeInsets.all(AppTheme.spacing16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/logo.png',
                  width: 40,
                  height: 40,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          SizedBox(height: AppTheme.spacing32),
        ],
        // Enhanced title with gradient text effect
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          ).createShader(bounds),
          child: ResponsiveText(
            'Welcome Back',
            style: AppTheme.displayMedium.copyWith(
              fontWeight: FontWeight.w800,
              color: Colors.white,
              letterSpacing: -0.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        ResponsiveText(
          'Sign in to continue your cognitive journey',
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textSecondary,
            height: 1.5,
            fontSize: 16.sp,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.maybeWhen(
          loading: () => true,
          orElse: () => false,
        );

        return Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AuthFormField(
                controller: _emailController,
                label: 'Email',
                hintText: 'Enter your email',
                keyboardType: TextInputType.emailAddress,
                prefixIcon: Icons.email_outlined,
                enabled: !isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _passwordController,
                label: 'Password',
                hintText: 'Enter your password',
                obscureText: _obscurePassword,
                prefixIcon: Icons.lock_outline,
                enabled: !isLoading,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                    color: AppTheme.textTertiary,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),

              // Tenant Code field hidden
              // SizedBox(height: AppTheme.spacing16),
              // // Enhanced Tenant Code field with prefilled data indicator
              // Container(
              //   decoration: BoxDecoration(
              //     // border: Border.all(
              //     //   color: _tenantController.text.isNotEmpty
              //     //       ? AppTheme.primaryColor.withOpacity(0.3)
              //     //       : Colors.transparent,
              //     //   width: 1,
              //     // ),
              //     borderRadius: BorderRadius.circular(8),
              //   ),
              //   child: AuthFormField(
              //     controller: _tenantController,
              //     label: 'Tenant Code (Optional)',
              //     hintText: _tenantController.text.isEmpty
              //         ? 'Enter your Tenant Code'
              //         : 'Previously used tenant code',
              //     prefixIcon: Icons.business_outlined,
              //     enabled: !isLoading,
              //     suffixIcon: _tenantController.text.isNotEmpty
              //         ? Row(
              //             mainAxisSize: MainAxisSize.min,
              //             children: [
              //               Icon(
              //                 Icons.check_circle_outline,
              //                 color: AppTheme.primaryColor,
              //                 size: 20,
              //               ),
              //               IconButton(
              //                 icon: Icon(
              //                   Icons.clear,
              //                   color: AppTheme.textTertiary,
              //                   size: 20,
              //                 ),
              //                 onPressed: () {
              //                   setState(() {
              //                     _tenantController.clear();
              //                   });
              //                 },
              //               ),
              //             ],
              //           )
              //         : null,
              //   ),
              // ),
              //
              // if (_tenantController.text.isNotEmpty)
              //   Padding(
              //     padding: EdgeInsets.only(top: 4.h, left: 12.w),
              //     child: Text(
              //       'Using previously saved tenant code',
              //       style: AppTheme.labelSmall.copyWith(
              //         color: AppTheme.primaryColor,
              //         fontSize: 11.sp,
              //       ),
              //     ),
              //   ),
              SizedBox(height: AppTheme.spacing16),
              Row(
                children: [
                  Checkbox(
                    value: _rememberMe,
                    onChanged: isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _rememberMe = value ?? false;
                            });
                          },
                    activeColor: AppTheme.primaryColor,
                  ),
                  Expanded(
                    child: ResponsiveText(
                      'Remember me',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: isLoading
                        ? null
                        : () {
                            // TODO: Implement forgot password
                          },
                    child: ResponsiveText(
                      'Forgot Password?',
                      style: AppTheme.labelMedium.copyWith(
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing32),
              AuthButton(
                text: 'Sign In',
                isLoading: isLoading,
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    context.read<AuthBloc>().add(
                      AuthEvent.loginRequested(
                        email: _emailController.text.trim(),
                        password: _passwordController.text,
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: Divider(color: AppTheme.dividerColor)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
              child: ResponsiveText(
                'OR',
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
            ),
            Expanded(child: Divider(color: AppTheme.dividerColor)),
          ],
        ),
        SizedBox(height: AppTheme.spacing24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveText(
              "Don't have an account? ",
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () {
                context.go(AppRouter.signup);
              },
              child: ResponsiveText(
                'Sign Up',
                style: AppTheme.labelMedium.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _tenantController.dispose();
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart'; // ✅ add this

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingItem> _onboardingItems = [
    OnboardingItem(
      title: 'Welcome to RecallLoop',
      description:
          'Enhance your cognitive abilities with personalized brain training exercises designed by experts.',
      asset: 'assets/images/logo.png', // ✅ now using image
    ),
    OnboardingItem(
      title: 'Track Your Progress',
      description:
          'Monitor your cognitive improvement with detailed analytics and personalized insights.',
      asset: 'assets/images/logo.png', // ✅ animation
    ),
    OnboardingItem(
      title: 'Mood & Wellness',
      description:
          'Keep track of your emotional well-being and see how it correlates with your cognitive performance.',
      asset: 'assets/images/logo.png',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildBackgroundGradient(),
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0), Color(0xFFF1F5F9)],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Column(
        children: [
          SizedBox(height: AppTheme.spacing24),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() => _currentPage = index);
              },
              itemCount: _onboardingItems.length,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _pageController,
                  builder: (context, child) {
                    return _buildOnboardingItem(_onboardingItems[index]);
                  },
                );
              },
            ),
          ),
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() => _currentPage = index);
                },
                itemCount: _onboardingItems.length,
                itemBuilder: (context, index) {
                  return AnimatedBuilder(
                    animation: _pageController,
                    builder: (context, child) {
                      return _buildOnboardingItem(_onboardingItems[index]);
                    },
                  );
                },
              ),
            ),
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
          child: Column(
            children: [
              SizedBox(height: AppTheme.spacing32),
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() => _currentPage = index);
                  },
                  itemCount: _onboardingItems.length,
                  itemBuilder: (context, index) {
                    return AnimatedBuilder(
                      animation: _pageController,
                      builder: (context, child) {
                        return _buildOnboardingItem(_onboardingItems[index]);
                      },
                    );
                  },
                ),
              ),
              _buildBottomSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOnboardingItem(OnboardingItem item) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppTheme.spacing24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: AppTheme.spacing24),
            Container(
              width: 280.w,
              height: 280.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.15),
                    AppTheme.secondaryColor.withValues(alpha: 0.1),
                    AppTheme.accentColor.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(AppTheme.radiusXLarge * 2),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned.fill(
                    child: CustomPaint(painter: _PatternPainter()),
                  ),
                  Container(
                    padding: EdgeInsets.all(AppTheme.spacing32),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryColor.withValues(alpha: 0.2),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: _buildAssetWidget(item.asset), // ✅ updated
                  ),
                ],
              ),
            ),
            SizedBox(height: AppTheme.spacing32),
            ShaderMask(
              shaderCallback: (bounds) => LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
              ).createShader(bounds),
              child: Text(
                item.title,
                style: AppTheme.displayMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w800,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: AppTheme.spacing16),
            Container(
              constraints: BoxConstraints(maxWidth: 400.w),
              child: Text(
                item.description,
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.7,
                  fontSize: 16.sp,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: AppTheme.spacing24),
          ],
        ),
      ),
    );
  }

  /// ✅ Handle image / Lottie / fallback icon
  Widget _buildAssetWidget(String asset) {
    if (asset.endsWith('.png') ||
        asset.endsWith('.jpg') ||
        asset.endsWith('.jpeg')) {
      return Image.asset(
        asset,
        width: 120.w,
        height: 120.h,
        fit: BoxFit.contain,
      );
    } else if (asset.endsWith('.json')) {
      return Lottie.asset(
        asset,
        width: 120.w,
        height: 120.h,
        fit: BoxFit.contain,
      );
    }
    return Icon(
      _getIconForAnimation(asset),
      size: 80.sp,
      color: AppTheme.primaryColor,
    );
  }

  Widget _buildBottomSection() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusXLarge * 1.5),
          topRight: Radius.circular(AppTheme.radiusXLarge * 1.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _onboardingItems.length,
              (index) => _buildModernPageIndicator(index),
            ),
          ),
          SizedBox(height: AppTheme.spacing24),
          Container(
            width: double.infinity,
            height: 60.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {
                if (_currentPage < _onboardingItems.length - 1) {
                  _pageController.nextPage(
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeInOutCubic,
                  );
                } else {
                  context.go(AppRouter.login);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                elevation: 0,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _currentPage < _onboardingItems.length - 1
                        ? 'Continue'
                        : 'Get Started',
                    style: AppTheme.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18.sp,
                    ),
                  ),
                  SizedBox(width: AppTheme.spacing8),
                  Icon(
                    _currentPage < _onboardingItems.length - 1
                        ? Icons.arrow_forward_rounded
                        : Icons.rocket_launch_rounded,
                    color: Colors.white,
                    size: 20.sp,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          TextButton(
            onPressed: () {
              context.go(AppRouter.login);
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: AppTheme.spacing24,
                vertical: AppTheme.spacing12,
              ),
            ),
            child: Text(
              'Skip for now',
              style: AppTheme.labelLarge.copyWith(
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernPageIndicator(int index) {
    final isActive = _currentPage == index;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOutCubic,
      margin: EdgeInsets.symmetric(horizontal: 6.w),
      width: isActive ? 32.w : 12.w,
      height: 12.h,
      decoration: BoxDecoration(
        gradient: isActive
            ? LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
              )
            : null,
        color: isActive ? null : AppTheme.primaryColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6.r),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.4),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
    );
  }

  IconData _getIconForAnimation(String asset) {
    if (asset.contains('brain')) {
      return Icons.psychology;
    } else if (asset.contains('progress')) {
      return Icons.trending_up;
    } else if (asset.contains('mood')) {
      return Icons.mood;
    }
    return Icons.lightbulb;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class OnboardingItem {
  final String title;
  final String description;
  final String asset;

  OnboardingItem({
    required this.title,
    required this.description,
    required this.asset,
  });
}

class _PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryColor.withValues(alpha: 0.05)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final spacing = 40.0;
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(
          Offset(x, y),
          3.0,
          paint..color = AppTheme.primaryColor.withValues(alpha: 0.08),
        );

        if (x + spacing < size.width) {
          canvas.drawLine(
            Offset(x + 3, y),
            Offset(x + spacing - 3, y),
            paint..color = AppTheme.primaryColor.withValues(alpha: 0.03),
          );
        }
        if (y + spacing < size.height) {
          canvas.drawLine(
            Offset(x, y + 3),
            Offset(x, y + spacing - 3),
            paint..color = AppTheme.primaryColor.withValues(alpha: 0.03),
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

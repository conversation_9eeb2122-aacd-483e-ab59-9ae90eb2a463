import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../bloc/auth/auth_bloc.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_field.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _tenantController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  CountryCode? _selectedCountryCode;

  @override
  void initState() {
    super.initState();
    _loadStoredData();
  }

  // Load stored data from SharedPreferences and prefill tenant code
  Future<void> _loadStoredData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedTenantCode = prefs.getString('tenant_code');

      if (mounted && storedTenantCode != null && storedTenantCode.isNotEmpty) {
        setState(() {
          _tenantController.text = storedTenantCode;
        });
      }
    } catch (e) {
      print('Error loading stored data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildBackgroundGradient(),
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            state.whenOrNull(
              signUpSuccess: (signupUser) async {
                print(signupUser);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Account created successfully! Please check your email to verify your account.',
                    ),
                    backgroundColor: AppTheme.successColor,
                    duration: Duration(seconds: 4),
                  ),
                );
                // Navigate to login page after successful signup
                context.go(AppRouter.login);
              },
              signUpError: (message) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              },
              error: (message) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              },
            );
          },
          child: ResponsiveLayout(
            mobile: _buildMobileLayout(),
            tablet: _buildTabletLayout(),
            desktop: _buildDesktopLayout(),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFF8FAFC), // Light gray-blue
          Color(0xFFE2E8F0), // Slightly darker gray-blue
          Color(0xFFF1F5F9), // Very light blue
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(height: AppTheme.spacing24),
            _buildHeader(),
            SizedBox(height: AppTheme.spacing32),
            // Modern card container for form
            Container(
              padding: EdgeInsets.all(AppTheme.spacing32),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: _buildSignupForm(),
            ),
            SizedBox(height: AppTheme.spacing32),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: SingleChildScrollView(
          child: ResponsiveContainer(
            maxWidth: 500.w,
            padding: EdgeInsets.all(AppTheme.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing32),
                // Modern card container for form
                Container(
                  padding: EdgeInsets.all(AppTheme.spacing40),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.95),
                    borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
                    border: Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 25,
                        offset: const Offset(0, 15),
                      ),
                    ],
                  ),
                  child: _buildSignupForm(),
                ),
                SizedBox(height: AppTheme.spacing32),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Row(
        children: [
          // Left side - Branding
          Expanded(
            child: Container(
              color: AppTheme.primaryColor,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      width: 100.w,
                      height: 100.h,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: AppTheme.spacing24),
                    ResponsiveText(
                      'Join RecallLoop',
                      style: AppTheme.displayLarge.copyWith(
                        color: AppTheme.textOnPrimary,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    SizedBox(height: AppTheme.spacing16),
                    ResponsiveText(
                      'Start your cognitive enhancement journey',
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textOnPrimary.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Signup form
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: ResponsiveContainer(
                  maxWidth: 400.w,
                  padding: EdgeInsets.all(AppTheme.spacing40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(showLogo: false),
                      SizedBox(height: AppTheme.spacing32),
                      _buildSignupForm(),
                      SizedBox(height: AppTheme.spacing24),
                      _buildFooter(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader({bool showLogo = true}) {
    return Column(
      children: [
        if (showLogo) ...[
          // Modern glassmorphism logo container
          Container(
            width: 100.w,
            height: 100.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.15),
                  AppTheme.secondaryColor.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Container(
              margin: EdgeInsets.all(AppTheme.spacing16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(
                Icons.psychology,
                size: 40.sp,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          SizedBox(height: AppTheme.spacing32),
        ],
        // Enhanced title with gradient text effect
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          ).createShader(bounds),
          child: ResponsiveText(
            'Create Account',
            style: AppTheme.displayMedium.copyWith(
              fontWeight: FontWeight.w800,
              color: Colors.white,
              letterSpacing: -0.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        ResponsiveText(
          'Join thousands improving their cognitive abilities',
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textSecondary,
            height: 1.5,
            fontSize: 16.sp,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignupForm() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.maybeWhen(
          loading: () => true,
          orElse: () => false,
        );

        return Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AuthFormField(
                controller: _nameController,
                label: 'First Name',
                hintText: 'Enter your first name',
                prefixIcon: Icons.person_outline,
                enabled: !isLoading,
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your first name';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _lastNameController,
                label: 'Last Name',
                hintText: 'Enter your last name',
                prefixIcon: Icons.person_outline,
                enabled: !isLoading,
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your last name';
                  }
                  if (value.trim().isEmpty) {
                    return 'Name must be at least 1 character';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _emailController,
                label: 'Email',
                hintText: 'Enter your email',
                keyboardType: TextInputType.emailAddress,
                prefixIcon: Icons.email_outlined,
                enabled: !isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _usernameController,
                label: 'Address',
                hintText: 'Enter address',
                prefixIcon: Icons.location_on_outlined,
                enabled: !isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an address';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthPhoneField(
                controller: _phoneController,
                label: 'Phone Number',
                hintText: 'Enter your phone number',
                enabled: !isLoading,
                initialCountryCode: _selectedCountryCode,
                onCountryChanged: (countryCode) {
                  setState(() {
                    _selectedCountryCode = countryCode;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your phone number';
                  }
                  // Remove any non-digit characters for validation
                  String digitsOnly = value.replaceAll(RegExp(r'[^0-9]'), '');
                  if (digitsOnly.length != 10) {
                    return 'Phone number must be exactly 10 digits';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _passwordController,
                label: 'Password',
                hintText: 'Create a strong password',
                obscureText: _obscurePassword,
                prefixIcon: Icons.lock_outline,
                enabled: !isLoading,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                    color: AppTheme.textTertiary,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a password';
                  }
                  if (value.length < 8) {
                    return 'Password must be at least 8 characters';
                  }
                  if (!RegExp(
                    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)',
                  ).hasMatch(value)) {
                    return 'Password must contain uppercase, lowercase, and number';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _confirmPasswordController,
                label: 'Confirm Password',
                hintText: 'Confirm your password',
                obscureText: _obscureConfirmPassword,
                prefixIcon: Icons.lock_outline,
                enabled: !isLoading,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureConfirmPassword
                        ? Icons.visibility
                        : Icons.visibility_off,
                    color: AppTheme.textTertiary,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureConfirmPassword = !_obscureConfirmPassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm your password';
                  }
                  if (value != _passwordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              // Enhanced Tenant Code field with prefilled data indicator
              Container(
                decoration: BoxDecoration(
                  // border: Border.all(
                  //   color: _tenantController.text.isNotEmpty
                  //       ? AppTheme.primaryColor.withOpacity(0.3)
                  //       : Colors.transparent,
                  //   width: 1,
                  // ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: AuthFormField(
                  controller: _tenantController,
                  label: 'Tenant Code ',
                  hintText: _tenantController.text.isEmpty
                      ? 'Enter your tenant code'
                      : 'Previously used tenant code',
                  prefixIcon: Icons.business_outlined,
                  enabled: !isLoading,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your tenant code';
                    }
                    return null;
                  },
                  suffixIcon: _tenantController.text.isNotEmpty
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: AppTheme.textTertiary,
                                size: 20,
                              ),
                              onPressed: () {
                                setState(() {
                                  _tenantController.clear();
                                });
                              },
                            ),
                          ],
                        )
                      : null,
                ),
              ),

              if (_tenantController.text.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 4.h, left: 12.w),
                  child: Text(
                    'Using previously saved tenant code',
                    style: AppTheme.labelSmall.copyWith(
                      color: AppTheme.primaryColor,
                      fontSize: 11.sp,
                    ),
                  ),
                ),

              SizedBox(height: AppTheme.spacing16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Checkbox(
                    value: _agreeToTerms,
                    onChanged: isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _agreeToTerms = value ?? false;
                            });
                          },
                    activeColor: AppTheme.primaryColor,
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(top: 12.h),
                      child: RichText(
                        text: TextSpan(
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                          children: [
                            const TextSpan(text: 'I agree to the '),
                            TextSpan(
                              text: 'Terms of Service',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            const TextSpan(text: ' and '),
                            TextSpan(
                              text: 'Privacy Policy',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing32),
              AuthButton(
                text: 'Create Account',
                isLoading: isLoading,
                onPressed: _agreeToTerms
                    ? () {
                        if (_formKey.currentState!.validate()) {
                          context.read<AuthBloc>().add(
                            AuthEvent.signUpRequested(
                              firstName: _nameController.text.trim(),
                              lastName: _lastNameController.text.trim(),
                              email: _emailController.text.trim(),
                              address: _usernameController.text.trim(),
                              phone: _selectedCountryCode != null
                                  ? '${_selectedCountryCode!.dialCode}${_phoneController.text.trim()}'
                                  : _phoneController.text.trim(),
                              password: _passwordController.text,
                              tenantCode:
                                  _tenantController.text.trim().isNotEmpty
                                  ? _tenantController.text.trim()
                                  : 'TN-0001',
                            ),
                          );
                        }
                      }
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: Divider(color: AppTheme.dividerColor)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
              child: ResponsiveText(
                'OR',
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
            ),
            Expanded(child: Divider(color: AppTheme.dividerColor)),
          ],
        ),
        SizedBox(height: AppTheme.spacing24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveText(
              'Already have an account? ',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () {
                context.go(AppRouter.login);
              },
              child: ResponsiveText(
                'Sign In',
                style: AppTheme.labelMedium.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _tenantController.dispose();
    super.dispose();
  }
}
